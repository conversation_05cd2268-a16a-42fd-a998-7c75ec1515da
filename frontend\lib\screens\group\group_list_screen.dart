import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../models/group.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/slide_animation.dart';
import '../../widgets/loading_animation.dart';
import 'create_group_screen.dart';
import 'group_chat_screen.dart';

class GroupListScreen extends StatefulWidget {
  const GroupListScreen({super.key});

  @override
  State<GroupListScreen> createState() => _GroupListScreenState();
}

class _GroupListScreenState extends State<GroupListScreen> {
  List<Group> _groups = [];
  bool _isLoading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadGroups();
  }

  Future<void> _loadGroups() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      // 模拟加载群聊列表
      await Future.delayed(const Duration(seconds: 1));
      
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;
      
      if (currentUser != null) {
        _groups = [
          Group(
            id: 1,
            name: '工作讨论组',
            description: '日常工作交流和讨论',
            avatar: null,
            ownerId: currentUser.id,
            members: [
              GroupMember(
                userId: currentUser.id,
                nickname: currentUser.nickname,
                role: GroupRole.owner,
                joinedAt: DateTime.now().subtract(const Duration(days: 30)),
              ),
              GroupMember(
                userId: 2,
                nickname: 'Alice Wonder',
                role: GroupRole.admin,
                joinedAt: DateTime.now().subtract(const Duration(days: 25)),
              ),
              GroupMember(
                userId: 3,
                nickname: 'Bob Builder',
                role: GroupRole.member,
                joinedAt: DateTime.now().subtract(const Duration(days: 20)),
              ),
            ],
            settings: GroupSettings(
              allowMemberInvite: true,
              joinPolicy: GroupJoinPolicy.inviteOnly,
            ),
            createdAt: DateTime.now().subtract(const Duration(days: 30)),
            updatedAt: DateTime.now(),
          ),
          Group(
            id: 2,
            name: '朋友聚会',
            description: '周末聚会安排',
            avatar: null,
            ownerId: 2,
            members: [
              GroupMember(
                userId: currentUser.id,
                nickname: currentUser.nickname,
                role: GroupRole.member,
                joinedAt: DateTime.now().subtract(const Duration(days: 15)),
              ),
              GroupMember(
                userId: 2,
                nickname: 'Alice Wonder',
                role: GroupRole.owner,
                joinedAt: DateTime.now().subtract(const Duration(days: 20)),
              ),
              GroupMember(
                userId: 4,
                nickname: 'Charlie Brown',
                role: GroupRole.member,
                joinedAt: DateTime.now().subtract(const Duration(days: 10)),
              ),
            ],
            settings: GroupSettings(
              allowMemberInvite: false,
              joinPolicy: GroupJoinPolicy.inviteOnly,
            ),
            createdAt: DateTime.now().subtract(const Duration(days: 20)),
            updatedAt: DateTime.now(),
          ),
        ];
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = '加载群聊列表失败: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _createGroup() async {
    final result = await Navigator.of(context).push<Group>(
      MaterialPageRoute(
        builder: (context) => const CreateGroupScreen(),
      ),
    );

    if (result != null) {
      setState(() {
        _groups.insert(0, result);
      });
    }
  }

  void _openGroupChat(Group group) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => GroupChatScreen(group: group),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('群聊'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.group_add),
            onPressed: _createGroup,
            tooltip: '创建群聊',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingAnimation(
          size: 60,
          message: '加载群聊列表中...',
        ),
      );
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _error,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadGroups,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_groups.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.group_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '暂无群聊',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击右上角创建群聊',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createGroup,
              icon: const Icon(Icons.group_add),
              label: const Text('创建群聊'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadGroups,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _groups.length,
        itemBuilder: (context, index) {
          final group = _groups[index];
          return ListItemAnimation(
            index: index,
            child: _buildGroupItem(group),
          );
        },
      ),
    );
  }

  Widget _buildGroupItem(Group group) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    final isOwner = currentUser != null && group.isOwner(currentUser.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppConstants.primaryColor.withValues(alpha: 0.1),
            border: Border.all(
              color: AppConstants.primaryColor.withValues(alpha: 0.3),
            ),
          ),
          child: group.avatar != null
              ? ClipOval(
                  child: Image.asset(
                    group.avatar!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.group,
                        color: AppConstants.primaryColor,
                        size: 24,
                      );
                    },
                  ),
                )
              : const Icon(
                  Icons.group,
                  color: AppConstants.primaryColor,
                  size: 24,
                ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                group.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (isOwner)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '群主',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.amber,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (group.description != null) ...[
              const SizedBox(height: 4),
              Text(
                group.description!,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 4),
            Text(
              '${group.memberCount} 名成员',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: const Icon(
          Icons.chevron_right,
          color: Colors.grey,
        ),
        onTap: () => _openGroupChat(group),
      ),
    );
  }
}
