import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class CameraInterface extends StatefulWidget {
  final bool isVideoMode;
  final Function(XFile) onMediaCaptured;

  const CameraInterface({
    Key? key,
    required this.isVideoMode,
    required this.onMediaCaptured,
  }) : super(key: key);

  @override
  State<CameraInterface> createState() => _CameraInterfaceState();
}

class _CameraInterfaceState extends State<CameraInterface>
    with TickerProviderStateMixin {
  final ImagePicker _imagePicker = ImagePicker();
  bool _isCapturing = false;
  bool _isRecording = false;
  late AnimationController _captureAnimationController;
  late AnimationController _recordingAnimationController;
  late Animation<double> _captureAnimation;
  late Animation<double> _recordingAnimation;

  @override
  void initState() {
    super.initState();
    _captureAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _recordingAnimationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _captureAnimation = Tween<double>(begin: 1.0, end: 0.8).animate(
      CurvedAnimation(parent: _captureAnimationController, curve: Curves.easeInOut),
    );
    _recordingAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _recordingAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _captureAnimationController.dispose();
    _recordingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white, size: 28),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.isVideoMode ? '录像' : '拍照',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              widget.isVideoMode ? Icons.camera_alt : Icons.videocam,
              color: Colors.white,
              size: 28,
            ),
            onPressed: _switchMode,
          ),
        ],
      ),
      body: Stack(
        children: [
          // 相机预览区域（模拟）
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF2C3E50),
                  Color(0xFF34495E),
                  Color(0xFF2C3E50),
                ],
              ),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.camera_alt_outlined,
                    size: 80,
                    color: Colors.white54,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '相机预览',
                    style: TextStyle(
                      color: Colors.white54,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 底部控制栏
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // 相册按钮
                  _buildControlButton(
                    icon: Icons.photo_library,
                    label: '相册',
                    onTap: _openGallery,
                  ),

                  // 拍摄/录制按钮
                  _buildCaptureButton(),

                  // 切换按钮
                  _buildControlButton(
                    icon: widget.isVideoMode ? Icons.camera_alt : Icons.videocam,
                    label: widget.isVideoMode ? '拍照' : '录像',
                    onTap: _switchMode,
                  ),
                ],
              ),
            ),
          ),

          // 录制状态指示
          if (_isRecording)
            Positioned(
              top: 100,
              left: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    const Text(
                      '录制中...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaptureButton() {
    return GestureDetector(
      onTap: widget.isVideoMode ? null : _capturePhoto,
      onLongPressStart: widget.isVideoMode ? (_) => _startRecording() : null,
      onLongPressEnd: widget.isVideoMode ? (_) => _stopRecording() : null,
      child: AnimatedBuilder(
        animation: widget.isVideoMode ? _recordingAnimation : _captureAnimation,
        builder: (context, child) {
          final scale = widget.isVideoMode && _isRecording
              ? _recordingAnimation.value
              : !widget.isVideoMode && _isCapturing
                  ? _captureAnimation.value
                  : 1.0;

          return Transform.scale(
            scale: scale,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: widget.isVideoMode
                    ? (_isRecording ? Colors.red : Colors.red.withValues(alpha: 0.8))
                    : Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 4,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                widget.isVideoMode
                    ? (_isRecording ? Icons.stop : Icons.videocam)
                    : Icons.camera_alt,
                color: widget.isVideoMode
                    ? Colors.white
                    : Colors.black87,
                size: 32,
              ),
            ),
          );
        },
      ),
    );
  }

  void _capturePhoto() async {
    if (_isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    _captureAnimationController.forward().then((_) {
      _captureAnimationController.reverse();
    });

    try {
      final XFile? photo = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (photo != null && mounted) {
        widget.onMediaCaptured(photo);
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('拍照失败: ${e.toString()}'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  void _startRecording() {
    setState(() {
      _isRecording = true;
    });
    _recordingAnimationController.repeat(reverse: true);
  }

  void _stopRecording() async {
    setState(() {
      _isRecording = false;
    });
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();

    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null && mounted) {
        widget.onMediaCaptured(video);
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('录制失败: ${e.toString()}'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _openGallery() async {
    try {
      final XFile? media = widget.isVideoMode
          ? await _imagePicker.pickVideo(source: ImageSource.gallery)
          : await _imagePicker.pickImage(source: ImageSource.gallery);

      if (media != null && mounted) {
        widget.onMediaCaptured(media);
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('选择失败: ${e.toString()}'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _switchMode() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => CameraInterface(
          isVideoMode: !widget.isVideoMode,
          onMediaCaptured: widget.onMediaCaptured,
        ),
      ),
    );
  }
}
