import 'dart:async';
import '../models/message.dart';
import '../models/user.dart';
import '../models/group.dart';

/// 消息转发服务
/// 处理消息转发、引用回复等功能
class MessageForwardService {
  static final MessageForwardService _instance = MessageForwardService._internal();
  factory MessageForwardService() => _instance;
  MessageForwardService._internal();

  // 事件流
  final StreamController<MessageForwardEvent> _eventController = 
      StreamController<MessageForwardEvent>.broadcast();
  
  Stream<MessageForwardEvent> get eventStream => _eventController.stream;

  /// 转发消息给用户
  Future<bool> forwardToUser(Message message, User targetUser) async {
    try {
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 500));

      // 创建转发消息
      final forwardedMessage = _createForwardedMessage(message, targetUser.id);

      _eventController.add(MessageForwardEvent(
        type: MessageForwardEventType.messageForwarded,
        originalMessage: message,
        forwardedMessage: forwardedMessage,
        targetUser: targetUser,
      ));

      return true;
    } catch (e) {
      _eventController.add(MessageForwardEvent(
        type: MessageForwardEventType.forwardFailed,
        originalMessage: message,
        error: e.toString(),
      ));
      return false;
    }
  }

  /// 转发消息给群组
  Future<bool> forwardToGroup(Message message, Group targetGroup) async {
    try {
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 500));

      // 创建转发消息
      final forwardedMessage = _createForwardedMessage(message, null, targetGroup.id);

      _eventController.add(MessageForwardEvent(
        type: MessageForwardEventType.messageForwarded,
        originalMessage: message,
        forwardedMessage: forwardedMessage,
        targetGroup: targetGroup,
      ));

      return true;
    } catch (e) {
      _eventController.add(MessageForwardEvent(
        type: MessageForwardEventType.forwardFailed,
        originalMessage: message,
        error: e.toString(),
      ));
      return false;
    }
  }

  /// 批量转发消息
  Future<List<MessageForwardResult>> forwardMultipleMessages(
    List<Message> messages,
    List<User> targetUsers,
    List<Group> targetGroups,
  ) async {
    final results = <MessageForwardResult>[];

    for (final message in messages) {
      // 转发给用户
      for (final user in targetUsers) {
        final success = await forwardToUser(message, user);
        results.add(MessageForwardResult(
          message: message,
          targetUser: user,
          success: success,
        ));
      }

      // 转发给群组
      for (final group in targetGroups) {
        final success = await forwardToGroup(message, group);
        results.add(MessageForwardResult(
          message: message,
          targetGroup: group,
          success: success,
        ));
      }
    }

    _eventController.add(MessageForwardEvent(
      type: MessageForwardEventType.batchForwardCompleted,
      batchResults: results,
    ));

    return results;
  }

  /// 引用回复消息
  Future<bool> replyToMessage(Message originalMessage, String replyContent, int receiverId) async {
    try {
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 500));

      // 创建引用回复消息
      final replyMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch,
        senderId: 1, // 当前用户ID
        receiverId: receiverId,
        content: replyContent,
        type: MessageType.text,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        sender: User(id: 1, username: 'current_user', email: '<EMAIL>'),
        receiver: User(id: receiverId, username: 'receiver', email: '<EMAIL>'),
        status: MessageStatus.sent,
      );

      _eventController.add(MessageForwardEvent(
        type: MessageForwardEventType.messageReplied,
        originalMessage: originalMessage,
        forwardedMessage: replyMessage,
      ));

      return true;
    } catch (e) {
      _eventController.add(MessageForwardEvent(
        type: MessageForwardEventType.replyFailed,
        originalMessage: originalMessage,
        error: e.toString(),
      ));
      return false;
    }
  }

  /// 创建转发消息
  Message _createForwardedMessage(Message originalMessage, int? receiverId, [int? groupId]) {
    String forwardContent;
    
    // 根据消息类型创建转发内容
    switch (originalMessage.type) {
      case MessageType.text:
        forwardContent = originalMessage.content;
        break;
      case MessageType.image:
        forwardContent = '[图片]';
        break;
      case MessageType.audio:
        forwardContent = '[语音]';
        break;
      case MessageType.video:
        forwardContent = '[视频]';
        break;
      case MessageType.file:
        forwardContent = '[文件]';
        break;
      default:
        forwardContent = '[消息]';
    }

    return Message(
      id: DateTime.now().millisecondsSinceEpoch,
      senderId: 1, // 当前用户ID
      receiverId: receiverId ?? 0,
      content: forwardContent,
      type: originalMessage.type,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sender: User(id: 1, username: 'current_user', email: '<EMAIL>'),
      receiver: receiverId != null 
          ? User(id: receiverId, username: 'receiver', email: '<EMAIL>')
          : User(id: 0, username: 'group', email: '<EMAIL>'),
      status: MessageStatus.sent,
    );
  }

  /// 检查消息是否可以转发
  bool canForwardMessage(Message message) {
    // 已撤回的消息不能转发
    if (message.isRecalled) return false;
    
    // 所有类型的消息都可以转发
    return true;
  }

  /// 获取转发预览文本
  String getForwardPreview(Message message) {
    if (message.isRecalled) {
      return '[消息已撤回]';
    }

    switch (message.type) {
      case MessageType.text:
        return message.content.length > 50 
            ? '${message.content.substring(0, 50)}...'
            : message.content;
      case MessageType.image:
        return '[图片]';
      case MessageType.audio:
        return '[语音]';
      case MessageType.video:
        return '[视频]';
      case MessageType.file:
        return '[文件]';
      default:
        return '[消息]';
    }
  }

  /// 获取引用回复预览文本
  String getReplyPreview(Message message) {
    const maxLength = 30;
    final preview = getForwardPreview(message);
    
    if (preview.length > maxLength) {
      return '${preview.substring(0, maxLength)}...';
    }
    return preview;
  }

  /// 格式化转发消息显示
  String formatForwardedMessage(Message originalMessage, User originalSender) {
    final senderName = originalSender.nickname ?? originalSender.username;
    final content = getForwardPreview(originalMessage);
    
    return '转发自 $senderName：\n$content';
  }

  void dispose() {
    _eventController.close();
  }
}

/// 消息转发事件
class MessageForwardEvent {
  final MessageForwardEventType type;
  final Message originalMessage;
  final Message? forwardedMessage;
  final User? targetUser;
  final Group? targetGroup;
  final String? error;
  final List<MessageForwardResult>? batchResults;

  MessageForwardEvent({
    required this.type,
    required this.originalMessage,
    this.forwardedMessage,
    this.targetUser,
    this.targetGroup,
    this.error,
    this.batchResults,
  });
}

/// 消息转发事件类型
enum MessageForwardEventType {
  messageForwarded,
  forwardFailed,
  messageReplied,
  replyFailed,
  batchForwardCompleted,
}

/// 消息转发结果
class MessageForwardResult {
  final Message message;
  final User? targetUser;
  final Group? targetGroup;
  final bool success;
  final String? error;

  MessageForwardResult({
    required this.message,
    this.targetUser,
    this.targetGroup,
    required this.success,
    this.error,
  });

  String get targetName {
    if (targetUser != null) {
      return targetUser!.nickname ?? targetUser!.username;
    } else if (targetGroup != null) {
      return targetGroup!.name;
    }
    return '未知';
  }
}

/// 转发目标类型
enum ForwardTargetType {
  user,
  group,
}

/// 转发目标
class ForwardTarget {
  final ForwardTargetType type;
  final User? user;
  final Group? group;

  ForwardTarget.user(this.user) : type = ForwardTargetType.user, group = null;
  ForwardTarget.group(this.group) : type = ForwardTargetType.group, user = null;

  String get name {
    switch (type) {
      case ForwardTargetType.user:
        return user!.nickname ?? user!.username;
      case ForwardTargetType.group:
        return group!.name;
    }
  }

  String get avatar {
    switch (type) {
      case ForwardTargetType.user:
        return user!.avatar ?? '';
      case ForwardTargetType.group:
        return group!.avatar ?? '';
    }
  }
}
