import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/message.dart';

// 消息状态指示器
class MessageStatusIndicator extends StatelessWidget {
  final MessageStatus status;
  final double size;

  const MessageStatusIndicator({
    super.key,
    required this.status,
    this.size = 16,
  });

  @override
  Widget build(BuildContext context) {
    return Icon(
      _getStatusIcon(),
      size: size,
      color: _getStatusColor(),
    );
  }

  IconData _getStatusIcon() {
    switch (status) {
      case MessageStatus.sending:
        return Icons.schedule;
      case MessageStatus.sent:
        return Icons.check;
      case MessageStatus.delivered:
        return Icons.done_all;
      case MessageStatus.read:
        return Icons.done_all;
      case MessageStatus.failed:
        return Icons.error_outline;
    }
  }

  Color _getStatusColor() {
    switch (status) {
      case MessageStatus.sending:
        return Colors.grey;
      case MessageStatus.sent:
        return Colors.grey;
      case MessageStatus.delivered:
        return Colors.grey;
      case MessageStatus.read:
        return AppConstants.primaryColor;
      case MessageStatus.failed:
        return AppConstants.errorColor;
    }
  }
}
