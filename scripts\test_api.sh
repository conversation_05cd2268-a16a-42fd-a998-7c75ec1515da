#!/bin/bash

# AqiChat API 测试脚本
# 用于测试后端API的基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
API_BASE_URL="http://localhost:8080/api/v1"
WS_URL="ws://localhost:8080/ws"

# 测试用户数据
TEST_USER1_USERNAME="testuser1"
TEST_USER1_EMAIL="<EMAIL>"
TEST_USER1_PASSWORD="123456"

TEST_USER2_USERNAME="testuser2"
TEST_USER2_EMAIL="<EMAIL>"
TEST_USER2_PASSWORD="123456"

# 全局变量
USER1_TOKEN=""
USER2_TOKEN=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    
    if curl -f -s "$API_BASE_URL/../health" > /dev/null; then
        log_success "后端服务正在运行"
    else
        log_error "后端服务未运行，请先启动服务"
        exit 1
    fi
}

# 测试用户注册
test_register() {
    log_info "测试用户注册..."
    
    # 注册用户1
    local response1=$(curl -s -X POST "$API_BASE_URL/auth/register" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$TEST_USER1_USERNAME\",
            \"email\": \"$TEST_USER1_EMAIL\",
            \"password\": \"$TEST_USER1_PASSWORD\",
            \"nickname\": \"Test User 1\"
        }")
    
    if echo "$response1" | grep -q "access_token"; then
        USER1_TOKEN=$(echo "$response1" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
        log_success "用户1注册成功"
    else
        log_warning "用户1可能已存在，尝试登录..."
        test_login_user1
    fi
    
    # 注册用户2
    local response2=$(curl -s -X POST "$API_BASE_URL/auth/register" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$TEST_USER2_USERNAME\",
            \"email\": \"$TEST_USER2_EMAIL\",
            \"password\": \"$TEST_USER2_PASSWORD\",
            \"nickname\": \"Test User 2\"
        }")
    
    if echo "$response2" | grep -q "access_token"; then
        USER2_TOKEN=$(echo "$response2" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
        log_success "用户2注册成功"
    else
        log_warning "用户2可能已存在，尝试登录..."
        test_login_user2
    fi
}

# 测试用户1登录
test_login_user1() {
    log_info "测试用户1登录..."
    
    local response=$(curl -s -X POST "$API_BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$TEST_USER1_USERNAME\",
            \"password\": \"$TEST_USER1_PASSWORD\"
        }")
    
    if echo "$response" | grep -q "access_token"; then
        USER1_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
        log_success "用户1登录成功"
    else
        log_error "用户1登录失败: $response"
        return 1
    fi
}

# 测试用户2登录
test_login_user2() {
    log_info "测试用户2登录..."
    
    local response=$(curl -s -X POST "$API_BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$TEST_USER2_USERNAME\",
            \"password\": \"$TEST_USER2_PASSWORD\"
        }")
    
    if echo "$response" | grep -q "access_token"; then
        USER2_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
        log_success "用户2登录成功"
    else
        log_error "用户2登录失败: $response"
        return 1
    fi
}

# 测试获取用户资料
test_get_profile() {
    log_info "测试获取用户资料..."
    
    local response=$(curl -s -X GET "$API_BASE_URL/users/profile" \
        -H "Authorization: Bearer $USER1_TOKEN")
    
    if echo "$response" | grep -q "username"; then
        log_success "获取用户资料成功"
    else
        log_error "获取用户资料失败: $response"
        return 1
    fi
}

# 测试搜索用户
test_search_users() {
    log_info "测试搜索用户..."
    
    local response=$(curl -s -X GET "$API_BASE_URL/users/search?query=test&limit=10" \
        -H "Authorization: Bearer $USER1_TOKEN")
    
    if echo "$response" | grep -q "\["; then
        log_success "搜索用户成功"
    else
        log_error "搜索用户失败: $response"
        return 1
    fi
}

# 测试发送好友请求
test_send_friend_request() {
    log_info "测试发送好友请求..."
    
    # 首先获取用户2的ID
    local search_response=$(curl -s -X GET "$API_BASE_URL/users/search?query=$TEST_USER2_USERNAME&limit=1" \
        -H "Authorization: Bearer $USER1_TOKEN")
    
    local user2_id=$(echo "$search_response" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    
    if [ -z "$user2_id" ]; then
        log_error "无法找到用户2的ID"
        return 1
    fi
    
    local response=$(curl -s -X POST "$API_BASE_URL/friends/request" \
        -H "Authorization: Bearer $USER1_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"receiver_id\": $user2_id,
            \"message\": \"Let's be friends!\"
        }")
    
    if echo "$response" | grep -q "sender_id"; then
        log_success "发送好友请求成功"
    else
        log_warning "好友请求可能已存在: $response"
    fi
}

# 测试获取好友列表
test_get_friends() {
    log_info "测试获取好友列表..."
    
    local response=$(curl -s -X GET "$API_BASE_URL/friends" \
        -H "Authorization: Bearer $USER1_TOKEN")
    
    if echo "$response" | grep -q "\["; then
        log_success "获取好友列表成功"
    else
        log_error "获取好友列表失败: $response"
        return 1
    fi
}

# 测试获取会话列表
test_get_conversations() {
    log_info "测试获取会话列表..."
    
    local response=$(curl -s -X GET "$API_BASE_URL/messages/conversations" \
        -H "Authorization: Bearer $USER1_TOKEN")
    
    if echo "$response" | grep -q "\["; then
        log_success "获取会话列表成功"
    else
        log_error "获取会话列表失败: $response"
        return 1
    fi
}

# 测试发送消息
test_send_message() {
    log_info "测试发送消息..."
    
    # 首先获取用户2的ID
    local search_response=$(curl -s -X GET "$API_BASE_URL/users/search?query=$TEST_USER2_USERNAME&limit=1" \
        -H "Authorization: Bearer $USER1_TOKEN")
    
    local user2_id=$(echo "$search_response" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    
    if [ -z "$user2_id" ]; then
        log_error "无法找到用户2的ID"
        return 1
    fi
    
    local response=$(curl -s -X POST "$API_BASE_URL/messages/send" \
        -H "Authorization: Bearer $USER1_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"receiver_id\": $user2_id,
            \"content\": \"Hello from API test!\",
            \"type\": \"text\"
        }")
    
    if echo "$response" | grep -q "content"; then
        log_success "发送消息成功"
    else
        log_error "发送消息失败: $response"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    log_info "开始运行API测试..."
    echo "=================================="
    
    check_service
    test_register
    test_get_profile
    test_search_users
    test_send_friend_request
    test_get_friends
    test_get_conversations
    test_send_message
    
    echo "=================================="
    log_success "所有API测试完成！"
}

# 清理测试数据
cleanup_test_data() {
    log_info "清理测试数据..."
    # 这里可以添加清理测试用户和数据的逻辑
    log_success "测试数据清理完成"
}

# 显示帮助信息
show_help() {
    echo "AqiChat API 测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [操作]"
    echo ""
    echo "操作:"
    echo "  test     运行所有测试 (默认)"
    echo "  cleanup  清理测试数据"
    echo "  help     显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 test"
    echo "  $0 cleanup"
}

# 主函数
main() {
    local action=${1:-test}
    
    case $action in
        test)
            run_all_tests
            ;;
        cleanup)
            cleanup_test_data
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
