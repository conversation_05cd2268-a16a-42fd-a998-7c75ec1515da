import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../models/user.dart';
import '../../widgets/slide_animation.dart';
import '../../services/video_call_service.dart';
import '../chat/chat_screen.dart';

class UserProfileScreen extends StatefulWidget {
  final User user;
  final bool isCurrentUser;

  const UserProfileScreen({
    super.key,
    required this.user,
    this.isCurrentUser = false,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final VideoCallService _videoCallService = VideoCallService();
  bool _isBlocked = false;
  bool _isFriend = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // 自定义AppBar
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppConstants.primaryColor,
                      AppConstants.primaryColor.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 60),
                      
                      // 头像
                      SlideInAnimation(
                        begin: const Offset(0.0, -1.0),
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 4),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 10,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: ClipOval(
                            child: widget.user.avatar != null
                                ? Image.network(
                                    widget.user.avatar!,
                                    width: 120,
                                    height: 120,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 120,
                                        height: 120,
                                        color: Colors.white.withValues(alpha: 0.2),
                                        child: const Icon(
                                          Icons.person,
                                          size: 60,
                                          color: Colors.white,
                                        ),
                                      );
                                    },
                                  )
                                : Container(
                                    width: 120,
                                    height: 120,
                                    color: Colors.white.withValues(alpha: 0.2),
                                    child: const Icon(
                                      Icons.person,
                                      size: 60,
                                      color: Colors.white,
                                    ),
                                  ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // 用户名和昵称
                      SlideInAnimation(
                        begin: const Offset(0.0, 1.0),
                        child: Column(
                          children: [
                            Text(
                              widget.user.nickname ?? widget.user.username,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            if (widget.user.nickname != null)
                              Text(
                                '@${widget.user.username}',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontSize: 16,
                                ),
                              ),
                            const SizedBox(height: 8),
                            
                            // 在线状态
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(widget.user.status),
                                    shape: BoxShape.circle,
                                    border: Border.all(color: Colors.white, width: 2),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _getStatusText(widget.user.status),
                                  style: TextStyle(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              if (!widget.isCurrentUser)
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onSelected: _handleMenuAction,
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'block',
                      child: Row(
                        children: [
                          Icon(_isBlocked ? Icons.person_add : Icons.block),
                          const SizedBox(width: 8),
                          Text(_isBlocked ? '解除屏蔽' : '屏蔽用户'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'report',
                      child: Row(
                        children: [
                          Icon(Icons.report),
                          SizedBox(width: 8),
                          Text('举报用户'),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
          
          // 内容区域
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 操作按钮
                  if (!widget.isCurrentUser)
                    SlideInAnimation(
                      begin: const Offset(0.0, 1.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _startChat,
                              icon: const Icon(Icons.message),
                              label: const Text('发消息'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppConstants.primaryColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _startVideoCall,
                            icon: const Icon(Icons.videocam),
                            label: const Text('视频'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _startAudioCall,
                            icon: const Icon(Icons.phone),
                            label: const Text('语音'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 20),
                  
                  // 个人信息
                  SlideInAnimation(
                    begin: const Offset(-1.0, 0.0),
                    child: _buildInfoCard(
                      '个人信息',
                      [
                        if (widget.user.bio != null)
                          _buildInfoItem(Icons.info, '个人简介', widget.user.bio!),
                        _buildInfoItem(Icons.email, '邮箱', widget.user.email),
                        if (widget.user.phone != null)
                          _buildInfoItem(Icons.phone, '手机号', widget.user.phone!),
                        _buildInfoItem(Icons.access_time, '加入时间', 
                            '${widget.user.createdAt.year}-${widget.user.createdAt.month.toString().padLeft(2, '0')}-${widget.user.createdAt.day.toString().padLeft(2, '0')}'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // 更多信息
                  SlideInAnimation(
                    begin: const Offset(1.0, 0.0),
                    child: _buildInfoCard(
                      '更多信息',
                      [
                        _buildInfoItem(Icons.location_on, '所在地', '北京市'),
                        _buildInfoItem(Icons.cake, '生日', '1990-01-01'),
                        _buildInfoItem(Icons.link, '个人网站', 'https://example.com'),
                        _buildInfoItem(Icons.person_outline, '性别', '不愿透露'),
                      ],
                    ),
                  ),
                  
                  if (!widget.isCurrentUser) ...[
                    const SizedBox(height: 16),
                    
                    // 共同好友
                    SlideInAnimation(
                      begin: const Offset(-1.0, 0.0),
                      child: _buildInfoCard(
                        '共同好友',
                        [
                          ListTile(
                            leading: const CircleAvatar(
                              child: Icon(Icons.people),
                            ),
                            title: const Text('3个共同好友'),
                            subtitle: const Text('Alice, Bob, Charlie'),
                            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                            onTap: () {
                              // TODO: 显示共同好友列表
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return Colors.green;
      case UserStatus.away:
        return Colors.orange;
      case UserStatus.busy:
        return Colors.red;
      case UserStatus.offline:
        return Colors.grey;
    }
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return '在线';
      case UserStatus.away:
        return '离开';
      case UserStatus.busy:
        return '忙碌';
      case UserStatus.offline:
        return '离线';
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'block':
        _toggleBlock();
        break;
      case 'report':
        _reportUser();
        break;
    }
  }

  void _toggleBlock() {
    setState(() {
      _isBlocked = !_isBlocked;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isBlocked ? '已屏蔽该用户' : '已解除屏蔽'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _reportUser() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('举报用户'),
        content: const Text('确定要举报这个用户吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('举报已提交'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            child: const Text('举报'),
          ),
        ],
      ),
    );
  }

  void _startChat() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChatScreen(user: widget.user),
      ),
    );
  }

  void _startVideoCall() {
    _videoCallService.startVideoCall(widget.user);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在发起视频通话...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _startAudioCall() {
    _videoCallService.startAudioCall(widget.user);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在发起语音通话...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
