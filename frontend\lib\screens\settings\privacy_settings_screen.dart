import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/slide_animation.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  bool _showOnlineStatus = true;
  bool _showLastSeen = true;
  bool _allowFriendRequests = true;
  bool _allowGroupInvites = true;
  bool _showReadReceipts = true;
  bool _allowProfilePhotoView = true;
  bool _allowStatusView = true;
  String _whoCanSeeProfile = 'everyone'; // everyone, friends, nobody
  String _whoCanAddToGroups = 'everyone'; // everyone, friends, nobody
  String _whoCanSeeLastSeen = 'everyone'; // everyone, friends, nobody

  final List<PrivacyOption> _privacyOptions = [
    PrivacyOption(value: 'everyone', title: '所有人', subtitle: '任何人都可以看到'),
    PrivacyOption(value: 'friends', title: '仅好友', subtitle: '只有好友可以看到'),
    PrivacyOption(value: 'nobody', title: '不公开', subtitle: '任何人都无法看到'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('隐私设置'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 在线状态
          SlideInAnimation(
            begin: const Offset(0.0, -1.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '在线状态',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    SwitchListTile(
                      title: const Text('显示在线状态'),
                      subtitle: const Text('让其他人看到您是否在线'),
                      value: _showOnlineStatus,
                      onChanged: (value) {
                        setState(() {
                          _showOnlineStatus = value;
                        });
                        _savePrivacySettings();
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    ListTile(
                      title: const Text('最后上线时间'),
                      subtitle: Text(_getPrivacyOptionTitle(_whoCanSeeLastSeen)),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => _showPrivacyOptionDialog(
                        '最后上线时间',
                        '选择谁可以看到您的最后上线时间',
                        _whoCanSeeLastSeen,
                        (value) {
                          setState(() {
                            _whoCanSeeLastSeen = value;
                          });
                          _savePrivacySettings();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 个人资料
          SlideInAnimation(
            begin: const Offset(-1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '个人资料',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    ListTile(
                      title: const Text('个人资料可见性'),
                      subtitle: Text(_getPrivacyOptionTitle(_whoCanSeeProfile)),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => _showPrivacyOptionDialog(
                        '个人资料可见性',
                        '选择谁可以查看您的个人资料',
                        _whoCanSeeProfile,
                        (value) {
                          setState(() {
                            _whoCanSeeProfile = value;
                          });
                          _savePrivacySettings();
                        },
                      ),
                    ),
                    
                    SwitchListTile(
                      title: const Text('头像可见'),
                      subtitle: const Text('允许他人查看您的头像'),
                      value: _allowProfilePhotoView,
                      onChanged: (value) {
                        setState(() {
                          _allowProfilePhotoView = value;
                        });
                        _savePrivacySettings();
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    SwitchListTile(
                      title: const Text('个人状态可见'),
                      subtitle: const Text('允许他人查看您的个人状态'),
                      value: _allowStatusView,
                      onChanged: (value) {
                        setState(() {
                          _allowStatusView = value;
                        });
                        _savePrivacySettings();
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 消息设置
          SlideInAnimation(
            begin: const Offset(1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '消息设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    SwitchListTile(
                      title: const Text('已读回执'),
                      subtitle: const Text('发送已读状态给对方'),
                      value: _showReadReceipts,
                      onChanged: (value) {
                        setState(() {
                          _showReadReceipts = value;
                        });
                        _savePrivacySettings();
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 联系人设置
          SlideInAnimation(
            begin: const Offset(-1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '联系人设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    SwitchListTile(
                      title: const Text('接受好友请求'),
                      subtitle: const Text('允许他人向您发送好友请求'),
                      value: _allowFriendRequests,
                      onChanged: (value) {
                        setState(() {
                          _allowFriendRequests = value;
                        });
                        _savePrivacySettings();
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    ListTile(
                      title: const Text('群聊邀请'),
                      subtitle: Text(_getPrivacyOptionTitle(_whoCanAddToGroups)),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => _showPrivacyOptionDialog(
                        '群聊邀请',
                        '选择谁可以邀请您加入群聊',
                        _whoCanAddToGroups,
                        (value) {
                          setState(() {
                            _whoCanAddToGroups = value;
                          });
                          _savePrivacySettings();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 数据与存储
          SlideInAnimation(
            begin: const Offset(1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '数据与存储',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    ListTile(
                      leading: const Icon(Icons.delete_forever, color: Colors.red),
                      title: const Text('清除聊天记录'),
                      subtitle: const Text('删除所有聊天记录'),
                      onTap: () => _showClearDataDialog('聊天记录'),
                    ),
                    
                    ListTile(
                      leading: const Icon(Icons.cached, color: AppConstants.primaryColor),
                      title: const Text('清除缓存'),
                      subtitle: const Text('清除应用缓存数据'),
                      onTap: () => _showClearDataDialog('缓存'),
                    ),
                    
                    ListTile(
                      leading: const Icon(Icons.download, color: AppConstants.primaryColor),
                      title: const Text('下载数据'),
                      subtitle: const Text('导出您的聊天数据'),
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('数据导出功能开发中'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 账户安全
          SlideInAnimation(
            begin: const Offset(0.0, 1.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '账户安全',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    ListTile(
                      leading: const Icon(Icons.lock, color: AppConstants.primaryColor),
                      title: const Text('两步验证'),
                      subtitle: const Text('为您的账户添加额外保护'),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('两步验证功能开发中'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                    
                    ListTile(
                      leading: const Icon(Icons.devices, color: AppConstants.primaryColor),
                      title: const Text('活跃设备'),
                      subtitle: const Text('管理已登录的设备'),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('设备管理功能开发中'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                    
                    ListTile(
                      leading: const Icon(Icons.delete_forever, color: Colors.red),
                      title: const Text('删除账户'),
                      subtitle: const Text('永久删除您的账户'),
                      onTap: () => _showDeleteAccountDialog(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getPrivacyOptionTitle(String value) {
    return _privacyOptions.firstWhere((option) => option.value == value).title;
  }

  void _showPrivacyOptionDialog(
    String title,
    String description,
    String currentValue,
    Function(String) onChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            const SizedBox(height: 16),
            ..._privacyOptions.map((option) => RadioListTile<String>(
              title: Text(option.title),
              subtitle: Text(option.subtitle),
              value: option.value,
              groupValue: currentValue,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.of(context).pop();
                }
              },
              activeColor: AppConstants.primaryColor,
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog(String dataType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('清除$dataType'),
        content: Text('确定要清除所有$dataType吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$dataType已清除'),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('清除'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除账户'),
        content: const Text(
          '警告：删除账户将永久删除您的所有数据，包括聊天记录、好友列表等。此操作无法撤销。\n\n确定要删除您的账户吗？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('账户删除功能开发中'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _savePrivacySettings() {
    // 在实际应用中，这里会保存隐私设置到服务器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('隐私设置已保存'),
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 1),
      ),
    );
  }
}

class PrivacyOption {
  final String value;
  final String title;
  final String subtitle;

  PrivacyOption({
    required this.value,
    required this.title,
    required this.subtitle,
  });
}
