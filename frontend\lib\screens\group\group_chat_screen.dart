import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../models/group.dart';
import '../../models/message.dart';
import '../../models/user.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/message_bubble.dart';
import '../../widgets/user_avatar.dart';
import '../../widgets/media_picker.dart';
import '../../widgets/loading_animation.dart';

class GroupChatScreen extends StatefulWidget {
  final Group group;

  const GroupChatScreen({
    super.key,
    required this.group,
  });

  @override
  State<GroupChatScreen> createState() => _GroupChatScreenState();
}

class _GroupChatScreenState extends State<GroupChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<Message> _messages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟加载群聊消息
      await Future.delayed(const Duration(seconds: 1));
      
      final now = DateTime.now();
      _messages.addAll([
        Message(
          id: 1,
          senderId: 2,
          receiverId: widget.group.id,
          content: '大家好！欢迎加入群聊 🎉',
          type: MessageType.text,
          createdAt: now.subtract(const Duration(hours: 2)),
          updatedAt: now.subtract(const Duration(hours: 2)),
          sender: _getUserById(2)!,
          receiver: _getUserById(widget.group.id)!,
          isRead: true,
        ),
        Message(
          id: 2,
          senderId: 3,
          receiverId: widget.group.id,
          content: '谢谢邀请！很高兴认识大家',
          type: MessageType.text,
          createdAt: now.subtract(const Duration(hours: 1, minutes: 30)),
          updatedAt: now.subtract(const Duration(hours: 1, minutes: 30)),
          sender: _getUserById(3)!,
          receiver: _getUserById(widget.group.id)!,
          isRead: true,
        ),
        Message(
          id: 3,
          senderId: 4,
          receiverId: widget.group.id,
          content: '这个群聊的主题是什么呢？',
          type: MessageType.text,
          createdAt: now.subtract(const Duration(minutes: 45)),
          updatedAt: now.subtract(const Duration(minutes: 45)),
          sender: _getUserById(4)!,
          receiver: _getUserById(widget.group.id)!,
          isRead: true,
        ),
      ]);

      setState(() {
        _isLoading = false;
      });
      
      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载消息失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _sendMessage() {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    if (currentUser == null) return;

    final message = Message(
      id: DateTime.now().millisecondsSinceEpoch,
      senderId: currentUser.id,
      receiverId: widget.group.id,
      content: content,
      type: MessageType.text,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sender: currentUser,
      receiver: _getUserById(widget.group.id)!,
      isRead: false,
    );

    setState(() {
      _messages.add(message);
    });

    _messageController.clear();
    _scrollToBottom();
  }

  void _showMediaPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MediaPicker(
        onMediaSelected: (path, type) {
          _handleMediaSelected(path, type);
        },
      ),
    );
  }

  void _handleMediaSelected(String path, MediaType type) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    if (currentUser == null) return;

    String content;
    MessageType messageType;

    switch (type) {
      case MediaType.image:
        content = path;
        messageType = MessageType.image;
        break;
      case MediaType.file:
        content = path.split('/').last;
        messageType = MessageType.file;
        break;
      case MediaType.audio:
        content = path;
        messageType = MessageType.file; // 暂时用文件类型
        break;
      case MediaType.video:
        content = path;
        messageType = MessageType.image; // 暂时用图片类型
        break;
    }

    final message = Message(
      id: DateTime.now().millisecondsSinceEpoch,
      senderId: currentUser.id,
      receiverId: widget.group.id,
      content: content,
      type: messageType,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sender: currentUser,
      receiver: _getUserById(widget.group.id)!,
      isRead: false,
    );

    setState(() {
      _messages.add(message);
    });

    _scrollToBottom();
  }

  void _showGroupInfo() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) => _buildGroupInfo(scrollController),
      ),
    );
  }

  Widget _buildGroupInfo(ScrollController scrollController) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          Expanded(
            child: ListView(
              controller: scrollController,
              children: [
                // 群头像和名称
                Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey[200],
                        border: Border.all(color: AppConstants.primaryColor, width: 2),
                      ),
                      child: widget.group.avatar != null
                          ? ClipOval(
                              child: Image.asset(
                                widget.group.avatar!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return const Icon(Icons.group, size: 40, color: Colors.grey);
                                },
                              ),
                            )
                          : const Icon(Icons.group, size: 40, color: Colors.grey),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      widget.group.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (widget.group.description != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        widget.group.description!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                    const SizedBox(height: 8),
                    Text(
                      '${widget.group.memberCount} 名成员',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                
                // 群成员列表
                const Text(
                  '群成员',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                ...widget.group.members.map((member) => ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppConstants.primaryColor,
                    child: Text(
                      member.nickname?.substring(0, 1).toUpperCase() ?? 
                      member.userId.toString().substring(0, 1),
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(member.nickname ?? 'User ${member.userId}'),
                  subtitle: Text(_getRoleText(member.role)),
                  trailing: member.role == GroupRole.owner
                      ? const Icon(Icons.star, color: Colors.amber)
                      : member.role == GroupRole.admin
                          ? const Icon(Icons.shield, color: Colors.blue)
                          : null,
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleText(GroupRole role) {
    switch (role) {
      case GroupRole.owner:
        return '群主';
      case GroupRole.admin:
        return '管理员';
      case GroupRole.member:
        return '成员';
    }
  }

  User? _getUserById(int userId) {
    // 这里应该从用户缓存或API获取用户信息
    // 暂时返回模拟用户
    return User(
      id: userId,
      username: 'user$userId',
      email: 'user$<EMAIL>',
      nickname: 'User $userId',
      avatar: '',
      status: UserStatus.online,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: _showGroupInfo,
          child: Row(
            children: [
              Container(
                width: 35,
                height: 35,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withValues(alpha: 0.2),
                ),
                child: widget.group.avatar != null
                    ? ClipOval(
                        child: Image.asset(
                          widget.group.avatar!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(Icons.group, size: 20, color: Colors.white);
                          },
                        ),
                      )
                    : const Icon(Icons.group, size: 20, color: Colors.white),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.group.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${widget.group.activeMemberCount} 名成员',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showGroupInfo,
            tooltip: '群信息',
          ),
        ],
      ),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child: _isLoading
                ? const Center(
                    child: LoadingAnimation(
                      size: 60,
                      message: '加载消息中...',
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final message = _messages[index];
                      final sender = _getUserById(message.senderId);
                      final authProvider = Provider.of<AuthProvider>(context, listen: false);
                      final isMe = message.senderId == authProvider.currentUser?.id;

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: MessageBubble(
                          message: message,
                          sender: sender,
                          isMe: isMe,
                          showAvatar: !isMe,
                          showSenderName: !isMe,
                        ),
                      );
                    },
                  ),
          ),

          // 输入框
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: _showMediaPicker,
                  color: AppConstants.primaryColor,
                ),
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: '输入消息...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 10,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                FloatingActionButton(
                  onPressed: _sendMessage,
                  backgroundColor: AppConstants.primaryColor,
                  mini: true,
                  child: const Icon(Icons.send, color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
