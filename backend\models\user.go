package models

import (
	"time"

	"gorm.io/gorm"
)

type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Username  string         `json:"username" gorm:"uniqueIndex;not null"`
	Email     string         `json:"email" gorm:"uniqueIndex;not null"`
	Password  string         `json:"-" gorm:"not null"` // 不在JSON中显示密码
	Nickname  string         `json:"nickname"`
	Avatar    string         `json:"avatar"`
	Status    UserStatus     `json:"status" gorm:"default:offline"`
	LastSeen  *time.Time     `json:"last_seen"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	SentMessages     []Message     `json:"-" gorm:"foreignKey:SenderID"`
	ReceivedMessages []Message     `json:"-" gorm:"foreignKey:ReceiverID"`
	SentRequests     []FriendRequest `json:"-" gorm:"foreignKey:SenderID"`
	ReceivedRequests []FriendRequest `json:"-" gorm:"foreignKey:ReceiverID"`
}

type UserStatus string

const (
	StatusOnline  UserStatus = "online"
	StatusOffline UserStatus = "offline"
	StatusAway    UserStatus = "away"
	StatusBusy    UserStatus = "busy"
)

type FriendRequest struct {
	ID         uint                `json:"id" gorm:"primaryKey"`
	SenderID   uint                `json:"sender_id" gorm:"not null"`
	ReceiverID uint                `json:"receiver_id" gorm:"not null"`
	Status     FriendRequestStatus `json:"status" gorm:"default:pending"`
	Message    string              `json:"message"`
	CreatedAt  time.Time           `json:"created_at"`
	UpdatedAt  time.Time           `json:"updated_at"`
	
	// 关联关系
	Sender   User `json:"sender" gorm:"foreignKey:SenderID"`
	Receiver User `json:"receiver" gorm:"foreignKey:ReceiverID"`
}

type FriendRequestStatus string

const (
	RequestPending  FriendRequestStatus = "pending"
	RequestAccepted FriendRequestStatus = "accepted"
	RequestRejected FriendRequestStatus = "rejected"
)

type Friendship struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	FriendID  uint      `json:"friend_id" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联关系
	User   User `json:"user" gorm:"foreignKey:UserID"`
	Friend User `json:"friend" gorm:"foreignKey:FriendID"`
}

// 用户注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=20"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Nickname string `json:"nickname" binding:"max=50"`
}

// 用户登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// 用户登录响应
type LoginResponse struct {
	User         User   `json:"user"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

// 更新用户资料请求
type UpdateProfileRequest struct {
	Nickname string     `json:"nickname" binding:"max=50"`
	Avatar   string     `json:"avatar"`
	Status   UserStatus `json:"status"`
}

// 搜索用户请求
type SearchUsersRequest struct {
	Query string `json:"query" binding:"required,min=1"`
	Limit int    `json:"limit" binding:"min=1,max=50"`
}

// 发送好友请求
type SendFriendRequestRequest struct {
	ReceiverID uint   `json:"receiver_id" binding:"required"`
	Message    string `json:"message" binding:"max=200"`
}
