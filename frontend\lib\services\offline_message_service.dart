import 'dart:convert';
import 'dart:async';
import '../models/message.dart';
import '../models/user.dart';

/// 离线消息服务
/// 处理消息的本地存储、同步和离线发送
class OfflineMessageService {
  static final OfflineMessageService _instance = OfflineMessageService._internal();
  factory OfflineMessageService() => _instance;
  OfflineMessageService._internal();

  // 离线消息队列
  final List<PendingMessage> _pendingMessages = [];
  final List<Message> _offlineMessages = [];
  final Map<String, List<Message>> _conversationMessages = {};
  
  // 同步状态
  bool _isOnline = true;
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  
  // 事件流
  final StreamController<OfflineMessageEvent> _eventController = 
      StreamController<OfflineMessageEvent>.broadcast();
  
  Stream<OfflineMessageEvent> get eventStream => _eventController.stream;

  /// 设置在线状态
  void setOnlineStatus(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;
      _eventController.add(OfflineMessageEvent(
        type: OfflineMessageEventType.connectionChanged,
        data: {'isOnline': isOnline},
      ));
      
      if (isOnline) {
        _syncPendingMessages();
      }
    }
  }

  /// 获取在线状态
  bool get isOnline => _isOnline;

  /// 获取同步状态
  bool get isSyncing => _isSyncing;

  /// 获取最后同步时间
  DateTime? get lastSyncTime => _lastSyncTime;

  /// 添加待发送消息
  Future<void> addPendingMessage(Message message) async {
    final pendingMessage = PendingMessage(
      message: message,
      timestamp: DateTime.now(),
      retryCount: 0,
      status: PendingMessageStatus.pending,
    );
    
    _pendingMessages.add(pendingMessage);
    
    // 保存到本地存储
    await _savePendingMessages();
    
    _eventController.add(OfflineMessageEvent(
      type: OfflineMessageEventType.messagePending,
      data: {'message': message},
    ));
    
    // 如果在线，立即尝试发送
    if (_isOnline) {
      _sendPendingMessage(pendingMessage);
    }
  }

  /// 获取待发送消息列表
  List<PendingMessage> getPendingMessages() {
    return List.unmodifiable(_pendingMessages);
  }

  /// 获取离线消息列表
  List<Message> getOfflineMessages() {
    return List.unmodifiable(_offlineMessages);
  }

  /// 获取对话的离线消息
  List<Message> getConversationOfflineMessages(String conversationId) {
    return _conversationMessages[conversationId] ?? [];
  }

  /// 添加离线接收的消息
  Future<void> addOfflineMessage(Message message) async {
    _offlineMessages.add(message);
    
    // 按对话分组存储
    final conversationId = _getConversationId(message);
    if (!_conversationMessages.containsKey(conversationId)) {
      _conversationMessages[conversationId] = [];
    }
    _conversationMessages[conversationId]!.add(message);
    
    // 保存到本地存储
    await _saveOfflineMessages();
    
    _eventController.add(OfflineMessageEvent(
      type: OfflineMessageEventType.messageReceived,
      data: {'message': message},
    ));
  }

  /// 标记消息为已读
  Future<void> markMessagesAsRead(List<int> messageIds) async {
    for (final messageId in messageIds) {
      final messageIndex = _offlineMessages.indexWhere((m) => m.id == messageId);
      if (messageIndex != -1) {
        _offlineMessages[messageIndex] = _offlineMessages[messageIndex].copyWith(
          isRead: true,
        );
      }
    }
    
    await _saveOfflineMessages();
    
    _eventController.add(OfflineMessageEvent(
      type: OfflineMessageEventType.messagesRead,
      data: {'messageIds': messageIds},
    ));
  }

  /// 同步待发送消息
  Future<void> _syncPendingMessages() async {
    if (_isSyncing || _pendingMessages.isEmpty) return;
    
    _isSyncing = true;
    _eventController.add(OfflineMessageEvent(
      type: OfflineMessageEventType.syncStarted,
      data: {},
    ));
    
    try {
      final messagesToSend = _pendingMessages
          .where((pm) => pm.status == PendingMessageStatus.pending)
          .toList();
      
      for (final pendingMessage in messagesToSend) {
        await _sendPendingMessage(pendingMessage);
        
        // 添加延迟避免过快发送
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      _lastSyncTime = DateTime.now();
      
      _eventController.add(OfflineMessageEvent(
        type: OfflineMessageEventType.syncCompleted,
        data: {'syncTime': _lastSyncTime},
      ));
    } catch (e) {
      _eventController.add(OfflineMessageEvent(
        type: OfflineMessageEventType.syncFailed,
        data: {'error': e.toString()},
      ));
    } finally {
      _isSyncing = false;
    }
  }

  /// 发送单个待发送消息
  Future<void> _sendPendingMessage(PendingMessage pendingMessage) async {
    try {
      pendingMessage.status = PendingMessageStatus.sending;
      
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 模拟发送成功/失败
      final success = DateTime.now().millisecond % 10 != 0; // 90% 成功率
      
      if (success) {
        pendingMessage.status = PendingMessageStatus.sent;
        _pendingMessages.remove(pendingMessage);
        
        _eventController.add(OfflineMessageEvent(
          type: OfflineMessageEventType.messageSent,
          data: {'message': pendingMessage.message},
        ));
      } else {
        pendingMessage.status = PendingMessageStatus.failed;
        pendingMessage.retryCount++;
        
        _eventController.add(OfflineMessageEvent(
          type: OfflineMessageEventType.messageFailed,
          data: {
            'message': pendingMessage.message,
            'retryCount': pendingMessage.retryCount,
          },
        ));
        
        // 如果重试次数过多，标记为永久失败
        if (pendingMessage.retryCount >= 3) {
          pendingMessage.status = PendingMessageStatus.permanentlyFailed;
        }
      }
      
      await _savePendingMessages();
    } catch (e) {
      pendingMessage.status = PendingMessageStatus.failed;
      pendingMessage.retryCount++;
      await _savePendingMessages();
      
      _eventController.add(OfflineMessageEvent(
        type: OfflineMessageEventType.messageFailed,
        data: {
          'message': pendingMessage.message,
          'error': e.toString(),
        },
      ));
    }
  }

  /// 重试发送失败的消息
  Future<void> retryFailedMessage(PendingMessage pendingMessage) async {
    if (pendingMessage.status == PendingMessageStatus.failed) {
      await _sendPendingMessage(pendingMessage);
    }
  }

  /// 删除待发送消息
  Future<void> deletePendingMessage(PendingMessage pendingMessage) async {
    _pendingMessages.remove(pendingMessage);
    await _savePendingMessages();
    
    _eventController.add(OfflineMessageEvent(
      type: OfflineMessageEventType.messageCancelled,
      data: {'message': pendingMessage.message},
    ));
  }

  /// 清理已读的离线消息
  Future<void> cleanupReadMessages() async {
    _offlineMessages.removeWhere((message) => message.isRead);
    
    // 重新构建对话消息映射
    _conversationMessages.clear();
    for (final message in _offlineMessages) {
      final conversationId = _getConversationId(message);
      if (!_conversationMessages.containsKey(conversationId)) {
        _conversationMessages[conversationId] = [];
      }
      _conversationMessages[conversationId]!.add(message);
    }
    
    await _saveOfflineMessages();
    
    _eventController.add(OfflineMessageEvent(
      type: OfflineMessageEventType.messagesCleanedUp,
      data: {},
    ));
  }

  /// 获取统计信息
  OfflineMessageStats getStats() {
    final pendingCount = _pendingMessages
        .where((pm) => pm.status == PendingMessageStatus.pending)
        .length;
    final failedCount = _pendingMessages
        .where((pm) => pm.status == PendingMessageStatus.failed)
        .length;
    final unreadCount = _offlineMessages
        .where((m) => !m.isRead)
        .length;
    
    return OfflineMessageStats(
      pendingCount: pendingCount,
      failedCount: failedCount,
      unreadOfflineCount: unreadCount,
      totalOfflineCount: _offlineMessages.length,
      lastSyncTime: _lastSyncTime,
      isOnline: _isOnline,
      isSyncing: _isSyncing,
    );
  }

  // 私有方法

  String _getConversationId(Message message) {
    if (message.groupId != null) {
      return 'group_${message.groupId}';
    } else {
      return 'user_${message.senderId}';
    }
  }

  Future<void> _savePendingMessages() async {
    // 在实际应用中，这里会保存到本地数据库或文件
    // 这里只是模拟
  }

  Future<void> _saveOfflineMessages() async {
    // 在实际应用中，这里会保存到本地数据库或文件
    // 这里只是模拟
  }

  Future<void> _loadPendingMessages() async {
    // 在实际应用中，这里会从本地数据库或文件加载
    // 这里只是模拟
  }

  Future<void> _loadOfflineMessages() async {
    // 在实际应用中，这里会从本地数据库或文件加载
    // 这里只是模拟
  }

  void dispose() {
    _eventController.close();
  }
}

/// 待发送消息
class PendingMessage {
  final Message message;
  final DateTime timestamp;
  int retryCount;
  PendingMessageStatus status;

  PendingMessage({
    required this.message,
    required this.timestamp,
    required this.retryCount,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'message': message.toJson(),
      'timestamp': timestamp.toIso8601String(),
      'retryCount': retryCount,
      'status': status.index,
    };
  }

  factory PendingMessage.fromJson(Map<String, dynamic> json) {
    return PendingMessage(
      message: Message.fromJson(json['message']),
      timestamp: DateTime.parse(json['timestamp']),
      retryCount: json['retryCount'],
      status: PendingMessageStatus.values[json['status']],
    );
  }
}

/// 待发送消息状态
enum PendingMessageStatus {
  pending,
  sending,
  sent,
  failed,
  permanentlyFailed,
}

/// 离线消息事件
class OfflineMessageEvent {
  final OfflineMessageEventType type;
  final Map<String, dynamic> data;

  OfflineMessageEvent({
    required this.type,
    required this.data,
  });
}

/// 离线消息事件类型
enum OfflineMessageEventType {
  connectionChanged,
  messagePending,
  messageSent,
  messageFailed,
  messageCancelled,
  messageReceived,
  messagesRead,
  messagesCleanedUp,
  syncStarted,
  syncCompleted,
  syncFailed,
}

/// 离线消息统计
class OfflineMessageStats {
  final int pendingCount;
  final int failedCount;
  final int unreadOfflineCount;
  final int totalOfflineCount;
  final DateTime? lastSyncTime;
  final bool isOnline;
  final bool isSyncing;

  OfflineMessageStats({
    required this.pendingCount,
    required this.failedCount,
    required this.unreadOfflineCount,
    required this.totalOfflineCount,
    required this.lastSyncTime,
    required this.isOnline,
    required this.isSyncing,
  });

  Map<String, dynamic> toJson() {
    return {
      'pendingCount': pendingCount,
      'failedCount': failedCount,
      'unreadOfflineCount': unreadOfflineCount,
      'totalOfflineCount': totalOfflineCount,
      'lastSyncTime': lastSyncTime?.toIso8601String(),
      'isOnline': isOnline,
      'isSyncing': isSyncing,
    };
  }
}
