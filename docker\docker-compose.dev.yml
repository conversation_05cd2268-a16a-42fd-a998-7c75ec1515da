version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: aqichat-postgres-dev
    environment:
      POSTGRES_DB: aqichat
      POSTGRES_USER: aqichat_user
      POSTGRES_PASSWORD: aqichat_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - aqichat-dev-network
    restart: unless-stopped

  # Go后端服务
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: aqichat-backend-dev
    environment:
      - ENVIRONMENT=development
      - PORT=8080
      - DATABASE_URL=******************************************************/aqichat?sslmode=disable
      - JWT_SECRET=dev-jwt-secret-key-for-testing
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - aqichat-dev-network
    restart: unless-stopped
    volumes:
      - ../backend:/app/src
    # 开发模式下的健康检查
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_dev_data:

networks:
  aqichat-dev-network:
    driver: bridge
