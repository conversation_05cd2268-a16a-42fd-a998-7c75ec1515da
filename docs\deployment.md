# AqiChat 部署指南

本文档介绍如何部署 AqiChat 聊天应用。

## 系统要求

### 最低配置
- CPU: 2核
- 内存: 4GB RAM
- 存储: 20GB 可用空间
- 操作系统: Linux (Ubuntu 20.04+ 推荐)

### 推荐配置
- CPU: 4核
- 内存: 8GB RAM
- 存储: 50GB SSD
- 操作系统: Linux (Ubuntu 22.04 LTS)

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl (用于健康检查)

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd aqichat
```

### 2. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env.dev

# 编辑配置文件
nano .env.dev
```

### 3. 启动服务
```bash
# 使用部署脚本启动开发环境
./scripts/deploy.sh dev start

# 或者直接使用 Docker Compose
cd docker
docker-compose up -d
```

### 4. 验证部署
```bash
# 检查服务状态
./scripts/deploy.sh dev status

# 运行API测试
./scripts/test_api.sh test
```

## 详细部署步骤

### 开发环境部署

1. **准备环境**
   ```bash
   # 安装 Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # 安装 Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env.dev
   ```
   
   编辑 `.env.dev` 文件，设置以下关键配置：
   - `DATABASE_URL`: 数据库连接字符串
   - `JWT_SECRET`: JWT 签名密钥
   - `ENVIRONMENT`: 设置为 `development`

3. **启动服务**
   ```bash
   ./scripts/deploy.sh dev start
   ```

4. **验证服务**
   ```bash
   # 检查所有容器状态
   docker ps
   
   # 检查后端健康状态
   curl http://localhost:8080/health
   
   # 运行完整测试
   ./scripts/test_api.sh
   ```

### 生产环境部署

1. **服务器准备**
   ```bash
   # 更新系统
   sudo apt update && sudo apt upgrade -y
   
   # 安装必要软件
   sudo apt install -y git curl wget
   ```

2. **安全配置**
   ```bash
   # 创建专用用户
   sudo useradd -m -s /bin/bash aqichat
   sudo usermod -aG docker aqichat
   
   # 切换到专用用户
   sudo su - aqichat
   ```

3. **环境配置**
   ```bash
   # 创建生产环境配置
   cp .env.example .env.prod
   ```
   
   编辑 `.env.prod` 文件，设置生产环境配置：
   ```bash
   ENVIRONMENT=production
   DATABASE_URL=postgres://user:password@localhost:5432/aqichat_prod?sslmode=require
   JWT_SECRET=your-super-secure-jwt-secret-key
   CORS_ORIGINS=https://yourdomain.com
   ```

4. **SSL证书配置**
   ```bash
   # 创建SSL证书目录
   mkdir -p docker/ssl
   
   # 复制SSL证书文件
   cp /path/to/your/cert.pem docker/ssl/
   cp /path/to/your/key.pem docker/ssl/
   ```

5. **启动生产服务**
   ```bash
   ./scripts/deploy.sh prod start
   ```

6. **配置防火墙**
   ```bash
   # 允许HTTP和HTTPS流量
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw enable
   ```

## 监控和维护

### 日志管理
```bash
# 查看所有服务日志
./scripts/deploy.sh prod logs

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f postgres
```

### 数据库备份
```bash
# 手动备份
./scripts/deploy.sh prod backup

# 设置定时备份 (crontab)
0 2 * * * /path/to/aqichat/scripts/deploy.sh prod backup
```

### 服务监控
```bash
# 检查服务状态
./scripts/deploy.sh prod status

# 检查资源使用情况
docker stats

# 检查磁盘使用情况
df -h
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新构建并重启服务
./scripts/deploy.sh prod build
./scripts/deploy.sh prod restart
```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查容器日志
   docker-compose logs
   
   # 检查端口占用
   netstat -tulpn | grep :8080
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose ps postgres
   
   # 测试数据库连接
   docker-compose exec postgres pg_isready -U aqichat_user
   ```

3. **WebSocket连接问题**
   ```bash
   # 检查Nginx配置
   docker-compose exec nginx nginx -t
   
   # 重新加载Nginx配置
   docker-compose exec nginx nginx -s reload
   ```

### 性能优化

1. **数据库优化**
   - 定期执行 `VACUUM` 和 `ANALYZE`
   - 监控慢查询日志
   - 适当调整连接池大小

2. **应用优化**
   - 启用 Gzip 压缩
   - 配置适当的缓存策略
   - 监控内存使用情况

3. **网络优化**
   - 使用 CDN 加速静态资源
   - 配置适当的 Keep-Alive 设置
   - 启用 HTTP/2

## 安全建议

1. **定期更新**
   - 保持系统和依赖包最新
   - 定期更新 Docker 镜像

2. **访问控制**
   - 使用强密码和密钥
   - 限制数据库访问权限
   - 配置防火墙规则

3. **数据保护**
   - 定期备份数据
   - 加密敏感数据
   - 监控异常访问

## 扩展部署

### 负载均衡
```yaml
# docker-compose.yml 示例
services:
  backend1:
    # ... 后端服务配置
  backend2:
    # ... 后端服务配置
  
  nginx:
    # ... 负载均衡配置
```

### 数据库集群
- 配置 PostgreSQL 主从复制
- 使用 pgpool-II 进行连接池管理
- 考虑使用 PostgreSQL 集群解决方案

### 容器编排
- 考虑使用 Kubernetes 进行大规模部署
- 使用 Helm Charts 管理配置
- 配置自动扩缩容策略
