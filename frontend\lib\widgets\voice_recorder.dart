import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import '../constants/app_constants.dart';

enum VoiceRecorderState {
  idle,
  recording,
  paused,
  completed,
}

class VoiceRecorder extends StatefulWidget {
  final Function(String audioPath, Duration duration) onRecordingComplete;
  final VoidCallback? onCancel;

  const VoiceRecorder({
    super.key,
    required this.onRecordingComplete,
    this.onCancel,
  });

  @override
  State<VoiceRecorder> createState() => _VoiceRecorderState();
}

class _VoiceRecorderState extends State<VoiceRecorder>
    with TickerProviderStateMixin {
  VoiceRecorderState _state = VoiceRecorderState.idle;
  Duration _recordingDuration = Duration.zero;
  Timer? _timer;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  List<double> _waveformData = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  void _startRecording() {
    setState(() {
      _state = VoiceRecorderState.recording;
      _recordingDuration = Duration.zero;
      _waveformData.clear();
    });

    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _recordingDuration += const Duration(milliseconds: 100);
        
        // 模拟波形数据
        _waveformData.add(_random.nextDouble() * 100);
        if (_waveformData.length > 50) {
          _waveformData.removeAt(0);
        }
      });
      
      // 最大录制时间60秒
      if (_recordingDuration.inSeconds >= 60) {
        _stopRecording();
      }
    });
  }

  void _pauseRecording() {
    setState(() {
      _state = VoiceRecorderState.paused;
    });
    _timer?.cancel();
  }

  void _resumeRecording() {
    setState(() {
      _state = VoiceRecorderState.recording;
    });
    
    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _recordingDuration += const Duration(milliseconds: 100);
        
        // 模拟波形数据
        _waveformData.add(_random.nextDouble() * 100);
        if (_waveformData.length > 50) {
          _waveformData.removeAt(0);
        }
      });
      
      if (_recordingDuration.inSeconds >= 60) {
        _stopRecording();
      }
    });
  }

  void _stopRecording() {
    setState(() {
      _state = VoiceRecorderState.completed;
    });
    _timer?.cancel();
    
    // 模拟音频文件路径
    final audioPath = '/mock/audio/recording_${DateTime.now().millisecondsSinceEpoch}.m4a';
    widget.onRecordingComplete(audioPath, _recordingDuration);
  }

  void _cancelRecording() {
    setState(() {
      _state = VoiceRecorderState.idle;
      _recordingDuration = Duration.zero;
      _waveformData.clear();
    });
    _timer?.cancel();
    widget.onCancel?.call();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // 标题
          Text(
            _state == VoiceRecorderState.idle ? '语音录制' : '录制中',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 30),
          
          // 波形显示
          if (_state != VoiceRecorderState.idle) ...[
            Container(
              height: 80,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: WaveformPainter(_waveformData),
              ),
            ),
            const SizedBox(height: 20),
          ],
          
          // 录制时间
          if (_state != VoiceRecorderState.idle)
            Text(
              _formatDuration(_recordingDuration),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryColor,
              ),
            ),
          const SizedBox(height: 30),
          
          // 控制按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // 取消按钮
              if (_state != VoiceRecorderState.idle)
                GestureDetector(
                  onTap: _cancelRecording,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.red,
                      size: 30,
                    ),
                  ),
                ),
              
              // 主控制按钮
              GestureDetector(
                onTap: () {
                  switch (_state) {
                    case VoiceRecorderState.idle:
                      _startRecording();
                      break;
                    case VoiceRecorderState.recording:
                      _pauseRecording();
                      break;
                    case VoiceRecorderState.paused:
                      _resumeRecording();
                      break;
                    case VoiceRecorderState.completed:
                      _startRecording();
                      break;
                  }
                },
                child: AnimatedBuilder(
                  animation: _state == VoiceRecorderState.recording 
                      ? _pulseAnimation 
                      : const AlwaysStoppedAnimation(1.0),
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: _getButtonColor(),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: _getButtonColor().withValues(alpha: 0.3),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Icon(
                          _getButtonIcon(),
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              // 完成按钮
              if (_state == VoiceRecorderState.paused || 
                  _state == VoiceRecorderState.completed)
                GestureDetector(
                  onTap: _stopRecording,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.green,
                      size: 30,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 20),
          
          // 提示文本
          Text(
            _getHintText(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Color _getButtonColor() {
    switch (_state) {
      case VoiceRecorderState.idle:
        return AppConstants.primaryColor;
      case VoiceRecorderState.recording:
        return Colors.red;
      case VoiceRecorderState.paused:
        return AppConstants.primaryColor;
      case VoiceRecorderState.completed:
        return AppConstants.primaryColor;
    }
  }

  IconData _getButtonIcon() {
    switch (_state) {
      case VoiceRecorderState.idle:
        return Icons.mic;
      case VoiceRecorderState.recording:
        return Icons.pause;
      case VoiceRecorderState.paused:
        return Icons.play_arrow;
      case VoiceRecorderState.completed:
        return Icons.mic;
    }
  }

  String _getHintText() {
    switch (_state) {
      case VoiceRecorderState.idle:
        return '点击麦克风开始录制';
      case VoiceRecorderState.recording:
        return '正在录制中，点击暂停';
      case VoiceRecorderState.paused:
        return '录制已暂停，点击继续';
      case VoiceRecorderState.completed:
        return '录制完成，点击发送';
    }
  }
}

class WaveformPainter extends CustomPainter {
  final List<double> waveformData;

  WaveformPainter(this.waveformData);

  @override
  void paint(Canvas canvas, Size size) {
    if (waveformData.isEmpty) return;

    final paint = Paint()
      ..color = AppConstants.primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.fill;

    final barWidth = size.width / waveformData.length;
    
    for (int i = 0; i < waveformData.length; i++) {
      final barHeight = (waveformData[i] / 100) * size.height;
      final x = i * barWidth;
      final y = (size.height - barHeight) / 2;
      
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth - 1, barHeight),
          const Radius.circular(1),
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
