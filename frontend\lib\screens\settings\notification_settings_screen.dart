import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../services/notification_service.dart';
import '../../widgets/slide_animation.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final NotificationService _notificationService = NotificationService();
  late NotificationSettings _settings;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _settings = _notificationService.settings;
  }

  Future<void> _updateSettings(NotificationSettings newSettings) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _notificationService.updateSettings(newSettings);
      setState(() {
        _settings = newSettings;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('通知设置已更新'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showDoNotDisturbSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '免打扰设置',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            
            SwitchListTile(
              title: const Text('启用免打扰'),
              subtitle: const Text('在指定时间段内不接收通知'),
              value: _settings.enableDoNotDisturb,
              onChanged: (value) {
                _updateSettings(_settings.copyWith(enableDoNotDisturb: value));
              },
              activeColor: AppConstants.primaryColor,
            ),
            
            const Divider(),
            
            const Text(
              '免打扰时间段',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: ListView(
                children: [
                  ListTile(
                    leading: const Icon(Icons.bedtime, color: AppConstants.primaryColor),
                    title: const Text('睡眠时间'),
                    subtitle: const Text('22:00 - 08:00'),
                    trailing: Switch(
                      value: true,
                      onChanged: (value) {
                        // TODO: 实现免打扰时间段管理
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                  ),
                  
                  ListTile(
                    leading: const Icon(Icons.work, color: AppConstants.primaryColor),
                    title: const Text('工作时间'),
                    subtitle: const Text('09:00 - 18:00'),
                    trailing: Switch(
                      value: false,
                      onChanged: (value) {
                        // TODO: 实现免打扰时间段管理
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: 添加自定义免打扰时间段
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('自定义时间段功能开发中'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('添加时间段'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('通知设置'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // 总开关
                SlideInAnimation(
                  begin: const Offset(0.0, -1.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '通知总开关',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          SwitchListTile(
                            title: const Text('接收通知'),
                            subtitle: const Text('关闭后将不会收到任何通知'),
                            value: _settings.enableNotifications,
                            onChanged: (value) {
                              _updateSettings(_settings.copyWith(enableNotifications: value));
                            },
                            activeColor: AppConstants.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // 通知方式
                SlideInAnimation(
                  begin: const Offset(-1.0, 0.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '通知方式',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          SwitchListTile(
                            title: const Text('声音提醒'),
                            subtitle: const Text('播放通知声音'),
                            value: _settings.enableSoundNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableSoundNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('振动提醒'),
                            subtitle: const Text('设备振动提醒'),
                            value: _settings.enableVibrationNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableVibrationNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          ListTile(
                            title: const Text('通知铃声'),
                            subtitle: Text(_settings.notificationSound),
                            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                            onTap: _settings.enableNotifications && _settings.enableSoundNotifications
                                ? () {
                                    // TODO: 显示铃声选择器
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('铃声选择功能开发中'),
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  }
                                : null,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // 消息类型通知
                SlideInAnimation(
                  begin: const Offset(1.0, 0.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '消息类型通知',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          SwitchListTile(
                            title: const Text('文本消息'),
                            subtitle: const Text('文字聊天消息'),
                            value: _settings.enableTextNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableTextNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('图片消息'),
                            subtitle: const Text('图片分享消息'),
                            value: _settings.enableImageNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableImageNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('语音消息'),
                            subtitle: const Text('语音聊天消息'),
                            value: _settings.enableAudioNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableAudioNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('视频消息'),
                            subtitle: const Text('视频分享消息'),
                            value: _settings.enableVideoNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableVideoNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('文件消息'),
                            subtitle: const Text('文件分享消息'),
                            value: _settings.enableFileNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableFileNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // 特殊通知
                SlideInAnimation(
                  begin: const Offset(-1.0, 0.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '特殊通知',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          SwitchListTile(
                            title: const Text('好友请求'),
                            subtitle: const Text('新的好友申请通知'),
                            value: _settings.enableFriendRequestNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableFriendRequestNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('群聊通知'),
                            subtitle: const Text('群聊邀请和消息通知'),
                            value: _settings.enableGroupNotifications,
                            onChanged: _settings.enableNotifications
                                ? (value) {
                                    _updateSettings(_settings.copyWith(enableGroupNotifications: value));
                                  }
                                : null,
                            activeColor: AppConstants.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // 免打扰
                SlideInAnimation(
                  begin: const Offset(1.0, 0.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '免打扰',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          ListTile(
                            leading: const Icon(Icons.do_not_disturb, color: AppConstants.primaryColor),
                            title: const Text('免打扰设置'),
                            subtitle: Text(_settings.enableDoNotDisturb ? '已启用' : '已关闭'),
                            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                            onTap: _showDoNotDisturbSettings,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
