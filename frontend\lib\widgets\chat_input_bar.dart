import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../models/emoji.dart';
import '../models/user.dart';
import 'emoji_picker.dart';

class ChatInputBar extends StatefulWidget {
  final Function(String) onSendMessage;
  final Function(String) onSendVoice;
  final Function(XFile) onSendImage;
  final Function(XFile) onSendVideo;
  final Function(XFile) onSendFile;
  final Function() onSendLocation;
  final Function(User) onSendContact;
  final Function() onStartVoiceCall;
  final Function() onStartVideoCall;

  const ChatInputBar({
    super.key,
    required this.onSendMessage,
    required this.onSendVoice,
    required this.onSendImage,
    required this.onSendVideo,
    required this.onSendFile,
    required this.onSendLocation,
    required this.onSendContact,
    required this.onStartVoiceCall,
    required this.onStartVideoCall,
  });

  @override
  State<ChatInputBar> createState() => _ChatInputBarState();
}

class _ChatInputBarState extends State<ChatInputBar>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ImagePicker _imagePicker = ImagePicker();
  
  bool _isVoiceMode = false;
  bool _showEmojiPicker = false;
  bool _showMoreMenu = false;
  bool _isRecording = false;
  bool _isTyping = false;
  bool _showVoiceGesture = false;
  double _gestureStartY = 0.0;
  double _currentGestureY = 0.0;
  String _gestureHint = '';

  late AnimationController _recordingAnimationController;
  late Animation<double> _recordingAnimation;

  @override
  void initState() {
    super.initState();
    _textController.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
    
    _recordingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _recordingAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _recordingAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _recordingAnimationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _textController.text.trim().isNotEmpty;
    if (hasText != _isTyping) {
      setState(() {
        _isTyping = hasText;
      });
    }
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      setState(() {
        _showEmojiPicker = false;
        _showMoreMenu = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 主输入栏
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey[300]!),
            ),
          ),
          child: Row(
            children: [
              // 左侧：语音/文字切换按钮
              GestureDetector(
                onTap: _toggleInputMode,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _isVoiceMode ? AppConstants.primaryColor : Colors.grey[200],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    _isVoiceMode ? Icons.keyboard : Icons.mic,
                    color: _isVoiceMode ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              
              // 中间：输入区域
              Expanded(
                child: _isVoiceMode ? _buildVoiceInput() : _buildTextInput(),
              ),
              const SizedBox(width: 8),
              
              // 右侧：表情和更多按钮
              Row(
                children: [
                  // 表情按钮
                  GestureDetector(
                    onTap: _toggleEmojiPicker,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _showEmojiPicker ? AppConstants.primaryColor : Colors.grey[200],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        Icons.emoji_emotions,
                        color: _showEmojiPicker ? Colors.white : Colors.grey[600],
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  
                  // 发送/更多按钮
                  _isTyping ? _buildSendButton() : _buildMoreButton(),
                ],
              ),
            ],
          ),
        ),
        
        // 表情选择器
        if (_showEmojiPicker) _buildEmojiPickerPanel(),
        
        // 更多功能菜单
        if (_showMoreMenu) _buildMoreMenuPanel(),
      ],
    );
  }

  Widget _buildTextInput() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 100),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: TextField(
        controller: _textController,
        focusNode: _focusNode,
        maxLines: null,
        textInputAction: TextInputAction.newline,
        decoration: const InputDecoration(
          hintText: '输入消息...',
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        onSubmitted: _sendTextMessage,
      ),
    );
  }

  Widget _buildVoiceInput() {
    return Stack(
      children: [
        GestureDetector(
          onLongPressStart: _startRecording,
          onLongPressEnd: _stopRecording,
          onLongPressCancel: _cancelRecording,
          onLongPressMoveUpdate: _updateRecordingGesture,
          child: AnimatedBuilder(
            animation: _recordingAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isRecording ? _recordingAnimation.value : 1.0,
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: _isRecording ? Colors.red[100] : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _isRecording ? Colors.red : Colors.grey[300]!,
                      width: _isRecording ? 2 : 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      _getVoiceInputText(),
                      style: TextStyle(
                        color: _isRecording ? Colors.red : Colors.grey[600],
                        fontWeight: _isRecording ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // 语音手势提示
        if (_showVoiceGesture)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getGestureIcon(),
                    color: _getGestureColor(),
                    size: 24,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _gestureHint,
                    style: TextStyle(
                      color: _getGestureColor(),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  String _getVoiceInputText() {
    if (!_isRecording) return '长按录音';
    if (_showVoiceGesture) return '';
    return '正在录音...';
  }

  IconData _getGestureIcon() {
    if (_gestureHint.contains('取消')) return Icons.cancel;
    if (_gestureHint.contains('转文字')) return Icons.text_fields;
    return Icons.send;
  }

  Color _getGestureColor() {
    if (_gestureHint.contains('取消')) return Colors.red;
    if (_gestureHint.contains('转文字')) return Colors.blue;
    return Colors.green;
  }

  Widget _buildSendButton() {
    return GestureDetector(
      onTap: () => _sendTextMessage(_textController.text),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppConstants.primaryColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Icon(
          Icons.send,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildMoreButton() {
    return GestureDetector(
      onTap: _toggleMoreMenu,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: _showMoreMenu ? AppConstants.primaryColor : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          Icons.add,
          color: _showMoreMenu ? Colors.white : Colors.grey[600],
          size: 20,
        ),
      ),
    );
  }

  Widget _buildEmojiPickerPanel() {
    return EmojiPicker(
      height: 250,
      onEmojiSelected: _insertEmoji,
      onStickerSelected: _sendSticker,
    );
  }

  Widget _buildMoreMenuPanel() {
    return Container(
      height: 240,
      color: Colors.white,
      child: GridView.count(
        crossAxisCount: 4,
        padding: const EdgeInsets.all(16),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        children: [
          _buildMoreMenuItem(
            icon: Icons.photo_library,
            label: '相册',
            color: Colors.green,
            onTap: _pickImageFromGallery,
          ),
          _buildMoreMenuItem(
            icon: Icons.camera_alt,
            label: '拍照',
            color: Colors.blue,
            onTap: _takePicture,
          ),
          _buildMoreMenuItem(
            icon: Icons.videocam,
            label: '录像',
            color: Colors.red,
            onTap: _recordVideo,
          ),
          _buildMoreMenuItem(
            icon: Icons.attach_file,
            label: '文件',
            color: Colors.orange,
            onTap: _pickFile,
          ),
          _buildMoreMenuItem(
            icon: Icons.phone,
            label: '语音通话',
            color: Colors.green,
            onTap: _startVoiceCall,
          ),
          _buildMoreMenuItem(
            icon: Icons.video_call,
            label: '视频通话',
            color: Colors.blue,
            onTap: _startVideoCall,
          ),
          _buildMoreMenuItem(
            icon: Icons.location_on,
            label: '位置',
            color: Colors.red,
            onTap: _sendLocation,
          ),
          _buildMoreMenuItem(
            icon: Icons.person,
            label: '个人名片',
            color: Colors.purple,
            onTap: _showContactPicker,
          ),
          _buildMoreMenuItem(
            icon: Icons.star,
            label: '收藏',
            color: Colors.amber,
            onTap: _showFavorites,
          ),
          _buildMoreMenuItem(
            icon: Icons.keyboard_voice,
            label: '语音转文字',
            color: Colors.teal,
            onTap: _startVoiceToText,
          ),
        ],
      ),
    );
  }

  Widget _buildMoreMenuItem({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _toggleInputMode() {
    setState(() {
      _isVoiceMode = !_isVoiceMode;
      if (_isVoiceMode) {
        _focusNode.unfocus();
      }
    });
  }

  void _toggleEmojiPicker() {
    setState(() {
      _showEmojiPicker = !_showEmojiPicker;
      _showMoreMenu = false;
      if (_showEmojiPicker) {
        _focusNode.unfocus();
      }
    });
  }

  void _toggleMoreMenu() {
    setState(() {
      _showMoreMenu = !_showMoreMenu;
      _showEmojiPicker = false;
      if (_showMoreMenu) {
        _focusNode.unfocus();
      }
    });
  }

  void _sendTextMessage(String text) {
    if (text.trim().isNotEmpty) {
      widget.onSendMessage(text.trim());
      _textController.clear();
    }
  }

  void _insertEmoji(String emoji) {
    final text = _textController.text;
    final selection = _textController.selection;
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      emoji,
    );
    _textController.text = newText;
    _textController.selection = TextSelection.collapsed(
      offset: selection.start + emoji.length,
    );
  }

  void _sendSticker(StickerItem sticker) {
    // 发送贴纸消息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('发送贴纸: ${sticker.name}'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _startRecording(LongPressStartDetails details) {
    setState(() {
      _isRecording = true;
      _showVoiceGesture = false;
      _gestureStartY = details.globalPosition.dy;
      _currentGestureY = details.globalPosition.dy;
      _gestureHint = '';
    });
    _recordingAnimationController.repeat(reverse: true);

    // 开始录音逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('开始录音...'),
        duration: Duration(milliseconds: 500),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _updateRecordingGesture(LongPressMoveUpdateDetails details) {
    if (!_isRecording) return;

    setState(() {
      _currentGestureY = details.globalPosition.dy;
      final deltaY = _gestureStartY - _currentGestureY;

      if (deltaY > 50) {
        _showVoiceGesture = true;
        final deltaX = details.globalPosition.dx - MediaQuery.of(context).size.width / 2;

        if (deltaX < -50) {
          _gestureHint = '松开取消';
        } else if (deltaX > 50) {
          _gestureHint = '松开转文字';
        } else {
          _gestureHint = '松开发送';
        }
      } else {
        _showVoiceGesture = false;
        _gestureHint = '';
      }
    });
  }

  void _stopRecording(LongPressEndDetails details) {
    final deltaY = _gestureStartY - _currentGestureY;
    final deltaX = details.globalPosition.dx - MediaQuery.of(context).size.width / 2;

    setState(() {
      _isRecording = false;
      _showVoiceGesture = false;
    });
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();

    if (deltaY > 50) {
      // 上滑手势
      if (deltaX < -50) {
        // 上滑左边 - 取消
        _cancelVoiceMessage();
      } else if (deltaX > 50) {
        // 上滑右边 - 语音转文字
        _convertVoiceToText();
      } else {
        // 上滑中间 - 发送
        _sendVoiceMessage();
      }
    } else {
      // 正常松开 - 发送
      _sendVoiceMessage();
    }
  }

  void _cancelRecording() {
    setState(() {
      _isRecording = false;
      _showVoiceGesture = false;
    });
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();
    _cancelVoiceMessage();
  }

  void _sendVoiceMessage() {
    widget.onSendVoice('voice_message_path');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('语音消息已发送'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _cancelVoiceMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('录音已取消'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _convertVoiceToText() {
    // 模拟语音转文字
    const convertedText = '这是语音转换的文字内容';
    _textController.text = convertedText;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('语音已转换为文字'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _pickImageFromGallery() async {
    final XFile? image = await _imagePicker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      widget.onSendImage(image);
    }
    setState(() {
      _showMoreMenu = false;
    });
  }

  void _startVoiceCall() {
    widget.onStartVoiceCall();
    setState(() {
      _showMoreMenu = false;
    });
  }

  void _startVideoCall() {
    widget.onStartVideoCall();
    setState(() {
      _showMoreMenu = false;
    });
  }

  void _sendLocation() {
    widget.onSendLocation();
    setState(() {
      _showMoreMenu = false;
    });
  }

  void _takePicture() async {
    final XFile? image = await _imagePicker.pickImage(source: ImageSource.camera);
    if (image != null) {
      widget.onSendImage(image);
    }
  }

  void _recordVideo() async {
    setState(() {
      _showMoreMenu = false;
    });

    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 5), // 限制5分钟
      );

      if (video != null && mounted) {
        // 检查视频时长
        final file = await video.readAsBytes();
        final sizeInMB = file.length / (1024 * 1024);

        if (sizeInMB > 100) { // 假设100MB为上限
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('视频文件过大，请录制较短的视频'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
          return;
        }

        widget.onSendVideo(video);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('视频录制完成'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('录制失败: ${e.toString()}'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _pickFile() {
    // 文件选择逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('文件选择功能即将推出'),
        behavior: SnackBarBehavior.floating,
      ),
    );
    setState(() {
      _showMoreMenu = false;
    });
  }

  void _showContactPicker() {
    setState(() {
      _showMoreMenu = false;
    });

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '分享名片',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 20),

            ListTile(
              leading: const CircleAvatar(
                backgroundColor: AppConstants.primaryColor,
                child: Icon(Icons.person, color: Colors.white),
              ),
              title: const Text('我的名片'),
              subtitle: const Text('分享我的个人信息'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.of(context).pop();
                _shareMyContact();
              },
            ),

            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.blue,
                child: Icon(Icons.people, color: Colors.white),
              ),
              title: const Text('好友名片'),
              subtitle: const Text('选择好友名片分享'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.of(context).pop();
                _showFriendsList();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _shareMyContact() {
    // 模拟分享自己的名片
    final myContact = User(
      id: 1,
      username: 'current_user',
      email: '<EMAIL>',
      nickname: '当前用户',
      bio: '这是我的个人简介',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    widget.onSendContact(myContact);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已分享我的名片'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showFriendsList() {
    // 模拟好友列表
    final friends = List.generate(5, (index) => User(
      id: index + 2,
      username: 'friend$index',
      email: 'friend$<EMAIL>',
      nickname: '好友$index',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ));

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        height: 400,
        child: Column(
          children: [
            const Text(
              '选择好友',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 20),

            Expanded(
              child: ListView.builder(
                itemCount: friends.length,
                itemBuilder: (context, index) {
                  final friend = friends[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppConstants.primaryColor,
                      child: Text(
                        friend.username[0].toUpperCase(),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(friend.nickname ?? friend.username),
                    subtitle: Text('@${friend.username}'),
                    onTap: () {
                      Navigator.of(context).pop();
                      widget.onSendContact(friend);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('已分享 ${friend.nickname ?? friend.username} 的名片'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFavorites() {
    setState(() {
      _showMoreMenu = false;
    });

    // 模拟收藏内容
    final favoriteItems = [
      {'type': 'text', 'content': '这是一条收藏的文字消息', 'time': '2024-01-01'},
      {'type': 'image', 'content': '收藏的图片.jpg', 'time': '2024-01-02'},
      {'type': 'link', 'content': 'https://example.com', 'time': '2024-01-03'},
      {'type': 'file', 'content': '重要文档.pdf', 'time': '2024-01-04'},
    ];

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        height: 500,
        child: Column(
          children: [
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '我的收藏',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 20),

            Expanded(
              child: favoriteItems.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.star_border, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            '暂无收藏内容',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: favoriteItems.length,
                      itemBuilder: (context, index) {
                        final item = favoriteItems[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: _getFavoriteTypeColor(item['type']!),
                              child: Icon(
                                _getFavoriteTypeIcon(item['type']!),
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            title: Text(
                              item['content']!,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            subtitle: Text('收藏时间: ${item['time']}'),
                            trailing: PopupMenuButton<String>(
                              onSelected: (action) => _handleFavoriteAction(action, item),
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'send',
                                  child: Row(
                                    children: [
                                      Icon(Icons.send),
                                      SizedBox(width: 8),
                                      Text('发送'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'remove',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text('移除', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            onTap: () {
                              Navigator.of(context).pop();
                              _sendFavoriteItem(item);
                            },
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getFavoriteTypeColor(String type) {
    switch (type) {
      case 'text':
        return Colors.blue;
      case 'image':
        return Colors.green;
      case 'link':
        return Colors.orange;
      case 'file':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getFavoriteTypeIcon(String type) {
    switch (type) {
      case 'text':
        return Icons.text_fields;
      case 'image':
        return Icons.image;
      case 'link':
        return Icons.link;
      case 'file':
        return Icons.attach_file;
      default:
        return Icons.star;
    }
  }

  void _handleFavoriteAction(String action, Map<String, String> item) {
    switch (action) {
      case 'send':
        _sendFavoriteItem(item);
        break;
      case 'remove':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已移除收藏: ${item['content']}'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
    }
  }

  void _sendFavoriteItem(Map<String, String> item) {
    widget.onSendMessage(item['content']!);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已发送收藏内容'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _startVoiceToText() {
    setState(() {
      _showMoreMenu = false;
    });

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('语音转文字'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.mic,
              size: 64,
              color: AppConstants.primaryColor,
            ),
            const SizedBox(height: 16),
            const Text('点击开始录音，系统将自动转换为文字'),
            const SizedBox(height: 20),

            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _performVoiceToText();
              },
              icon: const Icon(Icons.mic),
              label: const Text('开始录音'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  void _performVoiceToText() {
    // 模拟语音转文字过程
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在录音并转换...'),
          ],
        ),
      ),
    );

    // 模拟录音和转换过程
    Future.delayed(const Duration(seconds: 3), () {
      if (!mounted) return;

      Navigator.of(context).pop(); // 关闭加载对话框

      // 模拟转换结果
      const convertedText = '这是通过语音转换得到的文字内容，您可以编辑后发送。';
      _textController.text = convertedText;

      // 显示结果对话框
      if (mounted) {
        showDialog(
          context: context,
        builder: (context) => AlertDialog(
          title: const Text('转换完成'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 48),
              const SizedBox(height: 16),
              const Text('语音已成功转换为文字'),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  convertedText,
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _textController.clear();
              },
              child: const Text('重新录音'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 文字已经填入输入框，用户可以编辑
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('使用文字'),
            ),
          ],
        ),
      );
      }
    });
  }
}
