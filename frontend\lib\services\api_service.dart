import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/user.dart';
import '../models/message.dart';

part 'api_service.g.dart';

@RestApi(baseUrl: "http://localhost:8080/api/v1")
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  // 认证相关
  @POST("/auth/register")
  Future<LoginResponse> register(@Body() RegisterRequest request);

  @POST("/auth/login")
  Future<LoginResponse> login(@Body() LoginRequest request);

  @POST("/auth/refresh")
  Future<LoginResponse> refreshToken(@Header("Authorization") String token);

  // 用户相关
  @GET("/users/profile")
  Future<User> getProfile();

  @PUT("/users/profile")
  Future<User> updateProfile(@Body() Map<String, dynamic> request);

  @GET("/users/search")
  Future<List<User>> searchUsers(@Query("query") String query, @Query("limit") int limit);

  // 好友相关
  @GET("/friends")
  Future<List<User>> getFriends();

  @POST("/friends/request")
  Future<FriendRequest> sendFriendRequest(@Body() Map<String, dynamic> request);

  @PUT("/friends/request/{id}/accept")
  Future<FriendRequest> acceptFriendRequest(@Path("id") int id);

  @PUT("/friends/request/{id}/reject")
  Future<FriendRequest> rejectFriendRequest(@Path("id") int id);

  @DELETE("/friends/{id}")
  Future<void> removeFriend(@Path("id") int id);

  // 消息相关
  @GET("/messages/conversations")
  Future<List<Conversation>> getConversations();

  @GET("/messages/conversations/{id}")
  Future<List<Message>> getMessages(
    @Path("id") int conversationId,
    @Query("page") int page,
    @Query("limit") int limit,
  );

  @POST("/messages/send")
  Future<Message> sendMessage(@Body() SendMessageRequest request);

  @PUT("/messages/{id}/read")
  Future<void> markAsRead(@Path("id") int messageId);
}

class ApiClient {
  static const String baseUrl = "http://10.0.2.2:8080/api/v1";  // Android模拟器访问宿主机
  static const String wsUrl = "ws://10.0.2.2:8080/ws";
  
  late final Dio _dio;
  late final ApiService _apiService;
  
  String? _accessToken;
  String? _refreshToken;

  ApiClient() {
    _dio = Dio();
    _setupInterceptors();
    _apiService = ApiService(_dio, baseUrl: baseUrl);
  }

  ApiService get api => _apiService;
  String? get accessToken => _accessToken;
  String? get refreshToken => _refreshToken;

  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_accessToken != null) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        // 处理401错误，尝试刷新token
        if (error.response?.statusCode == 401 && _refreshToken != null) {
          try {
            final response = await _refreshAccessToken();
            if (response != null) {
              // 重试原请求
              final opts = error.requestOptions;
              opts.headers['Authorization'] = 'Bearer $_accessToken';
              final cloneReq = await _dio.fetch(opts);
              return handler.resolve(cloneReq);
            }
          } catch (e) {
            // 刷新失败，清除token
            clearTokens();
          }
        }
        handler.next(error);
      },
    ));

    // 日志拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));
  }

  void setTokens(String accessToken, String refreshToken) {
    _accessToken = accessToken;
    _refreshToken = refreshToken;
  }

  void clearTokens() {
    _accessToken = null;
    _refreshToken = null;
  }

  Future<LoginResponse?> _refreshAccessToken() async {
    if (_refreshToken == null) return null;
    
    try {
      final response = await _apiService.refreshToken('Bearer $_refreshToken');
      setTokens(response.accessToken, response.refreshToken);
      return response;
    } catch (e) {
      return null;
    }
  }

  bool get isAuthenticated => _accessToken != null;
}
