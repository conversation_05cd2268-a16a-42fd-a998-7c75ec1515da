import 'package:dio/dio.dart';
import '../models/user.dart';
import '../models/message.dart';

// 简化的API客户端，用于演示
class ApiClient {
  static const String baseUrl = "http://10.0.2.2:8080/api/v1";  // Android模拟器访问宿主机
  static const String wsUrl = "ws://10.0.2.2:8080/ws";
  
  late final Dio _dio;
  
  String? _accessToken;
  String? _refreshToken;

  ApiClient() {
    _dio = Dio();
    _setupInterceptors();
  }

  String? get accessToken => _accessToken;
  String? get refreshToken => _refreshToken;

  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_accessToken != null) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        // 处理401错误，尝试刷新token
        if (error.response?.statusCode == 401 && _refreshToken != null) {
          // 在真实环境中，这里会尝试刷新token
          clearTokens();
        }
        handler.next(error);
      },
    ));

    // 日志拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));
  }

  void setTokens(String accessToken, String refreshToken) {
    _accessToken = accessToken;
    _refreshToken = refreshToken;
  }

  void clearTokens() {
    _accessToken = null;
    _refreshToken = null;
  }

  bool get isAuthenticated => _accessToken != null;

  // 简化的API方法（在真实环境中会实现HTTP请求）
  Future<LoginResponse> register(RegisterRequest request) async {
    // 这里应该发送HTTP请求到后端
    // 现在返回模拟数据
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<LoginResponse> login(LoginRequest request) async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<User> getProfile() async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<List<Conversation>> getConversations() async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<List<Message>> getMessages(int conversationId, int page, int limit) async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<Message> sendMessage(SendMessageRequest request) async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<void> markAsRead(int messageId) async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<List<User>> searchUsers(String query, int limit) async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }

  Future<User> updateProfile(Map<String, dynamic> request) async {
    // 这里应该发送HTTP请求到后端
    throw UnimplementedError('Use MockApiService for demo');
  }
}
