import 'package:flutter/material.dart';

class AppConstants {
  // API配置
  static const String baseUrl = 'http://localhost:8080/api/v1';
  static const String wsUrl = 'ws://localhost:8080/ws';
  
  // 应用信息
  static const String appName = 'AqiChat';
  static const String appVersion = '1.0.0';
  
  // 颜色主题
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color primaryColorDark = Color(0xFF1976D2);
  static const Color primaryColorLight = Color(0xFFBBDEFB);
  static const Color accentColor = Color(0xFF03DAC6);
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFB00020);
  
  // 文本颜色
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // 消息气泡颜色
  static const Color sentMessageColor = Color(0xFF2196F3);
  static const Color receivedMessageColor = Color(0xFFE0E0E0);
  static const Color sentMessageTextColor = Color(0xFFFFFFFF);
  static const Color receivedMessageTextColor = Color(0xFF212121);
  
  // 在线状态颜色
  static const Color onlineColor = Color(0xFF4CAF50);
  static const Color offlineColor = Color(0xFF9E9E9E);
  static const Color awayColor = Color(0xFFFF9800);
  static const Color busyColor = Color(0xFFF44336);
  
  // 尺寸
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  static const double avatarRadius = 20.0;
  static const double largeAvatarRadius = 40.0;
  
  // 动画时长
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // 分页
  static const int defaultPageSize = 50;
  static const int maxPageSize = 100;
  
  // 消息限制
  static const int maxMessageLength = 1000;
  static const int maxNicknameLength = 50;
  static const int maxUsernameLength = 20;
  static const int minPasswordLength = 6;
  
  // 文件大小限制（字节）
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB
  
  // 支持的图片格式
  static const List<String> supportedImageFormats = [
    'jpg', 'jpeg', 'png', 'gif', 'webp'
  ];
  
  // 支持的文件格式
  static const List<String> supportedFileFormats = [
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar'
  ];
}

class AppStrings {
  // 通用
  static const String ok = '确定';
  static const String cancel = '取消';
  static const String save = '保存';
  static const String delete = '删除';
  static const String edit = '编辑';
  static const String send = '发送';
  static const String loading = '加载中...';
  static const String retry = '重试';
  static const String error = '错误';
  static const String success = '成功';
  
  // 认证
  static const String login = '登录';
  static const String register = '注册';
  static const String logout = '退出登录';
  static const String username = '用户名';
  static const String email = '邮箱';
  static const String password = '密码';
  static const String confirmPassword = '确认密码';
  static const String nickname = '昵称';
  static const String loginSuccess = '登录成功';
  static const String registerSuccess = '注册成功';
  static const String loginFailed = '登录失败';
  static const String registerFailed = '注册失败';
  
  // 聊天
  static const String chats = '聊天';
  static const String friends = '好友';
  static const String profile = '个人资料';
  static const String settings = '设置';
  static const String typeMessage = '输入消息...';
  static const String online = '在线';
  static const String offline = '离线';
  static const String away = '离开';
  static const String busy = '忙碌';
  static const String typing = '正在输入...';
  static const String lastSeen = '最后在线';
  
  // 好友
  static const String addFriend = '添加好友';
  static const String removeFriend = '删除好友';
  static const String friendRequest = '好友请求';
  static const String acceptRequest = '接受';
  static const String rejectRequest = '拒绝';
  static const String searchFriends = '搜索好友';
  static const String noFriends = '暂无好友';
  static const String noConversations = '暂无聊天记录';
  
  // 错误消息
  static const String networkError = '网络连接错误';
  static const String serverError = '服务器错误';
  static const String unknownError = '未知错误';
  static const String invalidCredentials = '用户名或密码错误';
  static const String userNotFound = '用户不存在';
  static const String emailAlreadyExists = '邮箱已存在';
  static const String usernameAlreadyExists = '用户名已存在';
  
  // 验证消息
  static const String usernameRequired = '请输入用户名';
  static const String emailRequired = '请输入邮箱';
  static const String passwordRequired = '请输入密码';
  static const String invalidEmail = '邮箱格式不正确';
  static const String passwordTooShort = '密码至少6位';
  static const String passwordNotMatch = '两次输入的密码不一致';
  static const String messageEmpty = '消息不能为空';
  static const String messageTooLong = '消息过长';
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: AppConstants.textOnPrimary,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: AppConstants.textOnPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.dark,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.primaryColorDark,
        foregroundColor: AppConstants.textOnPrimary,
        elevation: 0,
      ),
    );
  }
}
