import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';
import 'online_status_indicator.dart';

class UserAvatar extends StatelessWidget {
  final User user;
  final double radius;
  final bool showOnlineStatus;
  final VoidCallback? onTap;

  const UserAvatar({
    super.key,
    required this.user,
    this.radius = AppConstants.avatarRadius,
    this.showOnlineStatus = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          CircleAvatar(
            radius: radius,
            backgroundColor: AppConstants.primaryColorLight,
            backgroundImage: user.avatar != null && user.avatar!.isNotEmpty
                ? CachedNetworkImageProvider(user.avatar!)
                : null,
            child: user.avatar == null || user.avatar!.isEmpty
                ? Text(
                    _getInitials(user.displayName),
                    style: TextStyle(
                      fontSize: radius * 0.6,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.primaryColor,
                    ),
                  )
                : null,
          ),
          if (showOnlineStatus)
            Positioned(
              right: 0,
              bottom: 0,
              child: OnlineStatusIndicator(
                status: user.status,
                size: radius * 0.6,
                showAnimation: user.status == UserStatus.online,
              ),
            ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return (words[0].substring(0, 1) + words[1].substring(0, 1)).toUpperCase();
    }
  }


}
