import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';

class UserAvatar extends StatelessWidget {
  final User user;
  final double radius;
  final bool showOnlineStatus;
  final VoidCallback? onTap;

  const UserAvatar({
    super.key,
    required this.user,
    this.radius = AppConstants.avatarRadius,
    this.showOnlineStatus = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          CircleAvatar(
            radius: radius,
            backgroundColor: AppConstants.primaryColorLight,
            backgroundImage: user.avatar != null && user.avatar!.isNotEmpty
                ? CachedNetworkImageProvider(user.avatar!)
                : null,
            child: user.avatar == null || user.avatar!.isEmpty
                ? Text(
                    _getInitials(user.displayName),
                    style: TextStyle(
                      fontSize: radius * 0.6,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.primaryColor,
                    ),
                  )
                : null,
          ),
          if (showOnlineStatus)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: radius * 0.6,
                height: radius * 0.6,
                decoration: BoxDecoration(
                  color: _getStatusColor(user.status),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return (words[0].substring(0, 1) + words[1].substring(0, 1)).toUpperCase();
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return AppConstants.onlineColor;
      case UserStatus.away:
        return AppConstants.awayColor;
      case UserStatus.busy:
        return AppConstants.busyColor;
      case UserStatus.offline:
        return AppConstants.offlineColor;
    }
  }
}
