class EmojiCategory {
  final String id;
  final String name;
  final String icon;
  final List<EmojiItem> emojis;

  EmojiCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.emojis,
  });
}

class EmojiItem {
  final String id;
  final String emoji;
  final String name;
  final List<String> keywords;
  final bool isCustom;
  final String? imageUrl;

  EmojiItem({
    required this.id,
    required this.emoji,
    required this.name,
    required this.keywords,
    this.isCustom = false,
    this.imageUrl,
  });
}

class StickerPack {
  final String id;
  final String name;
  final String thumbnail;
  final List<StickerItem> stickers;
  final bool isPremium;
  final bool isDownloaded;

  StickerPack({
    required this.id,
    required this.name,
    required this.thumbnail,
    required this.stickers,
    this.isPremium = false,
    this.isDownloaded = false,
  });
}

class StickerItem {
  final String id;
  final String name;
  final String imageUrl;
  final String packId;
  final List<String> tags;

  StickerItem({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.packId,
    required this.tags,
  });
}

// 预定义的表情包数据
class EmojiData {
  static List<EmojiCategory> getDefaultCategories() {
    return [
      EmojiCategory(
        id: 'recent',
        name: '最近使用',
        icon: '🕒',
        emojis: [],
      ),
      EmojiCategory(
        id: 'smileys',
        name: '笑脸',
        icon: '😀',
        emojis: [
          EmojiItem(id: '1', emoji: '😀', name: '开心', keywords: ['开心', '笑']),
          EmojiItem(id: '2', emoji: '😃', name: '大笑', keywords: ['大笑', '高兴']),
          EmojiItem(id: '3', emoji: '😄', name: '笑眯眯', keywords: ['笑眯眯', '开心']),
          EmojiItem(id: '4', emoji: '😁', name: '咧嘴笑', keywords: ['咧嘴笑', '开心']),
          EmojiItem(id: '5', emoji: '😆', name: '哈哈', keywords: ['哈哈', '大笑']),
          EmojiItem(id: '6', emoji: '😅', name: '苦笑', keywords: ['苦笑', '尴尬']),
          EmojiItem(id: '7', emoji: '🤣', name: '笑哭', keywords: ['笑哭', '爆笑']),
          EmojiItem(id: '8', emoji: '😂', name: '笑出眼泪', keywords: ['笑出眼泪', '爆笑']),
          EmojiItem(id: '9', emoji: '🙂', name: '微笑', keywords: ['微笑', '开心']),
          EmojiItem(id: '10', emoji: '🙃', name: '倒脸', keywords: ['倒脸', '无奈']),
          EmojiItem(id: '11', emoji: '😉', name: '眨眼', keywords: ['眨眼', '调皮']),
          EmojiItem(id: '12', emoji: '😊', name: '害羞', keywords: ['害羞', '开心']),
          EmojiItem(id: '13', emoji: '😇', name: '天使', keywords: ['天使', '纯洁']),
          EmojiItem(id: '14', emoji: '🥰', name: '爱心眼', keywords: ['爱心眼', '喜欢']),
          EmojiItem(id: '15', emoji: '😍', name: '花痴', keywords: ['花痴', '喜欢']),
          EmojiItem(id: '16', emoji: '🤩', name: '星星眼', keywords: ['星星眼', '崇拜']),
        ],
      ),
      EmojiCategory(
        id: 'emotions',
        name: '情感',
        icon: '❤️',
        emojis: [
          EmojiItem(id: '17', emoji: '😘', name: '飞吻', keywords: ['飞吻', '爱']),
          EmojiItem(id: '18', emoji: '😗', name: '亲吻', keywords: ['亲吻', '爱']),
          EmojiItem(id: '19', emoji: '☺️', name: '害羞', keywords: ['害羞', '开心']),
          EmojiItem(id: '20', emoji: '😚', name: '闭眼亲吻', keywords: ['闭眼亲吻', '爱']),
          EmojiItem(id: '21', emoji: '😙', name: '亲亲', keywords: ['亲亲', '爱']),
          EmojiItem(id: '22', emoji: '🥲', name: '含泪微笑', keywords: ['含泪微笑', '感动']),
          EmojiItem(id: '23', emoji: '😋', name: '美味', keywords: ['美味', '好吃']),
          EmojiItem(id: '24', emoji: '😛', name: '吐舌头', keywords: ['吐舌头', '调皮']),
          EmojiItem(id: '25', emoji: '😜', name: '眨眼吐舌', keywords: ['眨眼吐舌', '调皮']),
          EmojiItem(id: '26', emoji: '🤪', name: '疯狂', keywords: ['疯狂', '搞怪']),
          EmojiItem(id: '27', emoji: '😝', name: '吐舌闭眼', keywords: ['吐舌闭眼', '调皮']),
          EmojiItem(id: '28', emoji: '🤑', name: '财迷', keywords: ['财迷', '金钱']),
          EmojiItem(id: '29', emoji: '🤗', name: '拥抱', keywords: ['拥抱', '温暖']),
          EmojiItem(id: '30', emoji: '🤭', name: '捂嘴笑', keywords: ['捂嘴笑', '偷笑']),
          EmojiItem(id: '31', emoji: '🤫', name: '嘘', keywords: ['嘘', '安静']),
          EmojiItem(id: '32', emoji: '🤔', name: '思考', keywords: ['思考', '疑问']),
        ],
      ),
      EmojiCategory(
        id: 'gestures',
        name: '手势',
        icon: '👋',
        emojis: [
          EmojiItem(id: '33', emoji: '👋', name: '挥手', keywords: ['挥手', '再见']),
          EmojiItem(id: '34', emoji: '🤚', name: '举手', keywords: ['举手', '停止']),
          EmojiItem(id: '35', emoji: '🖐️', name: '张开手', keywords: ['张开手', '五']),
          EmojiItem(id: '36', emoji: '✋', name: '举手', keywords: ['举手', '停']),
          EmojiItem(id: '37', emoji: '🖖', name: '瓦肯礼', keywords: ['瓦肯礼', '星际迷航']),
          EmojiItem(id: '38', emoji: '👌', name: 'OK', keywords: ['OK', '好的']),
          EmojiItem(id: '39', emoji: '🤌', name: '意大利手势', keywords: ['意大利手势', '什么']),
          EmojiItem(id: '40', emoji: '🤏', name: '捏', keywords: ['捏', '一点点']),
          EmojiItem(id: '41', emoji: '✌️', name: '胜利', keywords: ['胜利', '和平']),
          EmojiItem(id: '42', emoji: '🤞', name: '交叉手指', keywords: ['交叉手指', '祈祷']),
          EmojiItem(id: '43', emoji: '🤟', name: '我爱你', keywords: ['我爱你', '手语']),
          EmojiItem(id: '44', emoji: '🤘', name: '摇滚', keywords: ['摇滚', '酷']),
          EmojiItem(id: '45', emoji: '🤙', name: '打电话', keywords: ['打电话', '联系']),
          EmojiItem(id: '46', emoji: '👈', name: '左指', keywords: ['左指', '指向']),
          EmojiItem(id: '47', emoji: '👉', name: '右指', keywords: ['右指', '指向']),
          EmojiItem(id: '48', emoji: '👆', name: '上指', keywords: ['上指', '指向']),
        ],
      ),
      EmojiCategory(
        id: 'animals',
        name: '动物',
        icon: '🐶',
        emojis: [
          EmojiItem(id: '49', emoji: '🐶', name: '小狗', keywords: ['小狗', '狗']),
          EmojiItem(id: '50', emoji: '🐱', name: '小猫', keywords: ['小猫', '猫']),
          EmojiItem(id: '51', emoji: '🐭', name: '老鼠', keywords: ['老鼠', '鼠']),
          EmojiItem(id: '52', emoji: '🐹', name: '仓鼠', keywords: ['仓鼠', '可爱']),
          EmojiItem(id: '53', emoji: '🐰', name: '兔子', keywords: ['兔子', '可爱']),
          EmojiItem(id: '54', emoji: '🦊', name: '狐狸', keywords: ['狐狸', '聪明']),
          EmojiItem(id: '55', emoji: '🐻', name: '熊', keywords: ['熊', '可爱']),
          EmojiItem(id: '56', emoji: '🐼', name: '熊猫', keywords: ['熊猫', '中国']),
          EmojiItem(id: '57', emoji: '🐨', name: '考拉', keywords: ['考拉', '澳洲']),
          EmojiItem(id: '58', emoji: '🐯', name: '老虎', keywords: ['老虎', '威猛']),
          EmojiItem(id: '59', emoji: '🦁', name: '狮子', keywords: ['狮子', '王者']),
          EmojiItem(id: '60', emoji: '🐮', name: '牛', keywords: ['牛', '力量']),
          EmojiItem(id: '61', emoji: '🐷', name: '猪', keywords: ['猪', '可爱']),
          EmojiItem(id: '62', emoji: '🐸', name: '青蛙', keywords: ['青蛙', '绿色']),
          EmojiItem(id: '63', emoji: '🐵', name: '猴子', keywords: ['猴子', '调皮']),
          EmojiItem(id: '64', emoji: '🙈', name: '非礼勿视', keywords: ['非礼勿视', '害羞']),
        ],
      ),
      EmojiCategory(
        id: 'food',
        name: '食物',
        icon: '🍎',
        emojis: [
          EmojiItem(id: '65', emoji: '🍎', name: '苹果', keywords: ['苹果', '水果']),
          EmojiItem(id: '66', emoji: '🍊', name: '橙子', keywords: ['橙子', '水果']),
          EmojiItem(id: '67', emoji: '🍋', name: '柠檬', keywords: ['柠檬', '酸']),
          EmojiItem(id: '68', emoji: '🍌', name: '香蕉', keywords: ['香蕉', '水果']),
          EmojiItem(id: '69', emoji: '🍉', name: '西瓜', keywords: ['西瓜', '夏天']),
          EmojiItem(id: '70', emoji: '🍇', name: '葡萄', keywords: ['葡萄', '紫色']),
          EmojiItem(id: '71', emoji: '🍓', name: '草莓', keywords: ['草莓', '甜']),
          EmojiItem(id: '72', emoji: '🫐', name: '蓝莓', keywords: ['蓝莓', '健康']),
          EmojiItem(id: '73', emoji: '🍑', name: '樱桃', keywords: ['樱桃', '红色']),
          EmojiItem(id: '74', emoji: '🍒', name: '樱桃', keywords: ['樱桃', '甜']),
          EmojiItem(id: '75', emoji: '🥭', name: '芒果', keywords: ['芒果', '热带']),
          EmojiItem(id: '76', emoji: '🍍', name: '菠萝', keywords: ['菠萝', '热带']),
          EmojiItem(id: '77', emoji: '🥥', name: '椰子', keywords: ['椰子', '热带']),
          EmojiItem(id: '78', emoji: '🥝', name: '猕猴桃', keywords: ['猕猴桃', '绿色']),
          EmojiItem(id: '79', emoji: '🍅', name: '番茄', keywords: ['番茄', '红色']),
          EmojiItem(id: '80', emoji: '🍆', name: '茄子', keywords: ['茄子', '紫色']),
        ],
      ),
    ];
  }

  static List<StickerPack> getDefaultStickerPacks() {
    return [
      StickerPack(
        id: 'classic',
        name: '经典表情包',
        thumbnail: 'assets/stickers/classic/thumbnail.png',
        isDownloaded: true,
        stickers: List.generate(20, (index) => StickerItem(
          id: 'classic_$index',
          name: '经典表情$index',
          imageUrl: 'assets/stickers/classic/sticker_$index.png',
          packId: 'classic',
          tags: ['经典', '常用'],
        )),
      ),
      StickerPack(
        id: 'cute_animals',
        name: '可爱动物',
        thumbnail: 'assets/stickers/animals/thumbnail.png',
        isDownloaded: true,
        stickers: List.generate(15, (index) => StickerItem(
          id: 'animals_$index',
          name: '动物表情$index',
          imageUrl: 'assets/stickers/animals/sticker_$index.png',
          packId: 'cute_animals',
          tags: ['动物', '可爱'],
        )),
      ),
      StickerPack(
        id: 'funny_memes',
        name: '搞笑表情',
        thumbnail: 'assets/stickers/memes/thumbnail.png',
        isPremium: true,
        isDownloaded: false,
        stickers: List.generate(25, (index) => StickerItem(
          id: 'memes_$index',
          name: '搞笑表情$index',
          imageUrl: 'assets/stickers/memes/sticker_$index.png',
          packId: 'funny_memes',
          tags: ['搞笑', '流行'],
        )),
      ),
    ];
  }
}
