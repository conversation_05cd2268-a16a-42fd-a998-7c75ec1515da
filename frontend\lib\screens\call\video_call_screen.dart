import 'package:flutter/material.dart';
import 'dart:async';
import '../../constants/app_constants.dart';
import '../../services/video_call_service.dart';
import '../../widgets/user_avatar.dart';

class VideoCallScreen extends StatefulWidget {
  final CallSession session;

  const VideoCallScreen({
    super.key,
    required this.session,
  });

  @override
  State<VideoCallScreen> createState() => _VideoCallScreenState();
}

class _VideoCallScreenState extends State<VideoCallScreen>
    with TickerProviderStateMixin {
  final VideoCallService _callService = VideoCallService();
  late CallSession _session;
  Timer? _durationTimer;
  Duration _callDuration = Duration.zero;
  bool _isControlsVisible = true;
  Timer? _controlsTimer;
  
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _session = widget.session;
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    if (_session.status == CallStatus.ringing || _session.status == CallStatus.connecting) {
      _pulseController.repeat(reverse: true);
    }

    _startDurationTimer();
    _startControlsTimer();
    _listenToCallEvents();
  }

  @override
  void dispose() {
    _durationTimer?.cancel();
    _controlsTimer?.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  void _startDurationTimer() {
    if (_session.connectedTime != null) {
      _durationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _callDuration = _session.duration;
          });
        }
      });
    }
  }

  void _startControlsTimer() {
    _controlsTimer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isControlsVisible = false;
        });
      }
    });
  }

  void _resetControlsTimer() {
    _controlsTimer?.cancel();
    setState(() {
      _isControlsVisible = true;
    });
    _startControlsTimer();
  }

  void _listenToCallEvents() {
    _callService.eventStream.listen((event) {
      if (event.session?.id == _session.id) {
        setState(() {
          _session = event.session!;
        });

        switch (event.type) {
          case CallEventType.callConnected:
            _pulseController.stop();
            _startDurationTimer();
            break;
          case CallEventType.callEnded:
          case CallEventType.callRejected:
            Navigator.of(context).pop();
            break;
          default:
            break;
        }
      }
    });
  }

  void _toggleMicrophone() {
    _callService.toggleMicrophone();
    _resetControlsTimer();
  }

  void _toggleCamera() {
    _callService.toggleCamera();
    _resetControlsTimer();
  }

  void _toggleSpeaker() {
    _callService.toggleSpeaker();
    _resetControlsTimer();
  }

  void _switchCamera() {
    _callService.switchCamera();
    _resetControlsTimer();
  }

  void _endCall() {
    _callService.endCall();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  String _getStatusText() {
    switch (_session.status) {
      case CallStatus.connecting:
        return '正在连接...';
      case CallStatus.ringing:
        return '正在呼叫...';
      case CallStatus.connected:
        return _formatDuration(_callDuration);
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isVideoCall = _session.type == CallType.video;
    final otherUser = _session.caller.id == 1 ? _session.callee : _session.caller;

    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _resetControlsTimer,
        child: Stack(
          children: [
            // 视频背景或头像背景
            if (isVideoCall && _session.status == CallStatus.connected) ...[
              // 远程视频流（模拟）
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.blue.withValues(alpha: 0.3),
                      Colors.purple.withValues(alpha: 0.3),
                    ],
                  ),
                ),
                child: const Center(
                  child: Text(
                    '远程视频流',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              
              // 本地视频流（小窗口）
              if (_session.isCameraEnabled)
                Positioned(
                  top: 60,
                  right: 20,
                  child: Container(
                    width: 120,
                    height: 160,
                    decoration: BoxDecoration(
                      color: Colors.grey[800],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.green.withValues(alpha: 0.3),
                              Colors.blue.withValues(alpha: 0.3),
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Text(
                            '本地视频',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ] else ...[
              // 音频通话或未连接时显示头像
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppConstants.primaryColor.withValues(alpha: 0.8),
                      AppConstants.primaryColor.withValues(alpha: 0.4),
                    ],
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AnimatedBuilder(
                        animation: _pulseAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _session.status == CallStatus.ringing || 
                                   _session.status == CallStatus.connecting
                                ? _pulseAnimation.value
                                : 1.0,
                            child: Container(
                              width: 150,
                              height: 150,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 3,
                                ),
                              ),
                              child: UserAvatar(
                                user: otherUser,
                                radius: 75,
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 30),
                      Text(
                        otherUser.nickname ?? otherUser.username,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _getStatusText(),
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            // 顶部信息栏
            AnimatedOpacity(
              opacity: _isControlsVisible ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                padding: const EdgeInsets.only(top: 50, left: 20, right: 20),
                child: Row(
                  children: [
                    Icon(
                      isVideoCall ? Icons.videocam : Icons.phone,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isVideoCall ? '视频通话' : '语音通话',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    if (_session.status == CallStatus.connected)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Text(
                          _formatDuration(_callDuration),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // 底部控制栏
            AnimatedPositioned(
              duration: const Duration(milliseconds: 300),
              bottom: _isControlsVisible ? 50 : -150,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: _session.status == CallStatus.connected
                    ? _buildConnectedControls(isVideoCall)
                    : _buildCallControls(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectedControls(bool isVideoCall) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 麦克风
        _buildControlButton(
          icon: _session.isMicrophoneEnabled ? Icons.mic : Icons.mic_off,
          isActive: _session.isMicrophoneEnabled,
          onTap: _toggleMicrophone,
        ),
        
        // 摄像头（仅视频通话）
        if (isVideoCall)
          _buildControlButton(
            icon: _session.isCameraEnabled ? Icons.videocam : Icons.videocam_off,
            isActive: _session.isCameraEnabled,
            onTap: _toggleCamera,
          ),
        
        // 扬声器
        _buildControlButton(
          icon: _session.isSpeakerEnabled ? Icons.volume_up : Icons.volume_down,
          isActive: _session.isSpeakerEnabled,
          onTap: _toggleSpeaker,
        ),
        
        // 切换摄像头（仅视频通话且摄像头开启）
        if (isVideoCall && _session.isCameraEnabled)
          _buildControlButton(
            icon: Icons.flip_camera_ios,
            isActive: true,
            onTap: _switchCamera,
          ),
        
        // 挂断
        _buildControlButton(
          icon: Icons.call_end,
          isActive: true,
          backgroundColor: Colors.red,
          onTap: _endCall,
        ),
      ],
    );
  }

  Widget _buildCallControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 挂断
        _buildControlButton(
          icon: Icons.call_end,
          isActive: true,
          backgroundColor: Colors.red,
          onTap: _endCall,
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
    Color? backgroundColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: backgroundColor ?? 
                 (isActive 
                     ? Colors.white.withValues(alpha: 0.2)
                     : Colors.red.withValues(alpha: 0.8)),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }
}
