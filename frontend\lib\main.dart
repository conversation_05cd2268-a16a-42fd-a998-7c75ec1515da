import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/app_constants.dart';
import 'providers/auth_provider.dart';
import 'providers/chat_provider.dart';
import 'screens/splash_screen.dart';

void main() {
  runApp(const AqiChatApp());
}

class AqiChatApp extends StatelessWidget {
  const AqiChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProxyProvider<AuthProvider, ChatProvider>(
          create: (context) => ChatProvider(
            Provider.of<AuthProvider>(context, listen: false).apiClient,
          ),
          update: (context, auth, previous) => ChatProvider(auth.apiClient),
        ),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        home: const Splash<PERSON><PERSON><PERSON>(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
