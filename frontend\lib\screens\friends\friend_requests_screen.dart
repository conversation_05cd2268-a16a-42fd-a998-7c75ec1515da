import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../models/user.dart';
import '../../services/mock_api_service.dart';
import '../../widgets/user_avatar.dart';

class FriendRequestsScreen extends StatefulWidget {
  const FriendRequestsScreen({super.key});

  @override
  State<FriendRequestsScreen> createState() => _FriendRequestsScreenState();
}

class _FriendRequestsScreenState extends State<FriendRequestsScreen> {
  final MockApiService _mockApi = MockApiService();
  
  List<FriendRequest> _friendRequests = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadFriendRequests();
  }

  Future<void> _loadFriendRequests() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final requests = await _mockApi.getFriendRequests();
      setState(() {
        _friendRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = '加载好友请求失败: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _handleFriendRequest(FriendRequest request, bool accept) async {
    try {
      if (accept) {
        await _mockApi.acceptFriendRequest(request.id);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已接受 ${request.sender.displayName} 的好友请求'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      } else {
        await _mockApi.rejectFriendRequest(request.id);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已拒绝 ${request.sender.displayName} 的好友请求'),
            backgroundColor: AppConstants.warningColor,
          ),
        );
      }
      
      // 重新加载好友请求列表
      _loadFriendRequests();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('操作失败: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('好友请求'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFriendRequests,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppConstants.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(
                color: AppConstants.textSecondary,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFriendRequests,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_friendRequests.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppConstants.textHint,
            ),
            SizedBox(height: 16),
            Text(
              '暂无好友请求',
              style: TextStyle(
                color: AppConstants.textSecondary,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      itemCount: _friendRequests.length,
      itemBuilder: (context, index) {
        final request = _friendRequests[index];
        return _buildFriendRequestItem(request);
      },
    );
  }

  Widget _buildFriendRequestItem(FriendRequest request) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                UserAvatar(
                  user: request.sender,
                  radius: AppConstants.avatarRadius,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        request.sender.displayName,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '@${request.sender.username}',
                        style: const TextStyle(
                          color: AppConstants.textSecondary,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(request.status),
              ],
            ),
            
            if (request.message?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  request.message!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  _formatDateTime(request.createdAt),
                  style: const TextStyle(
                    color: AppConstants.textHint,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                if (request.status == FriendRequestStatus.pending) ...[
                  TextButton(
                    onPressed: () => _handleFriendRequest(request, false),
                    style: TextButton.styleFrom(
                      foregroundColor: AppConstants.errorColor,
                    ),
                    child: const Text('拒绝'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => _handleFriendRequest(request, true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('接受'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(FriendRequestStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case FriendRequestStatus.pending:
        color = AppConstants.warningColor;
        text = '待处理';
        break;
      case FriendRequestStatus.accepted:
        color = AppConstants.successColor;
        text = '已接受';
        break;
      case FriendRequestStatus.rejected:
        color = AppConstants.errorColor;
        text = '已拒绝';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: color.withOpacity(0.1),
      side: BorderSide(color: color, width: 1),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
