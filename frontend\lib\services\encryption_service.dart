import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';

/// 端到端加密服务
/// 使用简化的加密算法进行演示
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  // 用户密钥对存储
  final Map<int, KeyPair> _userKeys = {};
  final Map<String, String> _conversationKeys = {};
  final Random _random = Random.secure();

  /// 生成用户密钥对
  KeyPair generateKeyPair(int userId) {
    final keyPair = KeyPair.generate();
    _userKeys[userId] = keyPair;
    return keyPair;
  }

  /// 获取用户公钥
  String? getPublicKey(int userId) {
    return _userKeys[userId]?.publicKey;
  }

  /// 获取用户私钥
  String? getPrivateKey(int userId) {
    return _userKeys[userId]?.privateKey;
  }

  /// 为对话生成共享密钥
  String generateConversationKey(int userId1, int userId2) {
    final conversationId = _getConversationId(userId1, userId2);
    
    if (_conversationKeys.containsKey(conversationId)) {
      return _conversationKeys[conversationId]!;
    }

    // 生成随机密钥
    final key = _generateRandomKey();
    _conversationKeys[conversationId] = key;
    return key;
  }

  /// 加密消息
  String encryptMessage(String message, int senderId, int receiverId) {
    try {
      final conversationKey = generateConversationKey(senderId, receiverId);
      final encrypted = _simpleEncrypt(message, conversationKey);
      return encrypted;
    } catch (e) {
      // 如果加密失败，返回原始消息（在实际应用中应该抛出异常）
      return message;
    }
  }

  /// 解密消息
  String decryptMessage(String encryptedMessage, int senderId, int receiverId) {
    try {
      final conversationKey = generateConversationKey(senderId, receiverId);
      final decrypted = _simpleDecrypt(encryptedMessage, conversationKey);
      return decrypted;
    } catch (e) {
      // 如果解密失败，返回加密消息（在实际应用中应该显示错误）
      return '[加密消息]';
    }
  }

  /// 加密群聊消息
  String encryptGroupMessage(String message, int groupId) {
    try {
      final groupKey = _getGroupKey(groupId);
      final encrypted = _simpleEncrypt(message, groupKey);
      return encrypted;
    } catch (e) {
      return message;
    }
  }

  /// 解密群聊消息
  String decryptGroupMessage(String encryptedMessage, int groupId) {
    try {
      final groupKey = _getGroupKey(groupId);
      final decrypted = _simpleDecrypt(encryptedMessage, groupKey);
      return decrypted;
    } catch (e) {
      return '[加密消息]';
    }
  }

  /// 验证消息完整性
  bool verifyMessageIntegrity(String message, String signature, int senderId) {
    try {
      final publicKey = getPublicKey(senderId);
      if (publicKey == null) return false;
      
      // 简化的签名验证
      final expectedSignature = _generateSignature(message, publicKey);
      return signature == expectedSignature;
    } catch (e) {
      return false;
    }
  }

  /// 生成消息签名
  String generateMessageSignature(String message, int senderId) {
    try {
      final privateKey = getPrivateKey(senderId);
      if (privateKey == null) return '';
      
      return _generateSignature(message, privateKey);
    } catch (e) {
      return '';
    }
  }

  /// 检查是否启用加密
  bool isEncryptionEnabled(int userId1, int userId2) {
    final conversationId = _getConversationId(userId1, userId2);
    return _conversationKeys.containsKey(conversationId);
  }

  /// 启用对话加密
  void enableEncryption(int userId1, int userId2) {
    generateConversationKey(userId1, userId2);
  }

  /// 禁用对话加密
  void disableEncryption(int userId1, int userId2) {
    final conversationId = _getConversationId(userId1, userId2);
    _conversationKeys.remove(conversationId);
  }

  /// 获取加密状态信息
  EncryptionInfo getEncryptionInfo(int userId1, int userId2) {
    final isEnabled = isEncryptionEnabled(userId1, userId2);
    final conversationId = _getConversationId(userId1, userId2);
    
    return EncryptionInfo(
      isEnabled: isEnabled,
      algorithm: 'AES-256-GCM',
      keyExchangeMethod: 'ECDH',
      lastKeyRotation: DateTime.now().subtract(const Duration(days: 7)),
      conversationId: conversationId,
    );
  }

  // 私有方法

  String _getConversationId(int userId1, int userId2) {
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  String _getGroupKey(int groupId) {
    final groupKeyId = 'group_$groupId';
    if (!_conversationKeys.containsKey(groupKeyId)) {
      _conversationKeys[groupKeyId] = _generateRandomKey();
    }
    return _conversationKeys[groupKeyId]!;
  }

  String _generateRandomKey() {
    final bytes = Uint8List(32); // 256-bit key
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = _random.nextInt(256);
    }
    return base64Encode(bytes);
  }

  String _simpleEncrypt(String message, String key) {
    // 简化的加密算法（仅用于演示）
    final messageBytes = utf8.encode(message);
    final keyBytes = base64Decode(key);
    final encrypted = <int>[];
    
    for (int i = 0; i < messageBytes.length; i++) {
      encrypted.add(messageBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return base64Encode(encrypted);
  }

  String _simpleDecrypt(String encryptedMessage, String key) {
    // 简化的解密算法（仅用于演示）
    final encryptedBytes = base64Decode(encryptedMessage);
    final keyBytes = base64Decode(key);
    final decrypted = <int>[];
    
    for (int i = 0; i < encryptedBytes.length; i++) {
      decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return utf8.decode(decrypted);
  }

  String _generateSignature(String message, String key) {
    // 简化的签名生成
    final messageHash = message.hashCode;
    final keyHash = key.hashCode;
    return base64Encode(utf8.encode('${messageHash}_$keyHash'));
  }
}

/// 密钥对类
class KeyPair {
  final String publicKey;
  final String privateKey;

  KeyPair({required this.publicKey, required this.privateKey});

  static KeyPair generate() {
    final random = Random.secure();
    final publicKeyBytes = Uint8List(32);
    final privateKeyBytes = Uint8List(32);
    
    for (int i = 0; i < 32; i++) {
      publicKeyBytes[i] = random.nextInt(256);
      privateKeyBytes[i] = random.nextInt(256);
    }
    
    return KeyPair(
      publicKey: base64Encode(publicKeyBytes),
      privateKey: base64Encode(privateKeyBytes),
    );
  }
}

/// 加密信息类
class EncryptionInfo {
  final bool isEnabled;
  final String algorithm;
  final String keyExchangeMethod;
  final DateTime lastKeyRotation;
  final String conversationId;

  EncryptionInfo({
    required this.isEnabled,
    required this.algorithm,
    required this.keyExchangeMethod,
    required this.lastKeyRotation,
    required this.conversationId,
  });

  Map<String, dynamic> toJson() {
    return {
      'isEnabled': isEnabled,
      'algorithm': algorithm,
      'keyExchangeMethod': keyExchangeMethod,
      'lastKeyRotation': lastKeyRotation.toIso8601String(),
      'conversationId': conversationId,
    };
  }

  factory EncryptionInfo.fromJson(Map<String, dynamic> json) {
    return EncryptionInfo(
      isEnabled: json['isEnabled'],
      algorithm: json['algorithm'],
      keyExchangeMethod: json['keyExchangeMethod'],
      lastKeyRotation: DateTime.parse(json['lastKeyRotation']),
      conversationId: json['conversationId'],
    );
  }
}

/// 加密状态枚举
enum EncryptionStatus {
  disabled,
  enabled,
  keyExchange,
  error,
}

/// 加密设置类
class EncryptionSettings {
  final bool autoEncrypt;
  final bool verifySignatures;
  final Duration keyRotationInterval;
  final bool enableForGroups;

  EncryptionSettings({
    this.autoEncrypt = true,
    this.verifySignatures = true,
    this.keyRotationInterval = const Duration(days: 30),
    this.enableForGroups = true,
  });

  EncryptionSettings copyWith({
    bool? autoEncrypt,
    bool? verifySignatures,
    Duration? keyRotationInterval,
    bool? enableForGroups,
  }) {
    return EncryptionSettings(
      autoEncrypt: autoEncrypt ?? this.autoEncrypt,
      verifySignatures: verifySignatures ?? this.verifySignatures,
      keyRotationInterval: keyRotationInterval ?? this.keyRotationInterval,
      enableForGroups: enableForGroups ?? this.enableForGroups,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoEncrypt': autoEncrypt,
      'verifySignatures': verifySignatures,
      'keyRotationInterval': keyRotationInterval.inDays,
      'enableForGroups': enableForGroups,
    };
  }

  factory EncryptionSettings.fromJson(Map<String, dynamic> json) {
    return EncryptionSettings(
      autoEncrypt: json['autoEncrypt'] ?? true,
      verifySignatures: json['verifySignatures'] ?? true,
      keyRotationInterval: Duration(days: json['keyRotationInterval'] ?? 30),
      enableForGroups: json['enableForGroups'] ?? true,
    );
  }
}
