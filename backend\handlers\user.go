package handlers

import (
	"net/http"
	"strconv"

	"aqichat-backend/middleware"
	"aqichat-backend/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserHandler struct {
	db *gorm.DB
}

func NewUserHandler(db *gorm.DB) *UserHandler {
	return &UserHandler{db: db}
}

// GetProfile 获取用户资料
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateProfile 更新用户资料
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 更新用户信息
	updates := make(map[string]interface{})
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	if err := h.db.Model(&user).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}

	// 重新获取更新后的用户信息
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated profile"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// SearchUsers 搜索用户
func (h *UserHandler) SearchUsers(c *gin.Context) {
	query := c.Query("query")
	limitStr := c.DefaultQuery("limit", "10")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 50 {
		limit = 10
	}

	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Query parameter is required"})
		return
	}

	var users []models.User
	searchPattern := "%" + query + "%"
	
	if err := h.db.Where("username ILIKE ? OR nickname ILIKE ? OR email ILIKE ?", 
		searchPattern, searchPattern, searchPattern).
		Limit(limit).
		Find(&users).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// GetFriends 获取好友列表
func (h *UserHandler) GetFriends(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var friends []models.User
	if err := h.db.Table("users").
		Joins("JOIN friendships ON users.id = friendships.friend_id").
		Where("friendships.user_id = ?", userID).
		Find(&friends).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friends"})
		return
	}

	c.JSON(http.StatusOK, friends)
}

// SendFriendRequest 发送好友请求
func (h *UserHandler) SendFriendRequest(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.SendFriendRequestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查是否给自己发送好友请求
	if req.ReceiverID == userID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot send friend request to yourself"})
		return
	}

	// 检查接收者是否存在
	var receiver models.User
	if err := h.db.First(&receiver, req.ReceiverID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Receiver not found"})
		return
	}

	// 检查是否已经是好友
	var friendship models.Friendship
	if err := h.db.Where("user_id = ? AND friend_id = ?", userID, req.ReceiverID).First(&friendship).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Already friends"})
		return
	}

	// 检查是否已经发送过好友请求
	var existingRequest models.FriendRequest
	if err := h.db.Where("sender_id = ? AND receiver_id = ? AND status = ?", 
		userID, req.ReceiverID, models.RequestPending).First(&existingRequest).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Friend request already sent"})
		return
	}

	// 创建好友请求
	friendRequest := models.FriendRequest{
		SenderID:   userID,
		ReceiverID: req.ReceiverID,
		Status:     models.RequestPending,
		Message:    req.Message,
	}

	if err := h.db.Create(&friendRequest).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send friend request"})
		return
	}

	// 预加载发送者和接收者信息
	if err := h.db.Preload("Sender").Preload("Receiver").First(&friendRequest, friendRequest.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friend request details"})
		return
	}

	c.JSON(http.StatusCreated, friendRequest)
}

// AcceptFriendRequest 接受好友请求
func (h *UserHandler) AcceptFriendRequest(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	requestIDStr := c.Param("id")
	requestID, err := strconv.ParseUint(requestIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request ID"})
		return
	}

	var friendRequest models.FriendRequest
	if err := h.db.Where("id = ? AND receiver_id = ? AND status = ?",
		requestID, userID, models.RequestPending).First(&friendRequest).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friend request not found"})
		return
	}

	// 开始事务
	tx := h.db.Begin()

	// 更新好友请求状态
	if err := tx.Model(&friendRequest).Update("status", models.RequestAccepted).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept friend request"})
		return
	}

	// 创建双向好友关系
	friendship1 := models.Friendship{
		UserID:   friendRequest.SenderID,
		FriendID: friendRequest.ReceiverID,
	}
	friendship2 := models.Friendship{
		UserID:   friendRequest.ReceiverID,
		FriendID: friendRequest.SenderID,
	}

	if err := tx.Create(&friendship1).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create friendship"})
		return
	}

	if err := tx.Create(&friendship2).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create friendship"})
		return
	}

	tx.Commit()

	// 预加载发送者和接收者信息
	if err := h.db.Preload("Sender").Preload("Receiver").First(&friendRequest, friendRequest.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friend request details"})
		return
	}

	c.JSON(http.StatusOK, friendRequest)
}

// RejectFriendRequest 拒绝好友请求
func (h *UserHandler) RejectFriendRequest(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	requestIDStr := c.Param("id")
	requestID, err := strconv.ParseUint(requestIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request ID"})
		return
	}

	var friendRequest models.FriendRequest
	if err := h.db.Where("id = ? AND receiver_id = ? AND status = ?",
		requestID, userID, models.RequestPending).First(&friendRequest).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friend request not found"})
		return
	}

	// 更新好友请求状态
	if err := h.db.Model(&friendRequest).Update("status", models.RequestRejected).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reject friend request"})
		return
	}

	// 预加载发送者和接收者信息
	if err := h.db.Preload("Sender").Preload("Receiver").First(&friendRequest, friendRequest.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friend request details"})
		return
	}

	c.JSON(http.StatusOK, friendRequest)
}

// RemoveFriend 删除好友
func (h *UserHandler) RemoveFriend(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	friendIDStr := c.Param("id")
	friendID, err := strconv.ParseUint(friendIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid friend ID"})
		return
	}

	// 检查好友关系是否存在
	var friendship models.Friendship
	if err := h.db.Where("user_id = ? AND friend_id = ?", userID, friendID).First(&friendship).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friendship not found"})
		return
	}

	// 删除双向好友关系
	if err := h.db.Where("(user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?)",
		userID, friendID, friendID, userID).Delete(&models.Friendship{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove friend"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Friend removed successfully"})
}
