# AqiChat API 文档

## 基本信息

- **Base URL**: `http://localhost:8080/api/v1`
- **WebSocket URL**: `ws://localhost:8080/ws`
- **认证方式**: Bearer <PERSON>ken (JWT)
- **数据格式**: JSON

## 认证

### 用户注册
```http
POST /auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string", 
  "password": "string",
  "nickname": "string" // 可选
}
```

**响应**:
```json
{
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "Test User",
    "avatar": null,
    "status": "offline",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "username": "string",
  "password": "string"
}
```

### 刷新Token
```http
POST /auth/refresh
Authorization: Bearer {refresh_token}
```

## 用户管理

### 获取用户资料
```http
GET /users/profile
Authorization: Bearer {access_token}
```

### 更新用户资料
```http
PUT /users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "nickname": "string",
  "avatar": "string",
  "status": "online|offline|away|busy"
}
```

### 搜索用户
```http
GET /users/search?query={search_term}&limit={limit}
Authorization: Bearer {access_token}
```

## 好友管理

### 获取好友列表
```http
GET /friends
Authorization: Bearer {access_token}
```

### 发送好友请求
```http
POST /friends/request
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "receiver_id": 123,
  "message": "Let's be friends!"
}
```

### 接受好友请求
```http
PUT /friends/request/{request_id}/accept
Authorization: Bearer {access_token}
```

### 拒绝好友请求
```http
PUT /friends/request/{request_id}/reject
Authorization: Bearer {access_token}
```

### 删除好友
```http
DELETE /friends/{friend_id}
Authorization: Bearer {access_token}
```

## 消息管理

### 获取会话列表
```http
GET /messages/conversations
Authorization: Bearer {access_token}
```

**响应**:
```json
[
  {
    "id": 1,
    "friend": {
      "id": 2,
      "username": "friend",
      "nickname": "Friend Name",
      "avatar": "avatar_url",
      "status": "online"
    },
    "last_message": {
      "id": 10,
      "content": "Hello!",
      "type": "text",
      "created_at": "2023-01-01T12:00:00Z",
      "sender_name": "Friend Name",
      "sender_avatar": "avatar_url"
    },
    "unread_count": 2,
    "updated_at": "2023-01-01T12:00:00Z"
  }
]
```

### 获取消息列表
```http
GET /messages/conversations/{conversation_id}?page={page}&limit={limit}
Authorization: Bearer {access_token}
```

### 发送消息
```http
POST /messages/send
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "receiver_id": 123,
  "content": "Hello, how are you?",
  "type": "text|image|file|audio|video"
}
```

### 标记消息为已读
```http
PUT /messages/{message_id}/read
Authorization: Bearer {access_token}
```

## WebSocket 通信

### 连接
```javascript
const ws = new WebSocket('ws://localhost:8080/ws?token={access_token}');
```

### 消息格式
```json
{
  "type": "message|typing|user_online|user_offline|message_read",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2023-01-01T12:00:00Z"
}
```

### 消息类型

#### 新消息
```json
{
  "type": "message",
  "data": {
    "id": 123,
    "sender_id": 1,
    "receiver_id": 2,
    "content": "Hello!",
    "type": "text",
    "created_at": "2023-01-01T12:00:00Z",
    "sender": {
      "id": 1,
      "username": "sender",
      "nickname": "Sender Name",
      "avatar": "avatar_url"
    }
  },
  "timestamp": "2023-01-01T12:00:00Z"
}
```

#### 打字状态
```json
{
  "type": "typing",
  "data": {
    "user_id": 1,
    "receiver_id": 2,
    "is_typing": true
  },
  "timestamp": "2023-01-01T12:00:00Z"
}
```

#### 用户在线状态
```json
{
  "type": "user_online",
  "data": {
    "user_id": 1,
    "status": "online"
  },
  "timestamp": "2023-01-01T12:00:00Z"
}
```

#### 消息已读
```json
{
  "type": "message_read",
  "data": {
    "message_id": 123,
    "user_id": 2,
    "read_at": "2023-01-01T12:00:00Z"
  },
  "timestamp": "2023-01-01T12:00:00Z"
}
```

## 错误处理

### 错误响应格式
```json
{
  "error": "Error message description"
}
```

### 常见错误码

| 状态码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未认证或Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 限流

- 每个用户每分钟最多100个请求
- WebSocket连接每秒最多10条消息
- 文件上传大小限制50MB

## 数据模型

### User
```json
{
  "id": "integer",
  "username": "string",
  "email": "string",
  "nickname": "string",
  "avatar": "string",
  "status": "online|offline|away|busy",
  "last_seen": "datetime",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Message
```json
{
  "id": "integer",
  "sender_id": "integer",
  "receiver_id": "integer", 
  "content": "string",
  "type": "text|image|file|audio|video",
  "is_read": "boolean",
  "read_at": "datetime",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Conversation
```json
{
  "id": "integer",
  "user1_id": "integer",
  "user2_id": "integer",
  "last_message_id": "integer",
  "unread_count1": "integer",
  "unread_count2": "integer",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## 示例代码

### JavaScript/TypeScript
```javascript
// 登录
const login = async (username, password) => {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  });
  
  const data = await response.json();
  if (response.ok) {
    localStorage.setItem('access_token', data.access_token);
    return data;
  } else {
    throw new Error(data.error);
  }
};

// 发送消息
const sendMessage = async (receiverId, content) => {
  const token = localStorage.getItem('access_token');
  const response = await fetch('/api/v1/messages/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({
      receiver_id: receiverId,
      content: content,
      type: 'text',
    }),
  });
  
  return response.json();
};

// WebSocket连接
const connectWebSocket = (token) => {
  const ws = new WebSocket(`ws://localhost:8080/ws?token=${token}`);
  
  ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('Received message:', message);
  };
  
  return ws;
};
```
