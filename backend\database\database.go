package database

import (
	"fmt"
	"log"

	"aqichat-backend/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

func Initialize(databaseURL string) (*gorm.DB, error) {
	var err error
	
	// 配置GORM
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}
	
	// 连接数据库
	DB, err = gorm.Open(postgres.Open(databaseURL), config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}
	
	// 获取底层sql.DB对象进行连接池配置
	sqlDB, err := DB.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	
	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	
	// 自动迁移数据库表
	if err := autoMigrate(); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}
	
	log.Println("Database connected and migrated successfully")
	return DB, nil
}

func autoMigrate() error {
	// 按依赖顺序迁移表
	return DB.AutoMigrate(
		&models.User{},
		&models.FriendRequest{},
		&models.Friendship{},
		&models.Message{},
		&models.Conversation{},
	)
}

func GetDB() *gorm.DB {
	return DB
}

// 创建索引
func CreateIndexes() error {
	// 用户表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)").Error; err != nil {
		return err
	}
	
	// 好友请求表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_friend_requests_sender ON friend_requests(sender_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_friend_requests_receiver ON friend_requests(receiver_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_friend_requests_status ON friend_requests(status)").Error; err != nil {
		return err
	}
	
	// 好友关系表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_friendships_user ON friendships(user_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_friendships_friend ON friendships(friend_id)").Error; err != nil {
		return err
	}
	
	// 消息表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_messages_receiver ON messages(receiver_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_messages_is_read ON messages(is_read)").Error; err != nil {
		return err
	}
	
	// 会话表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_conversations_user1 ON conversations(user1_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_conversations_user2 ON conversations(user2_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at)").Error; err != nil {
		return err
	}
	
	// 复合索引
	if err := DB.Exec("CREATE UNIQUE INDEX IF NOT EXISTS idx_friendships_unique ON friendships(user_id, friend_id)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE UNIQUE INDEX IF NOT EXISTS idx_conversations_unique ON conversations(user1_id, user2_id)").Error; err != nil {
		return err
	}
	
	log.Println("Database indexes created successfully")
	return nil
}

// 数据库健康检查
func HealthCheck() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}
