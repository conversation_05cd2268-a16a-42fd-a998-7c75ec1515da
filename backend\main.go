package main

import (
	"log"
	"net/http"

	"aqichat-backend/config"
	"aqichat-backend/database"
	"aqichat-backend/handlers"
	"aqichat-backend/middleware"
	"aqichat-backend/websocket"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// 初始化配置
	cfg := config.Load()
	
	// 初始化日志
	logrus.SetLevel(logrus.InfoLevel)
	logrus.SetFormatter(&logrus.JSONFormatter{})
	
	// 初始化数据库
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 创建种子数据
	if err := database.SeedData(); err != nil {
		log.Printf("Warning: Failed to create seed data: %v", err)
	}
	
	// 初始化WebSocket Hub
	hub := websocket.NewHub()
	go hub.Run()
	
	// 设置Gin模式
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	
	// 创建路由
	router := gin.Default()
	
	// 添加中间件
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	
	// 注入依赖
	authHandler := handlers.NewAuthHandler(db)
	userHandler := handlers.NewUserHandler(db)
	messageHandler := handlers.NewMessageHandler(db, hub)
	wsHandler := handlers.NewWebSocketHandler(hub)
	
	// 设置路由
	setupRoutes(router, authHandler, userHandler, messageHandler, wsHandler)
	
	// 启动服务器
	logrus.Infof("Server starting on port %s", cfg.Port)
	if err := router.Run(":" + cfg.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

func setupRoutes(
	router *gin.Engine,
	authHandler *handlers.AuthHandler,
	userHandler *handlers.UserHandler,
	messageHandler *handlers.MessageHandler,
	wsHandler *handlers.WebSocketHandler,
) {
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	
	// API路由组
	api := router.Group("/api/v1")
	
	// 认证路由
	auth := api.Group("/auth")
	{
		auth.POST("/register", authHandler.Register)
		auth.POST("/login", authHandler.Login)
		auth.POST("/refresh", authHandler.RefreshToken)
	}
	
	// 需要认证的路由
	protected := api.Group("/")
	protected.Use(middleware.AuthRequired())
	{
		// 用户路由
		users := protected.Group("/users")
		{
			users.GET("/profile", userHandler.GetProfile)
			users.PUT("/profile", userHandler.UpdateProfile)
			users.GET("/search", userHandler.SearchUsers)
		}
		
		// 好友路由
		friends := protected.Group("/friends")
		{
			friends.GET("/", userHandler.GetFriends)
			friends.POST("/request", userHandler.SendFriendRequest)
			friends.PUT("/request/:id/accept", userHandler.AcceptFriendRequest)
			friends.PUT("/request/:id/reject", userHandler.RejectFriendRequest)
			friends.DELETE("/:id", userHandler.RemoveFriend)
		}
		
		// 消息路由
		messages := protected.Group("/messages")
		{
			messages.GET("/conversations", messageHandler.GetConversations)
			messages.GET("/conversations/:id", messageHandler.GetMessages)
			messages.POST("/send", messageHandler.SendMessage)
			messages.PUT("/:id/read", messageHandler.MarkAsRead)
		}
	}
	
	// WebSocket路由
	router.GET("/ws", wsHandler.HandleWebSocket)
}
