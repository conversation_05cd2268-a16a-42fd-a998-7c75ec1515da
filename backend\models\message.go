package models

import (
	"time"

	"gorm.io/gorm"
)

type Message struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	SenderID   uint           `json:"sender_id" gorm:"not null"`
	ReceiverID uint           `json:"receiver_id" gorm:"not null"`
	Content    string         `json:"content" gorm:"not null"`
	Type       MessageType    `json:"type" gorm:"default:text"`
	IsRead     bool           `json:"is_read" gorm:"default:false"`
	ReadAt     *time.Time     `json:"read_at"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Sender   User `json:"sender" gorm:"foreignKey:SenderID"`
	Receiver User `json:"receiver" gorm:"foreignKey:ReceiverID"`
}

type MessageType string

const (
	MessageTypeText  MessageType = "text"
	MessageTypeImage MessageType = "image"
	MessageTypeFile  MessageType = "file"
	MessageTypeAudio MessageType = "audio"
	MessageTypeVideo MessageType = "video"
)

type Conversation struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	User1ID      uint      `json:"user1_id" gorm:"not null"`
	User2ID      uint      `json:"user2_id" gorm:"not null"`
	LastMessage  *Message  `json:"last_message" gorm:"foreignKey:LastMessageID"`
	LastMessageID *uint    `json:"last_message_id"`
	UnreadCount1 int       `json:"unread_count1" gorm:"default:0"` // User1的未读消息数
	UnreadCount2 int       `json:"unread_count2" gorm:"default:0"` // User2的未读消息数
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	
	// 关联关系
	User1 User `json:"user1" gorm:"foreignKey:User1ID"`
	User2 User `json:"user2" gorm:"foreignKey:User2ID"`
}

// 发送消息请求
type SendMessageRequest struct {
	ReceiverID uint        `json:"receiver_id" binding:"required"`
	Content    string      `json:"content" binding:"required"`
	Type       MessageType `json:"type" binding:"required"`
}

// 获取消息请求
type GetMessagesRequest struct {
	ConversationID uint `json:"conversation_id" binding:"required"`
	Page           int  `json:"page" binding:"min=1"`
	Limit          int  `json:"limit" binding:"min=1,max=100"`
}

// 消息响应
type MessageResponse struct {
	Message
	SenderName   string `json:"sender_name"`
	SenderAvatar string `json:"sender_avatar"`
}

// 会话响应
type ConversationResponse struct {
	ID           uint             `json:"id"`
	Friend       User             `json:"friend"`
	LastMessage  *MessageResponse `json:"last_message"`
	UnreadCount  int              `json:"unread_count"`
	UpdatedAt    time.Time        `json:"updated_at"`
}

// WebSocket消息类型
type WSMessageType string

const (
	WSMessageTypeMessage      WSMessageType = "message"
	WSMessageTypeTyping       WSMessageType = "typing"
	WSMessageTypeUserOnline   WSMessageType = "user_online"
	WSMessageTypeUserOffline  WSMessageType = "user_offline"
	WSMessageTypeMessageRead  WSMessageType = "message_read"
)

// WebSocket消息
type WSMessage struct {
	Type      WSMessageType `json:"type"`
	Data      interface{}   `json:"data"`
	Timestamp time.Time     `json:"timestamp"`
}

// 打字状态消息
type TypingMessage struct {
	UserID     uint `json:"user_id"`
	ReceiverID uint `json:"receiver_id"`
	IsTyping   bool `json:"is_typing"`
}

// 用户在线状态消息
type UserStatusMessage struct {
	UserID uint       `json:"user_id"`
	Status UserStatus `json:"status"`
}

// 消息已读状态
type MessageReadStatus struct {
	MessageID uint      `json:"message_id"`
	UserID    uint      `json:"user_id"`
	ReadAt    time.Time `json:"read_at"`
}
