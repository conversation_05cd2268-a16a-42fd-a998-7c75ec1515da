# AqiChat 虚拟机部署指南

本指南将帮助你在虚拟机中部署和运行AqiChat聊天应用。

## 虚拟机要求

### 推荐配置
- **操作系统**: Ubuntu 22.04 LTS 或 Ubuntu 20.04 LTS
- **CPU**: 2核心或以上
- **内存**: 4GB RAM 或以上
- **存储**: 20GB 可用空间或以上
- **网络**: 能够访问互联网

### 最低配置
- **CPU**: 1核心
- **内存**: 2GB RAM
- **存储**: 10GB 可用空间

## 步骤1: 准备虚拟机

### 1.1 创建虚拟机
使用你喜欢的虚拟化软件（VirtualBox、VMware、Hyper-V等）创建Ubuntu虚拟机。

### 1.2 网络配置
确保虚拟机可以：
- 访问互联网（下载依赖）
- 从宿主机访问（端口转发）

**VirtualBox端口转发设置**:
- 名称: HTTP, 协议: TCP, 主机端口: 8080, 客户端端口: 8080
- 名称: SSH, 协议: TCP, 主机端口: 2222, 客户端端口: 22

## 步骤2: 系统初始化

### 2.1 更新系统
```bash
sudo apt update && sudo apt upgrade -y
```

### 2.2 安装基础工具
```bash
sudo apt install -y curl wget git vim unzip
```

### 2.3 安装Docker
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令
newgrp docker

# 验证Docker安装
docker --version
```

### 2.4 安装Docker Compose
```bash
# 下载Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

## 步骤3: 部署应用

### 3.1 克隆项目
```bash
# 如果你有Git仓库
git clone <your-repository-url> aqichat

# 或者从宿主机传输文件
# 可以使用scp、共享文件夹等方式
```

### 3.2 进入项目目录
```bash
cd aqichat
```

### 3.3 配置环境
```bash
# 复制环境配置文件
cp .env.example .env.dev

# 编辑配置文件
nano .env.dev
```

### 3.4 启动服务
```bash
# 使用部署脚本启动
chmod +x scripts/deploy.sh
./scripts/deploy.sh dev start

# 或者直接使用Docker Compose
cd docker
docker-compose up -d
```

## 步骤4: 验证部署

### 4.1 检查服务状态
```bash
# 检查容器状态
docker ps

# 检查服务日志
docker-compose logs -f
```

### 4.2 测试API
```bash
# 健康检查
curl http://localhost:8080/health

# 运行API测试
chmod +x scripts/test_api.sh
./scripts/test_api.sh test
```

### 4.3 从宿主机访问
在宿主机浏览器中访问：
- API健康检查: http://localhost:8080/health
- 如果配置了端口转发，使用对应的端口

## 步骤5: 开发Flutter应用

### 5.1 安装Flutter（可选）
如果你想在虚拟机中开发Flutter应用：

```bash
# 下载Flutter SDK
cd ~
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.16.0-stable.tar.xz
tar xf flutter_linux_3.16.0-stable.tar.xz

# 添加到PATH
echo 'export PATH="$PATH:$HOME/flutter/bin"' >> ~/.bashrc
source ~/.bashrc

# 验证安装
flutter doctor
```

### 5.2 配置Flutter应用
```bash
cd aqichat/frontend

# 安装依赖
flutter pub get

# 运行应用（Web版本）
flutter run -d web-server --web-port 3000
```

## 故障排除

### 常见问题

1. **Docker权限问题**
```bash
sudo usermod -aG docker $USER
newgrp docker
```

2. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tulpn | grep :8080

# 停止占用端口的进程
sudo kill -9 <PID>
```

3. **内存不足**
```bash
# 查看内存使用
free -h

# 清理Docker资源
docker system prune -f
```

4. **网络连接问题**
```bash
# 检查网络连接
ping google.com

# 检查DNS
nslookup google.com
```

### 性能优化

1. **增加虚拟机内存**
   - 建议至少4GB RAM

2. **启用虚拟化加速**
   - 在虚拟机设置中启用硬件虚拟化

3. **优化Docker**
```bash
# 限制Docker日志大小
echo '{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}' | sudo tee /etc/docker/daemon.json

sudo systemctl restart docker
```

## 监控和维护

### 查看系统资源
```bash
# CPU和内存使用情况
htop

# 磁盘使用情况
df -h

# Docker容器资源使用
docker stats
```

### 日志管理
```bash
# 查看应用日志
./scripts/deploy.sh dev logs

# 查看系统日志
sudo journalctl -f
```

### 备份数据
```bash
# 备份数据库
./scripts/deploy.sh dev backup

# 备份整个项目
tar -czf aqichat-backup-$(date +%Y%m%d).tar.gz aqichat/
```

## 下一步

1. **配置域名**: 如果有域名，可以配置Nginx反向代理
2. **SSL证书**: 为生产环境配置HTTPS
3. **监控**: 添加应用监控和告警
4. **自动化**: 设置CI/CD流水线

## 技术支持

如果遇到问题，请：
1. 检查Docker和Docker Compose版本
2. 查看容器日志：`docker-compose logs`
3. 检查系统资源使用情况
4. 参考主要的部署文档：`docs/deployment.md`
