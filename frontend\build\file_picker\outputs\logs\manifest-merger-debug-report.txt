-- Merging decision tree log ---
manifest
ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:1:1-10:12
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:1:1-10:12
	package
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:2:3-45
		INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:3:5-106
	android:maxSdkVersion
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:3:78-104
	android:name
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:3:22-77
queries
ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:4:5-9:15
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:5:6-8:14
action#android.intent.action.GET_CONTENT
ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:6:9-68
	android:name
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:6:17-65
data
ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:7:9-39
	android:mimeType
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml:7:15-37
uses-sdk
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\file_picker-6.2.1\android\src\main\AndroidManifest.xml
