import 'dart:async';
import '../models/message.dart';

/// 消息撤回服务
/// 处理消息撤回、编辑等功能
class MessageRecallService {
  static final MessageRecallService _instance = MessageRecallService._internal();
  factory MessageRecallService() => _instance;
  MessageRecallService._internal();

  // 撤回时间限制（分钟）
  static const int recallTimeLimit = 2;
  
  // 事件流
  final StreamController<MessageRecallEvent> _eventController = 
      StreamController<MessageRecallEvent>.broadcast();
  
  Stream<MessageRecallEvent> get eventStream => _eventController.stream;

  /// 检查消息是否可以撤回
  bool canRecallMessage(Message message) {
    // 已经撤回的消息不能再次撤回
    if (message.isRecalled) return false;
    
    // 检查时间限制
    final now = DateTime.now();
    final timeDiff = now.difference(message.createdAt);
    
    return timeDiff.inMinutes <= recallTimeLimit;
  }

  /// 撤回消息
  Future<bool> recallMessage(Message message) async {
    if (!canRecallMessage(message)) {
      _eventController.add(MessageRecallEvent(
        type: MessageRecallEventType.recallFailed,
        message: message,
        error: '消息发送时间超过${recallTimeLimit}分钟，无法撤回',
      ));
      return false;
    }

    try {
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 500));

      // 创建撤回后的消息
      final recalledMessage = message.copyWith(
        isRecalled: true,
        recalledAt: DateTime.now(),
        content: '[消息已撤回]',
      );

      _eventController.add(MessageRecallEvent(
        type: MessageRecallEventType.messageRecalled,
        message: recalledMessage,
        originalMessage: message,
      ));

      return true;
    } catch (e) {
      _eventController.add(MessageRecallEvent(
        type: MessageRecallEventType.recallFailed,
        message: message,
        error: e.toString(),
      ));
      return false;
    }
  }

  /// 检查消息是否可以编辑
  bool canEditMessage(Message message) {
    // 已经撤回的消息不能编辑
    if (message.isRecalled) return false;
    
    // 只有文本消息可以编辑
    if (message.type != MessageType.text) return false;
    
    // 检查时间限制
    final now = DateTime.now();
    final timeDiff = now.difference(message.createdAt);
    
    return timeDiff.inMinutes <= recallTimeLimit;
  }

  /// 编辑消息
  Future<bool> editMessage(Message message, String newContent) async {
    if (!canEditMessage(message)) {
      _eventController.add(MessageRecallEvent(
        type: MessageRecallEventType.editFailed,
        message: message,
        error: '消息无法编辑',
      ));
      return false;
    }

    if (newContent.trim().isEmpty) {
      _eventController.add(MessageRecallEvent(
        type: MessageRecallEventType.editFailed,
        message: message,
        error: '消息内容不能为空',
      ));
      return false;
    }

    try {
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 500));

      // 创建编辑后的消息
      final editedMessage = message.copyWith(
        content: newContent,
        isEdited: true,
        editedAt: DateTime.now(),
        originalContent: message.isEdited ? message.originalContent : message.content,
        updatedAt: DateTime.now(),
      );

      _eventController.add(MessageRecallEvent(
        type: MessageRecallEventType.messageEdited,
        message: editedMessage,
        originalMessage: message,
      ));

      return true;
    } catch (e) {
      _eventController.add(MessageRecallEvent(
        type: MessageRecallEventType.editFailed,
        message: message,
        error: e.toString(),
      ));
      return false;
    }
  }

  /// 获取消息的剩余撤回时间
  Duration? getRemainingRecallTime(Message message) {
    if (message.isRecalled || !canRecallMessage(message)) {
      return null;
    }

    final now = DateTime.now();
    final elapsed = now.difference(message.createdAt);
    final remaining = Duration(minutes: recallTimeLimit) - elapsed;
    
    return remaining.isNegative ? null : remaining;
  }

  /// 获取消息的剩余编辑时间
  Duration? getRemainingEditTime(Message message) {
    if (message.isRecalled || !canEditMessage(message)) {
      return null;
    }

    final now = DateTime.now();
    final elapsed = now.difference(message.createdAt);
    final remaining = Duration(minutes: recallTimeLimit) - elapsed;
    
    return remaining.isNegative ? null : remaining;
  }

  /// 格式化剩余时间显示
  String formatRemainingTime(Duration duration) {
    if (duration.inMinutes > 0) {
      return '${duration.inMinutes}分${duration.inSeconds.remainder(60)}秒';
    } else {
      return '${duration.inSeconds}秒';
    }
  }

  /// 获取消息显示内容
  String getDisplayContent(Message message) {
    if (message.isRecalled) {
      return '[消息已撤回]';
    }
    return message.content;
  }

  /// 获取消息状态文本
  String getMessageStatusText(Message message) {
    if (message.isRecalled) {
      return '已撤回';
    } else if (message.isEdited) {
      return '已编辑';
    }
    return '';
  }

  /// 检查是否显示编辑标识
  bool shouldShowEditedIndicator(Message message) {
    return message.isEdited && !message.isRecalled;
  }

  /// 检查是否显示撤回标识
  bool shouldShowRecalledIndicator(Message message) {
    return message.isRecalled;
  }

  void dispose() {
    _eventController.close();
  }
}

/// 消息撤回事件
class MessageRecallEvent {
  final MessageRecallEventType type;
  final Message message;
  final Message? originalMessage;
  final String? error;

  MessageRecallEvent({
    required this.type,
    required this.message,
    this.originalMessage,
    this.error,
  });
}

/// 消息撤回事件类型
enum MessageRecallEventType {
  messageRecalled,
  recallFailed,
  messageEdited,
  editFailed,
}

/// 消息操作类型
enum MessageAction {
  recall,
  edit,
  copy,
  forward,
  reply,
  delete,
}

/// 消息操作结果
class MessageActionResult {
  final bool success;
  final String? error;
  final Message? updatedMessage;

  MessageActionResult({
    required this.success,
    this.error,
    this.updatedMessage,
  });

  factory MessageActionResult.success([Message? updatedMessage]) {
    return MessageActionResult(
      success: true,
      updatedMessage: updatedMessage,
    );
  }

  factory MessageActionResult.failure(String error) {
    return MessageActionResult(
      success: false,
      error: error,
    );
  }
}
