import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/slide_animation.dart';

class AdvancedSettingsScreen extends StatefulWidget {
  const AdvancedSettingsScreen({super.key});

  @override
  State<AdvancedSettingsScreen> createState() => _AdvancedSettingsScreenState();
}

class _AdvancedSettingsScreenState extends State<AdvancedSettingsScreen> {
  bool _autoDownloadImages = true;
  bool _autoDownloadVideos = false;
  bool _autoDownloadDocuments = false;
  bool _compressImages = true;
  bool _saveToGallery = false;
  bool _enableBackup = true;
  bool _encryptBackup = true;
  String _backupFrequency = 'daily';
  String _downloadQuality = 'high';
  double _maxFileSize = 50.0; // MB

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('高级设置'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 下载设置
          SlideInAnimation(
            begin: const Offset(-1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '下载设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    SwitchListTile(
                      title: const Text('自动下载图片'),
                      subtitle: const Text('在WiFi环境下自动下载图片'),
                      value: _autoDownloadImages,
                      onChanged: (value) {
                        setState(() {
                          _autoDownloadImages = value;
                        });
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    SwitchListTile(
                      title: const Text('自动下载视频'),
                      subtitle: const Text('在WiFi环境下自动下载视频'),
                      value: _autoDownloadVideos,
                      onChanged: (value) {
                        setState(() {
                          _autoDownloadVideos = value;
                        });
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    SwitchListTile(
                      title: const Text('自动下载文档'),
                      subtitle: const Text('在WiFi环境下自动下载文档'),
                      value: _autoDownloadDocuments,
                      onChanged: (value) {
                        setState(() {
                          _autoDownloadDocuments = value;
                        });
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    const Divider(),
                    
                    ListTile(
                      title: const Text('下载质量'),
                      subtitle: Text(_getQualityText(_downloadQuality)),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: _showQualityPicker,
                    ),
                    
                    ListTile(
                      title: const Text('最大文件大小'),
                      subtitle: Text('${_maxFileSize.toInt()} MB'),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: _showFileSizePicker,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 媒体设置
          SlideInAnimation(
            begin: const Offset(1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '媒体设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    SwitchListTile(
                      title: const Text('压缩图片'),
                      subtitle: const Text('发送前自动压缩图片以节省流量'),
                      value: _compressImages,
                      onChanged: (value) {
                        setState(() {
                          _compressImages = value;
                        });
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    SwitchListTile(
                      title: const Text('保存到相册'),
                      subtitle: const Text('自动保存接收的图片和视频到相册'),
                      value: _saveToGallery,
                      onChanged: (value) {
                        setState(() {
                          _saveToGallery = value;
                        });
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 备份设置
          SlideInAnimation(
            begin: const Offset(-1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '备份设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    SwitchListTile(
                      title: const Text('启用备份'),
                      subtitle: const Text('自动备份聊天记录到云端'),
                      value: _enableBackup,
                      onChanged: (value) {
                        setState(() {
                          _enableBackup = value;
                        });
                      },
                      activeColor: AppConstants.primaryColor,
                    ),
                    
                    if (_enableBackup) ...[
                      SwitchListTile(
                        title: const Text('加密备份'),
                        subtitle: const Text('使用端到端加密保护备份数据'),
                        value: _encryptBackup,
                        onChanged: (value) {
                          setState(() {
                            _encryptBackup = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                      ),
                      
                      ListTile(
                        title: const Text('备份频率'),
                        subtitle: Text(_getBackupFrequencyText(_backupFrequency)),
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: _showBackupFrequencyPicker,
                      ),
                      
                      ListTile(
                        title: const Text('立即备份'),
                        subtitle: const Text('手动创建备份'),
                        trailing: const Icon(Icons.backup),
                        onTap: _createBackup,
                      ),
                      
                      ListTile(
                        title: const Text('恢复备份'),
                        subtitle: const Text('从云端恢复聊天记录'),
                        trailing: const Icon(Icons.restore),
                        onTap: _restoreBackup,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 存储管理
          SlideInAnimation(
            begin: const Offset(1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '存储管理',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    ListTile(
                      title: const Text('存储使用情况'),
                      subtitle: const Text('查看应用存储使用详情'),
                      trailing: const Icon(Icons.storage),
                      onTap: _showStorageUsage,
                    ),
                    
                    ListTile(
                      title: const Text('清理缓存'),
                      subtitle: const Text('清理临时文件和缓存数据'),
                      trailing: const Icon(Icons.cleaning_services),
                      onTap: _clearCache,
                    ),
                    
                    ListTile(
                      title: const Text('管理下载'),
                      subtitle: const Text('查看和管理已下载的文件'),
                      trailing: const Icon(Icons.download),
                      onTap: _manageDownloads,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 实验性功能
          SlideInAnimation(
            begin: const Offset(0.0, 1.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          '实验性功能',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'Beta',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '这些功能仍在开发中，可能不稳定',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    ListTile(
                      title: const Text('AI智能回复'),
                      subtitle: const Text('使用AI生成智能回复建议'),
                      trailing: Switch(
                        value: false,
                        onChanged: (value) {
                          // TODO: 实现AI功能
                        },
                        activeColor: AppConstants.primaryColor,
                      ),
                    ),
                    
                    ListTile(
                      title: const Text('消息翻译'),
                      subtitle: const Text('自动翻译外语消息'),
                      trailing: Switch(
                        value: false,
                        onChanged: (value) {
                          // TODO: 实现翻译功能
                        },
                        activeColor: AppConstants.primaryColor,
                      ),
                    ),
                    
                    ListTile(
                      title: const Text('语音转文字'),
                      subtitle: const Text('自动将语音消息转换为文字'),
                      trailing: Switch(
                        value: false,
                        onChanged: (value) {
                          // TODO: 实现语音转文字功能
                        },
                        activeColor: AppConstants.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getQualityText(String quality) {
    switch (quality) {
      case 'low':
        return '低质量 (节省流量)';
      case 'medium':
        return '中等质量';
      case 'high':
        return '高质量';
      default:
        return '高质量';
    }
  }

  String _getBackupFrequencyText(String frequency) {
    switch (frequency) {
      case 'daily':
        return '每天';
      case 'weekly':
        return '每周';
      case 'monthly':
        return '每月';
      default:
        return '每天';
    }
  }

  void _showQualityPicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择下载质量',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 20),
            
            ...['low', 'medium', 'high'].map((quality) => ListTile(
              title: Text(_getQualityText(quality)),
              leading: Radio<String>(
                value: quality,
                groupValue: _downloadQuality,
                onChanged: (value) {
                  setState(() {
                    _downloadQuality = value!;
                  });
                  Navigator.of(context).pop();
                },
                activeColor: AppConstants.primaryColor,
              ),
            )),
          ],
        ),
      ),
    );
  }

  void _showFileSizePicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('设置最大文件大小'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('${_maxFileSize.toInt()} MB'),
              Slider(
                value: _maxFileSize,
                min: 10,
                max: 200,
                divisions: 19,
                onChanged: (value) {
                  setState(() {
                    _maxFileSize = value;
                  });
                },
                activeColor: AppConstants.primaryColor,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {});
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showBackupFrequencyPicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择备份频率',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 20),
            
            ...['daily', 'weekly', 'monthly'].map((frequency) => ListTile(
              title: Text(_getBackupFrequencyText(frequency)),
              leading: Radio<String>(
                value: frequency,
                groupValue: _backupFrequency,
                onChanged: (value) {
                  setState(() {
                    _backupFrequency = value!;
                  });
                  Navigator.of(context).pop();
                },
                activeColor: AppConstants.primaryColor,
              ),
            )),
          ],
        ),
      ),
    );
  }

  void _createBackup() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在创建备份...'),
          ],
        ),
      ),
    );

    // 模拟备份过程
    Future.delayed(const Duration(seconds: 3), () {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('备份创建成功'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    });
  }

  void _restoreBackup() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('恢复备份'),
        content: const Text('恢复备份将覆盖当前的聊天记录，确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('备份恢复成功'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('恢复'),
          ),
        ],
      ),
    );
  }

  void _showStorageUsage() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('存储使用情况'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStorageItem('聊天记录', '125 MB'),
            _buildStorageItem('图片', '89 MB'),
            _buildStorageItem('视频', '234 MB'),
            _buildStorageItem('文档', '45 MB'),
            _buildStorageItem('缓存', '67 MB'),
            const Divider(),
            _buildStorageItem('总计', '560 MB', isTotal: true),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageItem(String label, String size, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          Text(
            size,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal ? AppConstants.primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  void _clearCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清理缓存'),
        content: const Text('确定要清理所有缓存数据吗？这将释放存储空间但可能影响应用性能。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('缓存清理完成，释放了67MB空间'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('清理'),
          ),
        ],
      ),
    );
  }

  void _manageDownloads() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('下载管理功能即将推出'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
