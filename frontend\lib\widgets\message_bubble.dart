import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/message.dart';
import '../models/user.dart';
import 'user_avatar.dart';
import 'message_status_indicator.dart';
import 'image_message.dart';
import 'file_message.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  final User? sender;
  final bool isMe;
  final bool showAvatar;
  final bool showSenderName;

  const MessageBubble({
    super.key,
    required this.message,
    this.sender,
    required this.isMe,
    this.showAvatar = true,
    this.showSenderName = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 发送者头像（左侧）
          if (!isMe && showAvatar) ...[
            sender != null
                ? UserAvatar(user: sender!, radius: 16)
                : const CircleAvatar(
                    radius: 16,
                    child: Icon(Icons.person, size: 16),
                  ),
            const SizedBox(width: 8),
          ],
          
          // 消息气泡
          Flexible(
            child: Column(
              crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // 发送者名称
                if (!isMe && showSenderName && sender != null) ...[
                  Padding(
                    padding: const EdgeInsets.only(left: 12, bottom: 4),
                    child: Text(
                      sender!.nickname ?? sender!.username,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
                
                // 消息内容
                Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.75,
                  ),
                  decoration: BoxDecoration(
                    color: isMe 
                        ? AppConstants.primaryColor 
                        : Colors.grey[200],
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(16),
                      topRight: const Radius.circular(16),
                      bottomLeft: Radius.circular(isMe ? 16 : 4),
                      bottomRight: Radius.circular(isMe ? 4 : 16),
                    ),
                  ),
                  child: _buildMessageContent(),
                ),
                
                // 消息状态和时间
                if (isMe) ...[
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatTime(message.createdAt),
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[500],
                        ),
                      ),
                      const SizedBox(width: 4),
                      MessageStatusIndicator(status: message.status),
                    ],
                  ),
                ] else ...[
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.createdAt),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // 占位符（右侧）
          if (!isMe && showAvatar) ...[
            const SizedBox(width: 8),
            const SizedBox(width: 32), // 头像占位
          ],
        ],
      ),
    );
  }

  Widget _buildMessageContent() {
    switch (message.type) {
      case MessageType.text:
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Text(
            message.content,
            style: TextStyle(
              color: isMe ? Colors.white : Colors.black87,
              fontSize: 16,
            ),
          ),
        );
      
      case MessageType.image:
        return ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(16),
            topRight: const Radius.circular(16),
            bottomLeft: Radius.circular(isMe ? 16 : 4),
            bottomRight: Radius.circular(isMe ? 4 : 16),
          ),
          child: ImageMessage(
            imageUrl: message.content,
            isMe: isMe,
            maxWidth: 200,
          ),
        );
      
      case MessageType.file:
        return Padding(
          padding: const EdgeInsets.all(8),
          child: FileMessage(
            fileName: message.content,
            fileSize: '未知大小',
            isMe: isMe,
          ),
        );
      
      case MessageType.audio:
        return Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.play_arrow,
                color: isMe ? Colors.white : AppConstants.primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                '语音消息',
                style: TextStyle(
                  color: isMe ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        );
      
      case MessageType.video:
        return Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.play_circle_outline,
                color: isMe ? Colors.white : AppConstants.primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                '视频消息',
                style: TextStyle(
                  color: isMe ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        );
      
      default:
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Text(
            message.content,
            style: TextStyle(
              color: isMe ? Colors.white : Colors.black87,
              fontSize: 16,
            ),
          ),
        );
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${dateTime.month}/${dateTime.day}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
