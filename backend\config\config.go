package config

import (
	"os"

	"github.com/spf13/viper"
)

type Config struct {
	Environment  string `mapstructure:"ENVIRONMENT"`
	Port         string `mapstructure:"PORT"`
	DatabaseURL  string `mapstructure:"DATABASE_URL"`
	JWTSecret    string `mapstructure:"JWT_SECRET"`
	RedisURL     string `mapstructure:"REDIS_URL"`
}

func Load() *Config {
	viper.SetConfigName("config")
	viper.SetConfigType("env")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	
	// 设置默认值
	viper.SetDefault("ENVIRONMENT", "development")
	viper.SetDefault("PORT", "8080")
	viper.SetDefault("DATABASE_URL", "postgres://user:password@localhost/aqichat?sslmode=disable")
	viper.SetDefault("JWT_SECRET", "your-secret-key-change-in-production")
	viper.SetDefault("REDIS_URL", "redis://localhost:6379")
	
	// 自动读取环境变量
	viper.AutomaticEnv()
	
	// 读取配置文件（如果存在）
	if err := viper.ReadInConfig(); err != nil {
		// 配置文件不存在时使用环境变量和默认值
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			panic(err)
		}
	}
	
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		panic(err)
	}
	
	// 从环境变量覆盖配置
	if env := os.Getenv("ENVIRONMENT"); env != "" {
		config.Environment = env
	}
	if port := os.Getenv("PORT"); port != "" {
		config.Port = port
	}
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		config.DatabaseURL = dbURL
	}
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		config.JWTSecret = jwtSecret
	}
	if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
		config.RedisURL = redisURL
	}
	
	return &config
}
