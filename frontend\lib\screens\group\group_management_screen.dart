import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../models/group.dart';
import '../../models/user.dart';
import '../../widgets/slide_animation.dart';
import '../profile/user_profile_screen.dart';

class GroupManagementScreen extends StatefulWidget {
  final Group group;

  const GroupManagementScreen({
    super.key,
    required this.group,
  });

  @override
  State<GroupManagementScreen> createState() => _GroupManagementScreenState();
}

class _GroupManagementScreenState extends State<GroupManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  List<User> _allMembers = [];
  List<User> _filteredMembers = [];
  List<User> _admins = [];
  List<User> _pendingInvites = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadGroupData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadGroupData() {
    setState(() {
      _isLoading = true;
    });

    // 模拟加载群组数据
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _allMembers = _generateMockMembers();
        _filteredMembers = List.from(_allMembers);
        _admins = _allMembers.take(2).toList();
        _pendingInvites = _generateMockPendingInvites();
        _isLoading = false;
      });
    });
  }

  List<User> _generateMockMembers() {
    return List.generate(15, (index) => User(
      id: 'member_$index',
      username: 'member$index',
      email: 'member$<EMAIL>',
      nickname: '成员$index',
      bio: '这是成员$index的简介',
      status: UserStatus.values[index % UserStatus.values.length],
      createdAt: DateTime.now().subtract(Duration(days: index * 10)),
    ));
  }

  List<User> _generateMockPendingInvites() {
    return List.generate(3, (index) => User(
      id: 'pending_$index',
      username: 'pending$index',
      email: 'pending$<EMAIL>',
      nickname: '待加入$index',
      status: UserStatus.offline,
      createdAt: DateTime.now(),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('管理 ${widget.group.name}'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit_group',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('编辑群组'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'group_settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('群组设置'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_members',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('导出成员'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(100),
          child: Column(
            children: [
              // 搜索框
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: '搜索成员...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _filterMembers('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: _filterMembers,
                ),
              ),
              
              // 标签栏
              TabBar(
                controller: _tabController,
                indicatorColor: Colors.white,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                tabs: [
                  Tab(
                    text: '成员 (${_allMembers.length})',
                    icon: const Icon(Icons.people, size: 20),
                  ),
                  Tab(
                    text: '管理员 (${_admins.length})',
                    icon: const Icon(Icons.admin_panel_settings, size: 20),
                  ),
                  Tab(
                    text: '待审核 (${_pendingInvites.length})',
                    icon: const Icon(Icons.pending, size: 20),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildMembersTab(),
                _buildAdminsTab(),
                _buildPendingTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddMemberDialog,
        backgroundColor: AppConstants.primaryColor,
        icon: const Icon(Icons.person_add),
        label: const Text('添加成员'),
      ),
    );
  }

  Widget _buildMembersTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredMembers.length,
      itemBuilder: (context, index) {
        final member = _filteredMembers[index];
        final isAdmin = _admins.any((admin) => admin.id == member.id);
        
        return SlideInAnimation(
          begin: const Offset(1.0, 0.0),
          delay: Duration(milliseconds: index * 50),
          child: Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundImage: member.avatar != null
                    ? NetworkImage(member.avatar!)
                    : null,
                backgroundColor: AppConstants.primaryColor,
                child: member.avatar == null
                    ? Text(
                        member.username[0].toUpperCase(),
                        style: const TextStyle(color: Colors.white),
                      )
                    : null,
              ),
              title: Row(
                children: [
                  Expanded(
                    child: Text(member.nickname ?? member.username),
                  ),
                  if (isAdmin)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '管理员',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('@${member.username}'),
                  Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _getStatusColor(member.status),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getStatusText(member.status),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              trailing: PopupMenuButton<String>(
                onSelected: (action) => _handleMemberAction(action, member),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view_profile',
                    child: Row(
                      children: [
                        Icon(Icons.person),
                        SizedBox(width: 8),
                        Text('查看资料'),
                      ],
                    ),
                  ),
                  if (!isAdmin)
                    const PopupMenuItem(
                      value: 'make_admin',
                      child: Row(
                        children: [
                          Icon(Icons.admin_panel_settings),
                          SizedBox(width: 8),
                          Text('设为管理员'),
                        ],
                      ),
                    ),
                  if (isAdmin)
                    const PopupMenuItem(
                      value: 'remove_admin',
                      child: Row(
                        children: [
                          Icon(Icons.remove_moderator),
                          SizedBox(width: 8),
                          Text('取消管理员'),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'remove_member',
                    child: Row(
                      children: [
                        Icon(Icons.person_remove, color: Colors.red),
                        SizedBox(width: 8),
                        Text('移除成员', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
              onTap: () => _viewMemberProfile(member),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAdminsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _admins.length,
      itemBuilder: (context, index) {
        final admin = _admins[index];
        
        return SlideInAnimation(
          begin: const Offset(-1.0, 0.0),
          delay: Duration(milliseconds: index * 100),
          child: Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: Stack(
                children: [
                  CircleAvatar(
                    backgroundImage: admin.avatar != null
                        ? NetworkImage(admin.avatar!)
                        : null,
                    backgroundColor: AppConstants.primaryColor,
                    child: admin.avatar == null
                        ? Text(
                            admin.username[0].toUpperCase(),
                            style: const TextStyle(color: Colors.white),
                          )
                        : null,
                  ),
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.star,
                        size: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              title: Text(admin.nickname ?? admin.username),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('@${admin.username}'),
                  Text(
                    '管理员权限: 全部',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              trailing: PopupMenuButton<String>(
                onSelected: (action) => _handleAdminAction(action, admin),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view_profile',
                    child: Row(
                      children: [
                        Icon(Icons.person),
                        SizedBox(width: 8),
                        Text('查看资料'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit_permissions',
                    child: Row(
                      children: [
                        Icon(Icons.security),
                        SizedBox(width: 8),
                        Text('编辑权限'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'remove_admin',
                    child: Row(
                      children: [
                        Icon(Icons.remove_moderator, color: Colors.red),
                        SizedBox(width: 8),
                        Text('取消管理员', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
              onTap: () => _viewMemberProfile(admin),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPendingTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pendingInvites.length,
      itemBuilder: (context, index) {
        final user = _pendingInvites[index];
        
        return SlideInAnimation(
          begin: const Offset(0.0, 1.0),
          delay: Duration(milliseconds: index * 100),
          child: Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.grey[400],
                child: Text(
                  user.username[0].toUpperCase(),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              title: Text(user.nickname ?? user.username),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('@${user.username}'),
                  Text(
                    '等待审核',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.check, color: Colors.green),
                    onPressed: () => _approveMember(user),
                    tooltip: '批准',
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.red),
                    onPressed: () => _rejectMember(user),
                    tooltip: '拒绝',
                  ),
                ],
              ),
              onTap: () => _viewMemberProfile(user),
            ),
          ),
        );
      },
    );
  }

  void _filterMembers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredMembers = List.from(_allMembers);
      } else {
        _filteredMembers = _allMembers.where((member) {
          final username = member.username.toLowerCase();
          final nickname = (member.nickname ?? '').toLowerCase();
          final searchQuery = query.toLowerCase();
          return username.contains(searchQuery) || nickname.contains(searchQuery);
        }).toList();
      }
    });
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return Colors.green;
      case UserStatus.away:
        return Colors.orange;
      case UserStatus.busy:
        return Colors.red;
      case UserStatus.offline:
        return Colors.grey;
    }
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return '在线';
      case UserStatus.away:
        return '离开';
      case UserStatus.busy:
        return '忙碌';
      case UserStatus.offline:
        return '离线';
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit_group':
        _editGroup();
        break;
      case 'group_settings':
        _showGroupSettings();
        break;
      case 'export_members':
        _exportMembers();
        break;
    }
  }

  void _handleMemberAction(String action, User member) {
    switch (action) {
      case 'view_profile':
        _viewMemberProfile(member);
        break;
      case 'make_admin':
        _makeAdmin(member);
        break;
      case 'remove_admin':
        _removeAdmin(member);
        break;
      case 'remove_member':
        _removeMember(member);
        break;
    }
  }

  void _handleAdminAction(String action, User admin) {
    switch (action) {
      case 'view_profile':
        _viewMemberProfile(admin);
        break;
      case 'edit_permissions':
        _editPermissions(admin);
        break;
      case 'remove_admin':
        _removeAdmin(admin);
        break;
    }
  }

  void _viewMemberProfile(User user) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(user: user),
      ),
    );
  }

  void _makeAdmin(User member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('设为管理员'),
        content: Text('确定要将 ${member.nickname ?? member.username} 设为管理员吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _admins.add(member);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${member.nickname ?? member.username} 已设为管理员'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _removeAdmin(User admin) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消管理员'),
        content: Text('确定要取消 ${admin.nickname ?? admin.username} 的管理员权限吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _admins.removeWhere((a) => a.id == admin.id);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('已取消 ${admin.nickname ?? admin.username} 的管理员权限'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _removeMember(User member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('移除成员'),
        content: Text('确定要将 ${member.nickname ?? member.username} 从群组中移除吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _allMembers.removeWhere((m) => m.id == member.id);
                _filteredMembers.removeWhere((m) => m.id == member.id);
                _admins.removeWhere((a) => a.id == member.id);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${member.nickname ?? member.username} 已被移除'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('移除'),
          ),
        ],
      ),
    );
  }

  void _approveMember(User user) {
    setState(() {
      _allMembers.add(user);
      _filteredMembers.add(user);
      _pendingInvites.removeWhere((u) => u.id == user.id);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${user.nickname ?? user.username} 已加入群组'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _rejectMember(User user) {
    setState(() {
      _pendingInvites.removeWhere((u) => u.id == user.id);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已拒绝 ${user.nickname ?? user.username} 的加入申请'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showAddMemberDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加成员'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: '输入用户名或邮箱',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('邀请已发送'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('发送邀请'),
          ),
        ],
      ),
    );
  }

  void _editGroup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('编辑群组功能即将推出'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showGroupSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('群组设置功能即将推出'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _exportMembers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('成员列表导出完成'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _editPermissions(User admin) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('权限编辑功能即将推出'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
