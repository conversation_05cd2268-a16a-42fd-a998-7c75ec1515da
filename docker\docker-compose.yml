version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: aqichat-postgres
    environment:
      POSTGRES_DB: aqichat
      POSTGRES_USER: aqichat_user
      POSTGRES_PASSWORD: aqichat_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - aqichat-network
    restart: unless-stopped

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: aqichat-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aqichat-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Go后端服务
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: aqichat-backend
    environment:
      - ENVIRONMENT=production
      - PORT=8080
      - DATABASE_URL=******************************************************/aqichat?sslmode=disable
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - REDIS_URL=redis://redis:6379
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    networks:
      - aqichat-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: aqichat-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - aqichat-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  aqichat-network:
    driver: bridge
