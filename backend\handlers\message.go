package handlers

import (
	"net/http"
	"strconv"
	"time"

	"aqichat-backend/middleware"
	"aqichat-backend/models"
	"aqichat-backend/websocket"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MessageHandler struct {
	db  *gorm.DB
	hub *websocket.Hub
}

func NewMessageHandler(db *gorm.DB, hub *websocket.Hub) *MessageHandler {
	return &MessageHandler{db: db, hub: hub}
}

// GetConversations 获取会话列表
func (h *MessageHandler) GetConversations(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var conversations []models.Conversation
	if err := h.db.Where("user1_id = ? OR user2_id = ?", userID, userID).
		Preload("User1").
		Preload("User2").
		Preload("LastMessage").
		Preload("LastMessage.Sender").
		Order("updated_at DESC").
		Find(&conversations).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get conversations"})
		return
	}

	// 转换为响应格式
	var responses []models.ConversationResponse
	for _, conv := range conversations {
		var friend models.User
		var unreadCount int

		// 确定对话的另一方和未读消息数
		if conv.User1ID == userID {
			friend = conv.User2
			unreadCount = conv.UnreadCount1
		} else {
			friend = conv.User1
			unreadCount = conv.UnreadCount2
		}

		var lastMessage *models.MessageResponse
		if conv.LastMessage != nil {
			lastMessage = &models.MessageResponse{
				Message:      *conv.LastMessage,
				SenderName:   conv.LastMessage.Sender.Nickname,
				SenderAvatar: conv.LastMessage.Sender.Avatar,
			}
			if lastMessage.SenderName == "" {
				lastMessage.SenderName = conv.LastMessage.Sender.Username
			}
		}

		response := models.ConversationResponse{
			ID:          conv.ID,
			Friend:      friend,
			LastMessage: lastMessage,
			UnreadCount: unreadCount,
			UpdatedAt:   conv.UpdatedAt,
		}

		responses = append(responses, response)
	}

	c.JSON(http.StatusOK, responses)
}

// GetMessages 获取消息列表
func (h *MessageHandler) GetMessages(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	conversationIDStr := c.Param("id")
	conversationID, err := strconv.ParseUint(conversationIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid conversation ID"})
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "50")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 50
	}

	// 验证用户是否有权限访问该会话
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND (user1_id = ? OR user2_id = ?)", 
		conversationID, userID, userID).First(&conversation).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Conversation not found"})
		return
	}

	// 获取消息
	var messages []models.Message
	offset := (page - 1) * limit

	if err := h.db.Where("(sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)",
		conversation.User1ID, conversation.User2ID, conversation.User2ID, conversation.User1ID).
		Preload("Sender").
		Preload("Receiver").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&messages).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get messages"})
		return
	}

	// 转换为响应格式
	var responses []models.MessageResponse
	for _, msg := range messages {
		response := models.MessageResponse{
			Message:      msg,
			SenderName:   msg.Sender.Nickname,
			SenderAvatar: msg.Sender.Avatar,
		}
		if response.SenderName == "" {
			response.SenderName = msg.Sender.Username
		}
		responses = append(responses, response)
	}

	c.JSON(http.StatusOK, responses)
}

// SendMessage 发送消息
func (h *MessageHandler) SendMessage(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查接收者是否存在
	var receiver models.User
	if err := h.db.First(&receiver, req.ReceiverID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Receiver not found"})
		return
	}

	// 检查是否是好友关系
	var friendship models.Friendship
	if err := h.db.Where("user_id = ? AND friend_id = ?", userID, req.ReceiverID).First(&friendship).Error; err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only send messages to friends"})
		return
	}

	// 创建消息
	message := models.Message{
		SenderID:   userID,
		ReceiverID: req.ReceiverID,
		Content:    req.Content,
		Type:       req.Type,
	}

	if err := h.db.Create(&message).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send message"})
		return
	}

	// 预加载发送者和接收者信息
	if err := h.db.Preload("Sender").Preload("Receiver").First(&message, message.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get message details"})
		return
	}

	// 更新或创建会话
	h.updateConversation(userID, req.ReceiverID, &message)

	// 通过WebSocket发送消息
	h.broadcastMessage(&message)

	// 转换为响应格式
	response := models.MessageResponse{
		Message:      message,
		SenderName:   message.Sender.Nickname,
		SenderAvatar: message.Sender.Avatar,
	}
	if response.SenderName == "" {
		response.SenderName = message.Sender.Username
	}

	c.JSON(http.StatusCreated, response)
}

// MarkAsRead 标记消息为已读
func (h *MessageHandler) MarkAsRead(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	messageIDStr := c.Param("id")
	messageID, err := strconv.ParseUint(messageIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
		return
	}

	var message models.Message
	if err := h.db.Where("id = ? AND receiver_id = ?", messageID, userID).First(&message).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Message not found"})
		return
	}

	// 更新消息为已读
	now := time.Now()
	if err := h.db.Model(&message).Updates(map[string]interface{}{
		"is_read": true,
		"read_at": &now,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark message as read"})
		return
	}

	// 更新会话的未读消息数
	h.updateUnreadCount(message.SenderID, userID)

	// 通过WebSocket通知发送者消息已读
	h.broadcastMessageRead(messageID, userID)

	c.JSON(http.StatusOK, gin.H{"message": "Message marked as read"})
}

// updateConversation 更新或创建会话
func (h *MessageHandler) updateConversation(senderID, receiverID uint, message *models.Message) {
	// 确保user1_id < user2_id以保持一致性
	user1ID, user2ID := senderID, receiverID
	if user1ID > user2ID {
		user1ID, user2ID = user2ID, user1ID
	}

	var conversation models.Conversation
	err := h.db.Where("user1_id = ? AND user2_id = ?", user1ID, user2ID).First(&conversation).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新会话
		conversation = models.Conversation{
			User1ID:       user1ID,
			User2ID:       user2ID,
			LastMessageID: &message.ID,
		}

		// 设置未读消息数
		if senderID == user1ID {
			conversation.UnreadCount2 = 1
		} else {
			conversation.UnreadCount1 = 1
		}

		h.db.Create(&conversation)
	} else {
		// 更新现有会话
		updates := map[string]interface{}{
			"last_message_id": message.ID,
			"updated_at":      time.Now(),
		}

		// 增加接收者的未读消息数
		if senderID == user1ID {
			updates["unread_count2"] = gorm.Expr("unread_count2 + 1")
		} else {
			updates["unread_count1"] = gorm.Expr("unread_count1 + 1")
		}

		h.db.Model(&conversation).Updates(updates)
	}
}

// updateUnreadCount 更新未读消息数
func (h *MessageHandler) updateUnreadCount(senderID, receiverID uint) {
	// 确保user1_id < user2_id以保持一致性
	user1ID, user2ID := senderID, receiverID
	if user1ID > user2ID {
		user1ID, user2ID = user2ID, user1ID
	}

	var conversation models.Conversation
	if err := h.db.Where("user1_id = ? AND user2_id = ?", user1ID, user2ID).First(&conversation).Error; err != nil {
		return
	}

	// 重置接收者的未读消息数
	updates := make(map[string]interface{})
	if receiverID == user1ID {
		updates["unread_count1"] = 0
	} else {
		updates["unread_count2"] = 0
	}

	h.db.Model(&conversation).Updates(updates)
}

// broadcastMessage 广播消息
func (h *MessageHandler) broadcastMessage(message *models.Message) {
	wsMessage := models.WSMessage{
		Type: models.WSMessageTypeMessage,
		Data: map[string]interface{}{
			"id":          message.ID,
			"sender_id":   message.SenderID,
			"receiver_id": message.ReceiverID,
			"content":     message.Content,
			"type":        message.Type,
			"created_at":  message.CreatedAt,
			"sender": map[string]interface{}{
				"id":       message.Sender.ID,
				"username": message.Sender.Username,
				"nickname": message.Sender.Nickname,
				"avatar":   message.Sender.Avatar,
			},
		},
		Timestamp: time.Now(),
	}

	// 发送给接收者
	h.hub.SendToUser(message.ReceiverID, wsMessage)
}

// broadcastMessageRead 广播消息已读状态
func (h *MessageHandler) broadcastMessageRead(messageID uint64, userID uint) {
	wsMessage := models.WSMessage{
		Type: models.WSMessageTypeMessageRead,
		Data: map[string]interface{}{
			"message_id": messageID,
			"user_id":    userID,
			"read_at":    time.Now(),
		},
		Timestamp: time.Now(),
	}

	// 这里需要找到消息的发送者并发送通知
	var message models.Message
	if err := h.db.First(&message, messageID).Error; err == nil {
		h.hub.SendToUser(message.SenderID, wsMessage)
	}
}
