import 'user.dart';

// 消息状态枚举
enum MessageStatus {
  sending,   // 发送中
  sent,      // 已发送
  delivered, // 已送达
  read,      // 已读
  failed,    // 发送失败
}

// 简化的消息模型，用于演示
class Message {
  final int id;
  final int senderId;
  final int receiverId;
  final String content;
  final MessageType type;
  final bool isRead;
  final DateTime? readAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User sender;
  final User receiver;
  final MessageStatus status;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.type,
    this.isRead = false,
    this.readAt,
    required this.createdAt,
    required this.updatedAt,
    required this.sender,
    required this.receiver,
    this.status = MessageStatus.sent,
  });

  Message copyWith({
    int? id,
    int? senderId,
    int? receiverId,
    String? content,
    MessageType? type,
    bool? isRead,
    DateTime? readAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    User? sender,
    User? receiver,
    MessageStatus? status,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sender: sender ?? this.sender,
      receiver: receiver ?? this.receiver,
      status: status ?? this.status,
    );
  }
}

enum MessageType {
  text,
  image,
  file,
  audio,
  video,
}

class Conversation {
  final int id;
  final User friend;
  final Message? lastMessage;
  final int unreadCount;
  final DateTime updatedAt;

  Conversation({
    required this.id,
    required this.friend,
    this.lastMessage,
    this.unreadCount = 0,
    required this.updatedAt,
  });
}

class SendMessageRequest {
  final int receiverId;
  final String content;
  final MessageType type;

  SendMessageRequest({
    required this.receiverId,
    required this.content,
    required this.type,
  });
}

// WebSocket消息类型
enum WSMessageType {
  message,
  typing,
  userOnline,
  userOffline,
  messageRead,
}

class WSMessage {
  final WSMessageType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  WSMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  // 简化的JSON序列化
  factory WSMessage.fromJson(Map<String, dynamic> json) {
    return WSMessage(
      type: WSMessageType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => WSMessageType.message,
      ),
      data: json['data'] ?? {},
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
