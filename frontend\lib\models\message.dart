import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'message.g.dart';

@JsonSerializable()
class Message {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_id')
  final int senderId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'receiver_id')
  final int receiverId;
  final String content;
  final MessageType type;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_read')
  final bool isRead;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'read_at')
  final DateTime? readAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;
  final User sender;
  final User receiver;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.type,
    this.isRead = false,
    this.readAt,
    required this.createdAt,
    required this.updatedAt,
    required this.sender,
    required this.receiver,
  });

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFrom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$MessageToJson(this);

  Message copyWith({
    int? id,
    int? senderId,
    int? receiverId,
    String? content,
    MessageType? type,
    bool? isRead,
    DateTime? readAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    User? sender,
    User? receiver,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sender: sender ?? this.sender,
      receiver: receiver ?? this.receiver,
    );
  }
}

enum MessageType {
  @JsonValue('text')
  text,
  @JsonValue('image')
  image,
  @JsonValue('file')
  file,
  @JsonValue('audio')
  audio,
  @JsonValue('video')
  video,
}

@JsonSerializable()
class Conversation {
  final int id;
  final User friend;
  @JsonKey(name: 'last_message')
  final Message? lastMessage;
  @JsonKey(name: 'unread_count')
  final int unreadCount;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  Conversation({
    required this.id,
    required this.friend,
    this.lastMessage,
    this.unreadCount = 0,
    required this.updatedAt,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) =>
      _$ConversationFromJson(json);
  Map<String, dynamic> toJson() => _$ConversationToJson(this);
}

@JsonSerializable()
class SendMessageRequest {
  @JsonKey(name: 'receiver_id')
  final int receiverId;
  final String content;
  final MessageType type;

  SendMessageRequest({
    required this.receiverId,
    required this.content,
    required this.type,
  });

  factory SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendMessageRequestFromJson(json);
  Map<String, dynamic> toJson() => _$SendMessageRequestToJson(this);
}

// WebSocket消息类型
enum WSMessageType {
  @JsonValue('message')
  message,
  @JsonValue('typing')
  typing,
  @JsonValue('user_online')
  userOnline,
  @JsonValue('user_offline')
  userOffline,
  @JsonValue('message_read')
  messageRead,
}

@JsonSerializable()
class WSMessage {
  final WSMessageType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  WSMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory WSMessage.fromJson(Map<String, dynamic> json) =>
      _$WSMessageFromJson(json);
  Map<String, dynamic> toJson() => _$WSMessageToJson(this);
}

@JsonSerializable()
class TypingMessage {
  @JsonKey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'receiver_id')
  final int receiverId;
  @JsonKey(name: 'is_typing')
  final bool isTyping;

  TypingMessage({
    required this.userId,
    required this.receiverId,
    required this.isTyping,
  });

  factory TypingMessage.fromJson(Map<String, dynamic> json) =>
      _$TypingMessageFromJson(json);
  Map<String, dynamic> toJson() => _$TypingMessageToJson(this);
}

@JsonSerializable()
class UserStatusMessage {
  @JsonKey(name: 'user_id')
  final int userId;
  final UserStatus status;

  UserStatusMessage({
    required this.userId,
    required this.status,
  });

  factory UserStatusMessage.fromJson(Map<String, dynamic> json) =>
      _$UserStatusMessageFromJson(json);
  Map<String, dynamic> toJson() => _$UserStatusMessageToJson(this);
}
