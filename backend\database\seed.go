package database

import (
	"log"
	"time"

	"aqichat-backend/models"
	"aqichat-backend/utils"

	"gorm.io/gorm"
)

// SeedData 初始化种子数据
func SeedData() error {
	// 检查是否已有用户数据
	var userCount int64
	if err := DB.Model(&models.User{}).Count(&userCount).Error; err != nil {
		return err
	}
	
	// 如果已有用户数据，跳过种子数据创建
	if userCount > 0 {
		log.Println("Database already has user data, skipping seed")
		return nil
	}
	
	log.Println("Creating seed data...")
	
	// 创建测试用户
	users, err := createTestUsers()
	if err != nil {
		return err
	}
	
	// 创建好友关系
	if err := createFriendships(users); err != nil {
		return err
	}
	
	// 创建测试消息
	if err := createTestMessages(users); err != nil {
		return err
	}
	
	log.Println("Seed data created successfully")
	return nil
}

func createTestUsers() ([]models.User, error) {
	hashedPassword, err := utils.HashPassword("123456")
	if err != nil {
		return nil, err
	}
	
	users := []models.User{
		{
			Username: "alice",
			Email:    "<EMAIL>",
			Password: hashedPassword,
			Nickname: "Alice",
			Status:   models.StatusOnline,
		},
		{
			Username: "bob",
			Email:    "<EMAIL>",
			Password: hashedPassword,
			Nickname: "Bob",
			Status:   models.StatusOnline,
		},
		{
			Username: "charlie",
			Email:    "<EMAIL>",
			Password: hashedPassword,
			Nickname: "Charlie",
			Status:   models.StatusOffline,
		},
		{
			Username: "diana",
			Email:    "<EMAIL>",
			Password: hashedPassword,
			Nickname: "Diana",
			Status:   models.StatusAway,
		},
		{
			Username: "eve",
			Email:    "<EMAIL>",
			Password: hashedPassword,
			Nickname: "Eve",
			Status:   models.StatusBusy,
		},
	}
	
	for i := range users {
		if err := DB.Create(&users[i]).Error; err != nil {
			return nil, err
		}
	}
	
	return users, nil
}

func createFriendships(users []models.User) error {
	// Alice和Bob是好友
	friendship1 := models.Friendship{
		UserID:   users[0].ID,
		FriendID: users[1].ID,
	}
	friendship2 := models.Friendship{
		UserID:   users[1].ID,
		FriendID: users[0].ID,
	}
	
	// Alice和Charlie是好友
	friendship3 := models.Friendship{
		UserID:   users[0].ID,
		FriendID: users[2].ID,
	}
	friendship4 := models.Friendship{
		UserID:   users[2].ID,
		FriendID: users[0].ID,
	}
	
	// Bob和Diana是好友
	friendship5 := models.Friendship{
		UserID:   users[1].ID,
		FriendID: users[3].ID,
	}
	friendship6 := models.Friendship{
		UserID:   users[3].ID,
		FriendID: users[1].ID,
	}
	
	friendships := []models.Friendship{
		friendship1, friendship2, friendship3, friendship4, friendship5, friendship6,
	}
	
	for _, friendship := range friendships {
		if err := DB.Create(&friendship).Error; err != nil {
			return err
		}
	}
	
	return nil
}

func createTestMessages(users []models.User) error {
	now := time.Now()
	
	messages := []models.Message{
		{
			SenderID:   users[0].ID, // Alice
			ReceiverID: users[1].ID, // Bob
			Content:    "Hi Bob! How are you?",
			Type:       models.MessageTypeText,
			CreatedAt:  now.Add(-time.Hour * 2),
		},
		{
			SenderID:   users[1].ID, // Bob
			ReceiverID: users[0].ID, // Alice
			Content:    "Hi Alice! I'm doing great, thanks for asking!",
			Type:       models.MessageTypeText,
			IsRead:     true,
			ReadAt:     &now,
			CreatedAt:  now.Add(-time.Hour * 2).Add(time.Minute * 5),
		},
		{
			SenderID:   users[0].ID, // Alice
			ReceiverID: users[1].ID, // Bob
			Content:    "That's wonderful to hear! 😊",
			Type:       models.MessageTypeText,
			CreatedAt:  now.Add(-time.Hour * 2).Add(time.Minute * 10),
		},
		{
			SenderID:   users[0].ID, // Alice
			ReceiverID: users[2].ID, // Charlie
			Content:    "Hey Charlie, are you free this weekend?",
			Type:       models.MessageTypeText,
			CreatedAt:  now.Add(-time.Hour * 1),
		},
		{
			SenderID:   users[2].ID, // Charlie
			ReceiverID: users[0].ID, // Alice
			Content:    "Yes, I'm free! What did you have in mind?",
			Type:       models.MessageTypeText,
			CreatedAt:  now.Add(-time.Minute * 30),
		},
		{
			SenderID:   users[1].ID, // Bob
			ReceiverID: users[3].ID, // Diana
			Content:    "Diana, did you see the latest project updates?",
			Type:       models.MessageTypeText,
			CreatedAt:  now.Add(-time.Minute * 15),
		},
	}
	
	for _, message := range messages {
		if err := DB.Create(&message).Error; err != nil {
			return err
		}
	}
	
	// 创建对应的会话
	conversations := []models.Conversation{
		{
			User1ID:      users[0].ID, // Alice
			User2ID:      users[1].ID, // Bob
			LastMessageID: &messages[2].ID,
			UnreadCount1: 0,
			UnreadCount2: 1,
		},
		{
			User1ID:      users[0].ID, // Alice
			User2ID:      users[2].ID, // Charlie
			LastMessageID: &messages[4].ID,
			UnreadCount1: 1,
			UnreadCount2: 0,
		},
		{
			User1ID:      users[1].ID, // Bob
			User2ID:      users[3].ID, // Diana
			LastMessageID: &messages[5].ID,
			UnreadCount1: 0,
			UnreadCount2: 1,
		},
	}
	
	for _, conversation := range conversations {
		if err := DB.Create(&conversation).Error; err != nil {
			return err
		}
	}
	
	return nil
}
