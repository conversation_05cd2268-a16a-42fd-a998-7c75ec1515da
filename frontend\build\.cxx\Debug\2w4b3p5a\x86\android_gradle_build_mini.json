{"buildFiles": ["D:\\FlutterSdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\.cxx\\Debug\\2w4b3p5a\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\.cxx\\Debug\\2w4b3p5a\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}