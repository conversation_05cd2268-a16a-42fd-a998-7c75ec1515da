// 简化的用户模型，用于演示
class User {
  final int id;
  final String username;
  final String email;
  final String? nickname;
  final String? avatar;
  final UserStatus status;
  final DateTime? lastSeen;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.username,
    required this.email,
    this.nickname,
    this.avatar,
    this.status = UserStatus.offline,
    this.lastSeen,
    required this.createdAt,
    required this.updatedAt,
  });

  User copyWith({
    int? id,
    String? username,
    String? email,
    String? nickname,
    String? avatar,
    UserStatus? status,
    DateTime? lastSeen,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      status: status ?? this.status,
      lastSeen: lastSeen ?? this.lastSeen,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName => nickname?.isNotEmpty == true ? nickname! : username;
}

enum UserStatus {
  online,
  offline,
  away,
  busy,
}

class FriendRequest {
  final int id;
  final int senderId;
  final int receiverId;
  final FriendRequestStatus status;
  final String? message;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User sender;
  final User receiver;

  FriendRequest({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.status,
    this.message,
    required this.createdAt,
    required this.updatedAt,
    required this.sender,
    required this.receiver,
  });
}

enum FriendRequestStatus {
  pending,
  accepted,
  rejected,
}

class RegisterRequest {
  final String username;
  final String email;
  final String password;
  final String? nickname;

  RegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    this.nickname,
  });
}

class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });
}

class LoginResponse {
  final User user;
  final String accessToken;
  final String refreshToken;

  LoginResponse({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
  });
}
