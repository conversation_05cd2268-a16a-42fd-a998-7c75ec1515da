# 环境配置示例文件
# 复制此文件为 .env.dev, .env.staging, .env.prod 并修改相应的值

# 应用环境 (development, staging, production)
ENVIRONMENT=development

# 服务端口
PORT=8080

# 数据库配置
DATABASE_URL=postgres://aqichat_user:aqichat_password@localhost:5432/aqichat?sslmode=disable

# JWT密钥 (生产环境请使用强密钥)
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379

# 日志级别 (debug, info, warn, error)
LOG_LEVEL=info

# 文件上传配置
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# CORS配置
CORS_ORIGINS=*

# 邮件配置 (用于通知等功能)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# 推送通知配置 (可选)
FCM_SERVER_KEY=your-fcm-server-key

# 监控配置 (可选)
SENTRY_DSN=your-sentry-dsn

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60s
