import 'package:dio/dio.dart';
import '../models/user.dart';
import '../models/message.dart';

// 真实的API客户端，连接到Go后端
class RealApiService {
  static const String baseUrl = "http://10.0.2.2:8080/api/v1";  // Android模拟器访问宿主机
  static const String wsUrl = "ws://10.0.2.2:8080/ws";
  
  late final Dio _dio;
  
  String? _accessToken;
  String? _refreshToken;

  RealApiService() {
    _dio = Dio();
    _setupInterceptors();
  }

  String? get accessToken => _accessToken;
  String? get refreshToken => _refreshToken;

  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_accessToken != null) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        options.headers['Content-Type'] = 'application/json';
        handler.next(options);
      },
      onError: (error, handler) async {
        // 处理401错误，尝试刷新token
        if (error.response?.statusCode == 401 && _refreshToken != null) {
          try {
            await _refreshAccessToken();
            // 重试原请求
            final opts = error.requestOptions;
            opts.headers['Authorization'] = 'Bearer $_accessToken';
            final response = await _dio.fetch(opts);
            handler.resolve(response);
            return;
          } catch (e) {
            clearTokens();
          }
        }
        handler.next(error);
      },
    ));

    // 日志拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print('[API] $obj'),
    ));
  }

  void setTokens(String accessToken, String refreshToken) {
    _accessToken = accessToken;
    _refreshToken = refreshToken;
  }

  void clearTokens() {
    _accessToken = null;
    _refreshToken = null;
  }

  bool get isAuthenticated => _accessToken != null;

  // 刷新访问令牌
  Future<void> _refreshAccessToken() async {
    final response = await _dio.post(
      '$baseUrl/auth/refresh',
      data: {'refresh_token': _refreshToken},
    );
    
    final data = response.data;
    _accessToken = data['access_token'];
    _refreshToken = data['refresh_token'];
  }

  // 用户注册
  Future<LoginResponse> register(RegisterRequest request) async {
    final response = await _dio.post(
      '$baseUrl/auth/register',
      data: {
        'username': request.username,
        'email': request.email,
        'password': request.password,
        'nickname': request.nickname,
      },
    );

    final data = response.data;
    final user = User(
      id: data['user']['id'],
      username: data['user']['username'],
      email: data['user']['email'],
      nickname: data['user']['nickname'],
      avatar: data['user']['avatar'],
      status: _parseUserStatus(data['user']['status']),
      createdAt: DateTime.parse(data['user']['created_at']),
      updatedAt: DateTime.parse(data['user']['updated_at']),
    );

    _accessToken = data['access_token'];
    _refreshToken = data['refresh_token'];

    return LoginResponse(
      user: user,
      accessToken: _accessToken!,
      refreshToken: _refreshToken!,
    );
  }

  // 用户登录
  Future<LoginResponse> login(LoginRequest request) async {
    final response = await _dio.post(
      '$baseUrl/auth/login',
      data: {
        'username': request.username,
        'password': request.password,
      },
    );

    final data = response.data;
    final user = User(
      id: data['user']['id'],
      username: data['user']['username'],
      email: data['user']['email'],
      nickname: data['user']['nickname'],
      avatar: data['user']['avatar'],
      status: _parseUserStatus(data['user']['status']),
      createdAt: DateTime.parse(data['user']['created_at']),
      updatedAt: DateTime.parse(data['user']['updated_at']),
    );

    _accessToken = data['access_token'];
    _refreshToken = data['refresh_token'];

    return LoginResponse(
      user: user,
      accessToken: _accessToken!,
      refreshToken: _refreshToken!,
    );
  }

  // 获取用户资料
  Future<User> getProfile() async {
    final response = await _dio.get('$baseUrl/users/profile');
    final data = response.data;
    
    return User(
      id: data['id'],
      username: data['username'],
      email: data['email'],
      nickname: data['nickname'],
      avatar: data['avatar'],
      status: _parseUserStatus(data['status']),
      lastSeen: data['last_seen'] != null ? DateTime.parse(data['last_seen']) : null,
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
    );
  }

  // 搜索用户
  Future<List<User>> searchUsers(String query, int limit) async {
    final response = await _dio.get(
      '$baseUrl/users/search',
      queryParameters: {
        'q': query,
        'limit': limit,
      },
    );

    final List<dynamic> data = response.data;
    return data.map((item) => User(
      id: item['id'],
      username: item['username'],
      email: item['email'],
      nickname: item['nickname'],
      avatar: item['avatar'],
      status: _parseUserStatus(item['status']),
      lastSeen: item['last_seen'] != null ? DateTime.parse(item['last_seen']) : null,
      createdAt: DateTime.parse(item['created_at']),
      updatedAt: DateTime.parse(item['updated_at']),
    )).toList();
  }

  // 获取会话列表
  Future<List<Conversation>> getConversations() async {
    final response = await _dio.get('$baseUrl/messages/conversations');
    final List<dynamic> data = response.data;
    
    return data.map((item) => Conversation(
      id: item['id'],
      friend: User(
        id: item['friend']['id'],
        username: item['friend']['username'],
        email: item['friend']['email'],
        nickname: item['friend']['nickname'],
        avatar: item['friend']['avatar'],
        status: _parseUserStatus(item['friend']['status']),
        createdAt: DateTime.parse(item['friend']['created_at']),
        updatedAt: DateTime.parse(item['friend']['updated_at']),
      ),
      lastMessage: item['last_message'] != null ? _parseMessage(item['last_message']) : null,
      unreadCount: item['unread_count'] ?? 0,
      updatedAt: DateTime.parse(item['updated_at']),
    )).toList();
  }

  // 获取消息列表
  Future<List<Message>> getMessages(int conversationId, int page, int limit) async {
    final response = await _dio.get(
      '$baseUrl/messages/conversations/$conversationId',
      queryParameters: {
        'page': page,
        'limit': limit,
      },
    );

    final List<dynamic> data = response.data;
    return data.map((item) => _parseMessage(item)).toList();
  }

  // 发送消息
  Future<Message> sendMessage(SendMessageRequest request) async {
    final response = await _dio.post(
      '$baseUrl/messages/send',
      data: {
        'receiver_id': request.receiverId,
        'content': request.content,
        'type': request.type.toString().split('.').last,
      },
    );

    return _parseMessage(response.data);
  }

  // 标记消息为已读
  Future<void> markAsRead(int messageId) async {
    await _dio.put('$baseUrl/messages/$messageId/read');
  }

  // 更新用户资料
  Future<User> updateProfile(Map<String, dynamic> request) async {
    final response = await _dio.put(
      '$baseUrl/users/profile',
      data: request,
    );

    final data = response.data;
    return User(
      id: data['id'],
      username: data['username'],
      email: data['email'],
      nickname: data['nickname'],
      avatar: data['avatar'],
      status: _parseUserStatus(data['status']),
      lastSeen: data['last_seen'] != null ? DateTime.parse(data['last_seen']) : null,
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
    );
  }

  // 解析用户状态
  UserStatus _parseUserStatus(String? status) {
    switch (status) {
      case 'online':
        return UserStatus.online;
      case 'away':
        return UserStatus.away;
      case 'busy':
        return UserStatus.busy;
      default:
        return UserStatus.offline;
    }
  }

  // 解析消息状态
  MessageStatus _parseMessageStatus(String? status) {
    switch (status) {
      case 'sending':
        return MessageStatus.sending;
      case 'sent':
        return MessageStatus.sent;
      case 'delivered':
        return MessageStatus.delivered;
      case 'read':
        return MessageStatus.read;
      case 'failed':
        return MessageStatus.failed;
      default:
        return MessageStatus.sent;
    }
  }

  // 解析消息类型
  MessageType _parseMessageType(String? type) {
    switch (type) {
      case 'image':
        return MessageType.image;
      case 'file':
        return MessageType.file;
      case 'audio':
        return MessageType.audio;
      case 'video':
        return MessageType.video;
      default:
        return MessageType.text;
    }
  }

  // 解析消息对象
  Message _parseMessage(Map<String, dynamic> data) {
    return Message(
      id: data['id'],
      senderId: data['sender_id'],
      receiverId: data['receiver_id'],
      content: data['content'],
      type: _parseMessageType(data['type']),
      isRead: data['is_read'] ?? false,
      readAt: data['read_at'] != null ? DateTime.parse(data['read_at']) : null,
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      sender: User(
        id: data['sender']['id'],
        username: data['sender']['username'],
        email: data['sender']['email'],
        nickname: data['sender']['nickname'],
        avatar: data['sender']['avatar'],
        status: _parseUserStatus(data['sender']['status']),
        createdAt: DateTime.parse(data['sender']['created_at']),
        updatedAt: DateTime.parse(data['sender']['updated_at']),
      ),
      receiver: User(
        id: data['receiver']['id'],
        username: data['receiver']['username'],
        email: data['receiver']['email'],
        nickname: data['receiver']['nickname'],
        avatar: data['receiver']['avatar'],
        status: _parseUserStatus(data['receiver']['status']),
        createdAt: DateTime.parse(data['receiver']['created_at']),
        updatedAt: DateTime.parse(data['receiver']['updated_at']),
      ),
      status: _parseMessageStatus(data['status']),
    );
  }
}
