import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../models/message.dart';

class WebSocketService {
  static const String wsUrl = "ws://********:8080/ws";  // Android模拟器访问宿主机
  
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  
  final StreamController<WSMessage> _messageController = StreamController.broadcast();
  final StreamController<bool> _connectionController = StreamController.broadcast();
  
  Stream<WSMessage> get messageStream => _messageController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  
  bool _isConnected = false;
  String? _accessToken;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectDelay = Duration(seconds: 3);
  static const Duration heartbeatInterval = Duration(seconds: 30);

  bool get isConnected => _isConnected;

  Future<void> connect(String accessToken) async {
    _accessToken = accessToken;
    await _connectWebSocket();
  }

  Future<void> _connectWebSocket() async {
    try {
      if (_channel != null) {
        await disconnect();
      }

      final uri = Uri.parse('$wsUrl?token=$_accessToken');
      _channel = WebSocketChannel.connect(uri);
      
      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _isConnected = true;
      _reconnectAttempts = 0;
      _connectionController.add(true);
      _startHeartbeat();
      
      print('WebSocket connected');
    } catch (e) {
      print('WebSocket connection error: $e');
      _onError(e);
    }
  }

  void _onMessage(dynamic data) {
    try {
      final Map<String, dynamic> json = jsonDecode(data);
      final message = WSMessage.fromJson(json);
      _messageController.add(message);
    } catch (e) {
      print('Error parsing WebSocket message: $e');
    }
  }

  void _onError(dynamic error) {
    print('WebSocket error: $error');
    _isConnected = false;
    _connectionController.add(false);
    _stopHeartbeat();
    _scheduleReconnect();
  }

  void _onDisconnected() {
    print('WebSocket disconnected');
    _isConnected = false;
    _connectionController.add(false);
    _stopHeartbeat();
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    if (_reconnectAttempts < maxReconnectAttempts && _accessToken != null) {
      _reconnectAttempts++;
      print('Scheduling reconnect attempt $_reconnectAttempts');
      
      _reconnectTimer?.cancel();
      _reconnectTimer = Timer(reconnectDelay, () {
        _connectWebSocket();
      });
    } else {
      print('Max reconnect attempts reached or no access token');
    }
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      if (_isConnected) {
        sendMessage(WSMessage(
          type: WSMessageType.message,
          data: {'type': 'ping'},
          timestamp: DateTime.now(),
        ));
      }
    });
  }

  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void sendMessage(WSMessage message) {
    if (_isConnected && _channel != null) {
      try {
        final data = jsonEncode(message.toJson());
        _channel!.sink.add(data);
      } catch (e) {
        print('Error sending WebSocket message: $e');
      }
    } else {
      print('WebSocket not connected, cannot send message');
    }
  }

  void sendTextMessage(int receiverId, String content) {
    sendMessage(WSMessage(
      type: WSMessageType.message,
      data: {
        'receiver_id': receiverId,
        'content': content,
        'type': 'text',
      },
      timestamp: DateTime.now(),
    ));
  }

  void sendTypingStatus(int receiverId, bool isTyping) {
    sendMessage(WSMessage(
      type: WSMessageType.typing,
      data: {
        'receiver_id': receiverId,
        'is_typing': isTyping,
      },
      timestamp: DateTime.now(),
    ));
  }

  void markMessageAsRead(int messageId) {
    sendMessage(WSMessage(
      type: WSMessageType.messageRead,
      data: {
        'message_id': messageId,
      },
      timestamp: DateTime.now(),
    ));
  }

  Future<void> disconnect() async {
    _reconnectTimer?.cancel();
    _stopHeartbeat();
    
    await _subscription?.cancel();
    await _channel?.sink.close(status.goingAway);
    
    _channel = null;
    _subscription = null;
    _isConnected = false;
    _accessToken = null;
    _reconnectAttempts = 0;
    
    _connectionController.add(false);
    print('WebSocket disconnected');
  }

  void dispose() {
    disconnect();
    _messageController.close();
    _connectionController.close();
  }
}
