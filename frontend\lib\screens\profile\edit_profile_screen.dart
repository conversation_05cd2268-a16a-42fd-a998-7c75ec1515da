import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../constants/app_constants.dart';
import '../../models/user.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/slide_animation.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nicknameController = TextEditingController();
  final _bioController = TextEditingController();
  final _phoneController = TextEditingController();
  final _locationController = TextEditingController();
  final _websiteController = TextEditingController();
  final _birthdayController = TextEditingController();

  bool _isLoading = false;
  String? _selectedAvatar;
  UserStatus _selectedStatus = UserStatus.online;
  DateTime? _selectedBirthday;
  String _selectedGender = 'prefer_not_to_say';
  bool _showBirthday = true;
  bool _showLocation = true;
  bool _allowSearchByPhone = true;
  bool _allowSearchByEmail = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _bioController.dispose();
    _phoneController.dispose();
    _locationController.dispose();
    _websiteController.dispose();
    _birthdayController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user != null) {
      _nicknameController.text = user.nickname ?? '';
      _bioController.text = user.bio ?? '';
      _phoneController.text = user.phone ?? '';
      _selectedAvatar = user.avatar;
      _selectedStatus = user.status;

      // 模拟加载额外的用户信息
      _locationController.text = '北京市';
      _websiteController.text = 'https://example.com';
      _selectedBirthday = DateTime(1990, 1, 1);
      _birthdayController.text = '1990-01-01';
      _selectedGender = 'prefer_not_to_say';
    }
  }

  Future<void> _pickAvatar() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedAvatar = image.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('选择头像失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _takePhoto() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedAvatar = image.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('拍照失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showAvatarOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择头像',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            
            ListTile(
              leading: const Icon(Icons.photo_library, color: AppConstants.primaryColor),
              title: const Text('从相册选择'),
              onTap: () {
                Navigator.of(context).pop();
                _pickAvatar();
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.camera_alt, color: AppConstants.primaryColor),
              title: const Text('拍照'),
              onTap: () {
                Navigator.of(context).pop();
                _takePhoto();
              },
            ),
            
            if (_selectedAvatar != null)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('移除头像'),
                onTap: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _selectedAvatar = null;
                  });
                },
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    // 显示确认对话框
    final bool? confirmed = await _showSaveConfirmDialog();
    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser!;

      // 创建更新后的用户对象
      final updatedUser = currentUser.copyWith(
        nickname: _nicknameController.text.trim().isEmpty
            ? null
            : _nicknameController.text.trim(),
        bio: _bioController.text.trim().isEmpty
            ? null
            : _bioController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        avatar: _selectedAvatar,
        status: _selectedStatus,
      );

      // 模拟API调用和头像上传
      await Future.delayed(const Duration(seconds: 2));

      // 更新用户信息
      authProvider.updateUser(updatedUser);

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('个人资料已更新'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: '查看',
              textColor: Colors.white,
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('更新失败: ${e.toString()}')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: '重试',
              textColor: Colors.white,
              onPressed: _saveProfile,
            ),
          ),
        );
      }
    }
  }

  Future<bool?> _showSaveConfirmDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('保存更改'),
        content: const Text('确定要保存对个人资料的更改吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return '在线';
      case UserStatus.away:
        return '离开';
      case UserStatus.busy:
        return '忙碌';
      case UserStatus.offline:
        return '离线';
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return Colors.green;
      case UserStatus.away:
        return Colors.orange;
      case UserStatus.busy:
        return Colors.red;
      case UserStatus.offline:
        return Colors.grey;
    }
  }

  Future<void> _selectBirthday() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedBirthday ?? DateTime(1990, 1, 1),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      locale: const Locale('zh', 'CN'),
    );

    if (picked != null && picked != _selectedBirthday) {
      setState(() {
        _selectedBirthday = picked;
        _birthdayController.text = '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
      });
    }
  }

  String _getGenderText(String gender) {
    switch (gender) {
      case 'male':
        return '男';
      case 'female':
        return '女';
      case 'prefer_not_to_say':
        return '不愿透露';
      default:
        return '不愿透露';
    }
  }

  void _showGenderPicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择性别',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),

            ListTile(
              leading: const Icon(Icons.male, color: Colors.blue),
              title: const Text('男'),
              onTap: () {
                setState(() {
                  _selectedGender = 'male';
                });
                Navigator.of(context).pop();
              },
            ),

            ListTile(
              leading: const Icon(Icons.female, color: Colors.pink),
              title: const Text('女'),
              onTap: () {
                setState(() {
                  _selectedGender = 'female';
                });
                Navigator.of(context).pop();
              },
            ),

            ListTile(
              leading: const Icon(Icons.help_outline, color: Colors.grey),
              title: const Text('不愿透露'),
              onTap: () {
                setState(() {
                  _selectedGender = 'prefer_not_to_say';
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarImage() {
    if (_selectedAvatar!.startsWith('http')) {
      // 网络图片
      return Image.network(
        _selectedAvatar!,
        width: 120,
        height: 120,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 120,
            height: 120,
            color: AppConstants.primaryColor.withValues(alpha: 0.1),
            child: Icon(
              Icons.person,
              size: 60,
              color: AppConstants.primaryColor,
            ),
          );
        },
      );
    } else {
      // 本地文件
      return Image.file(
        File(_selectedAvatar!),
        width: 120,
        height: 120,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 120,
            height: 120,
            color: AppConstants.primaryColor.withValues(alpha: 0.1),
            child: Icon(
              Icons.person,
              size: 60,
              color: AppConstants.primaryColor,
            ),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      return const Scaffold(
        body: Center(child: Text('用户信息加载失败')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('编辑资料'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveProfile,
              child: const Text(
                '保存',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 头像部分
            SlideInAnimation(
              begin: const Offset(0.0, -1.0),
              child: Center(
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: _showAvatarOptions,
                      child: Stack(
                        children: [
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppConstants.primaryColor,
                                width: 3,
                              ),
                            ),
                            child: ClipOval(
                              child: _selectedAvatar != null
                                  ? _buildAvatarImage()
                                  : Container(
                                      width: 120,
                                      height: 120,
                                      color: AppConstants.primaryColor.withValues(alpha: 0.1),
                                      child: Icon(
                                        Icons.person,
                                        size: 60,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                color: AppConstants.primaryColor,
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white, width: 2),
                              ),
                              child: const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '点击更换头像',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),

            // 基本信息
            SlideInAnimation(
              begin: const Offset(-1.0, 0.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '基本信息',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // 用户名（不可编辑）
                      TextFormField(
                        initialValue: currentUser.username,
                        decoration: const InputDecoration(
                          labelText: '用户名',
                          prefixIcon: Icon(Icons.account_circle),
                          border: OutlineInputBorder(),
                        ),
                        enabled: false,
                      ),
                      const SizedBox(height: 16),
                      
                      // 昵称
                      TextFormField(
                        controller: _nicknameController,
                        decoration: const InputDecoration(
                          labelText: '昵称',
                          prefixIcon: Icon(Icons.person),
                          border: OutlineInputBorder(),
                          hintText: '请输入昵称',
                        ),
                        maxLength: 20,
                        validator: (value) {
                          if (value != null && value.length > 20) {
                            return '昵称不能超过20个字符';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // 个人简介
                      TextFormField(
                        controller: _bioController,
                        decoration: const InputDecoration(
                          labelText: '个人简介',
                          prefixIcon: Icon(Icons.info),
                          border: OutlineInputBorder(),
                          hintText: '介绍一下自己吧',
                        ),
                        maxLines: 3,
                        maxLength: 100,
                        validator: (value) {
                          if (value != null && value.length > 100) {
                            return '个人简介不能超过100个字符';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 联系信息
            SlideInAnimation(
              begin: const Offset(1.0, 0.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '联系信息',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // 邮箱（不可编辑）
                      TextFormField(
                        initialValue: currentUser.email,
                        decoration: const InputDecoration(
                          labelText: '邮箱',
                          prefixIcon: Icon(Icons.email),
                          border: OutlineInputBorder(),
                        ),
                        enabled: false,
                      ),
                      const SizedBox(height: 16),
                      
                      // 手机号
                      TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: '手机号',
                          prefixIcon: Icon(Icons.phone),
                          border: OutlineInputBorder(),
                          hintText: '请输入手机号',
                        ),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                              return '请输入正确的手机号';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // 个人网站
                      TextFormField(
                        controller: _websiteController,
                        decoration: const InputDecoration(
                          labelText: '个人网站',
                          prefixIcon: Icon(Icons.link),
                          border: OutlineInputBorder(),
                          hintText: '请输入个人网站链接',
                        ),
                        keyboardType: TextInputType.url,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final uri = Uri.tryParse(value);
                            if (uri == null || !uri.hasAbsolutePath || (!uri.scheme.startsWith('http'))) {
                              return '请输入有效的网站链接';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // 所在地
                      TextFormField(
                        controller: _locationController,
                        decoration: const InputDecoration(
                          labelText: '所在地',
                          prefixIcon: Icon(Icons.location_on),
                          border: OutlineInputBorder(),
                          hintText: '请输入所在地',
                        ),
                        maxLength: 50,
                        validator: (value) {
                          if (value != null && value.length > 50) {
                            return '所在地不能超过50个字符';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 个人详情
            SlideInAnimation(
              begin: const Offset(-1.0, 0.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '个人详情',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 生日
                      GestureDetector(
                        onTap: _selectBirthday,
                        child: AbsorbPointer(
                          child: TextFormField(
                            controller: _birthdayController,
                            decoration: const InputDecoration(
                              labelText: '生日',
                              prefixIcon: Icon(Icons.cake),
                              border: OutlineInputBorder(),
                              hintText: '请选择生日',
                              suffixIcon: Icon(Icons.calendar_today),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 性别
                      GestureDetector(
                        onTap: _showGenderPicker,
                        child: AbsorbPointer(
                          child: TextFormField(
                            decoration: InputDecoration(
                              labelText: '性别',
                              prefixIcon: const Icon(Icons.person_outline),
                              border: const OutlineInputBorder(),
                              hintText: '请选择性别',
                              suffixIcon: const Icon(Icons.arrow_drop_down),
                            ),
                            controller: TextEditingController(text: _getGenderText(_selectedGender)),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 隐私设置
            SlideInAnimation(
              begin: const Offset(1.0, 0.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '隐私设置',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),

                      SwitchListTile(
                        title: const Text('显示生日'),
                        subtitle: const Text('允许其他人查看您的生日'),
                        value: _showBirthday,
                        onChanged: (value) {
                          setState(() {
                            _showBirthday = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                      ),

                      SwitchListTile(
                        title: const Text('显示所在地'),
                        subtitle: const Text('允许其他人查看您的所在地'),
                        value: _showLocation,
                        onChanged: (value) {
                          setState(() {
                            _showLocation = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                      ),

                      SwitchListTile(
                        title: const Text('允许通过手机号搜索'),
                        subtitle: const Text('其他人可以通过手机号找到您'),
                        value: _allowSearchByPhone,
                        onChanged: (value) {
                          setState(() {
                            _allowSearchByPhone = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                      ),

                      SwitchListTile(
                        title: const Text('允许通过邮箱搜索'),
                        subtitle: const Text('其他人可以通过邮箱找到您'),
                        value: _allowSearchByEmail,
                        onChanged: (value) {
                          setState(() {
                            _allowSearchByEmail = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 状态设置
            SlideInAnimation(
              begin: const Offset(0.0, 1.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '在线状态',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      ...UserStatus.values.map((status) => RadioListTile<UserStatus>(
                        title: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: _getStatusColor(status),
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(_getStatusText(status)),
                          ],
                        ),
                        value: status,
                        groupValue: _selectedStatus,
                        onChanged: (UserStatus? value) {
                          if (value != null) {
                            setState(() {
                              _selectedStatus = value;
                            });
                          }
                        },
                        activeColor: AppConstants.primaryColor,
                      )),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
