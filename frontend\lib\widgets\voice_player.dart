import 'package:flutter/material.dart';
import 'dart:async';
import '../constants/app_constants.dart';

enum VoicePlayerState {
  stopped,
  playing,
  paused,
  loading,
}

class VoicePlayer extends StatefulWidget {
  final String audioPath;
  final Duration duration;
  final bool isFromMe;

  const VoicePlayer({
    super.key,
    required this.audioPath,
    required this.duration,
    this.isFromMe = false,
  });

  @override
  State<VoicePlayer> createState() => _VoicePlayerState();
}

class _VoicePlayerState extends State<VoicePlayer>
    with SingleTickerProviderStateMixin {
  VoicePlayerState _state = VoicePlayerState.stopped;
  Duration _currentPosition = Duration.zero;
  Timer? _timer;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _togglePlayback() async {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    switch (_state) {
      case VoicePlayerState.stopped:
        await _startPlayback();
        break;
      case VoicePlayerState.playing:
        _pausePlayback();
        break;
      case VoicePlayerState.paused:
        _resumePlayback();
        break;
      case VoicePlayerState.loading:
        // 加载中，不做任何操作
        break;
    }
  }

  Future<void> _startPlayback() async {
    setState(() {
      _state = VoicePlayerState.loading;
    });

    // 模拟加载延迟
    await Future.delayed(const Duration(milliseconds: 300));

    setState(() {
      _state = VoicePlayerState.playing;
      _currentPosition = Duration.zero;
    });

    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _currentPosition += const Duration(milliseconds: 100);
        
        if (_currentPosition >= widget.duration) {
          _currentPosition = widget.duration;
          _state = VoicePlayerState.stopped;
          timer.cancel();
        }
      });
    });
  }

  void _pausePlayback() {
    setState(() {
      _state = VoicePlayerState.paused;
    });
    _timer?.cancel();
  }

  void _resumePlayback() {
    setState(() {
      _state = VoicePlayerState.playing;
    });

    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _currentPosition += const Duration(milliseconds: 100);
        
        if (_currentPosition >= widget.duration) {
          _currentPosition = widget.duration;
          _state = VoicePlayerState.stopped;
          timer.cancel();
        }
      });
    });
  }

  void _seekTo(double value) {
    final newPosition = Duration(
      milliseconds: (value * widget.duration.inMilliseconds).round(),
    );
    
    setState(() {
      _currentPosition = newPosition;
    });

    // 如果正在播放，重新开始计时
    if (_state == VoicePlayerState.playing) {
      _timer?.cancel();
      _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
        setState(() {
          _currentPosition += const Duration(milliseconds: 100);
          
          if (_currentPosition >= widget.duration) {
            _currentPosition = widget.duration;
            _state = VoicePlayerState.stopped;
            timer.cancel();
          }
        });
      });
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  IconData _getPlayIcon() {
    switch (_state) {
      case VoicePlayerState.stopped:
        return Icons.play_arrow;
      case VoicePlayerState.playing:
        return Icons.pause;
      case VoicePlayerState.paused:
        return Icons.play_arrow;
      case VoicePlayerState.loading:
        return Icons.hourglass_empty;
    }
  }

  Color _getIconColor() {
    return widget.isFromMe ? Colors.white : AppConstants.primaryColor;
  }

  Color _getProgressColor() {
    return widget.isFromMe 
        ? Colors.white.withValues(alpha: 0.8)
        : AppConstants.primaryColor;
  }

  Color _getBackgroundColor() {
    return widget.isFromMe 
        ? Colors.white.withValues(alpha: 0.2)
        : Colors.grey.withValues(alpha: 0.2);
  }

  @override
  Widget build(BuildContext context) {
    final progress = widget.duration.inMilliseconds > 0
        ? _currentPosition.inMilliseconds / widget.duration.inMilliseconds
        : 0.0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 播放/暂停按钮
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: GestureDetector(
                  onTap: _togglePlayback,
                  child: Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: _getIconColor().withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: _state == VoicePlayerState.loading
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getIconColor(),
                              ),
                            ),
                          )
                        : Icon(
                            _getPlayIcon(),
                            color: _getIconColor(),
                            size: 20,
                          ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 8),
          
          // 波形和进度条
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 简化的波形显示
                Container(
                  height: 20,
                  child: CustomPaint(
                    painter: SimpleWaveformPainter(
                      progress: progress,
                      color: _getProgressColor(),
                      backgroundColor: _getIconColor().withValues(alpha: 0.3),
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                
                // 时间显示
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(_currentPosition),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getIconColor().withValues(alpha: 0.8),
                      ),
                    ),
                    Text(
                      _formatDuration(widget.duration),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getIconColor().withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          
          // 语音图标
          Icon(
            Icons.mic,
            color: _getIconColor().withValues(alpha: 0.6),
            size: 16,
          ),
        ],
      ),
    );
  }
}

class SimpleWaveformPainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;

  SimpleWaveformPainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final progressPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 绘制简化的波形
    const barCount = 20;
    final barWidth = size.width / barCount;
    final progressWidth = size.width * progress;

    for (int i = 0; i < barCount; i++) {
      final x = i * barWidth;
      final barHeight = size.height * (0.3 + (i % 3) * 0.2); // 简单的高度变化
      final y = (size.height - barHeight) / 2;

      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(x, y, barWidth - 1, barHeight),
        const Radius.circular(1),
      );

      // 根据进度选择颜色
      final paint = x < progressWidth ? progressPaint : backgroundPaint;
      canvas.drawRRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
