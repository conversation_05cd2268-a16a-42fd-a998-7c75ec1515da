{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "path_provider_foundation", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "path_provider_android", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\sqflite_android-2.4.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "path_provider_foundation", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "path_provider_linux", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "path_provider_windows", "path": "C:\\\\src\\\\flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}], "web": []}, "dependencyGraph": [{"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}], "date_created": "2025-07-04 15:20:03.823037", "version": "3.32.4", "swift_package_manager_enabled": {"ios": false, "macos": false}}