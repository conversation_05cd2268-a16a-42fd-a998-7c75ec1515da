#!/bin/bash

# AqiChat 部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev, staging, prod
# 操作: start, stop, restart, logs, status

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认值
ENVIRONMENT=${1:-dev}
ACTION=${2:-start}
PROJECT_NAME="aqichat"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    case $ENVIRONMENT in
        dev)
            export COMPOSE_PROJECT_NAME="${PROJECT_NAME}_dev"
            export DATABASE_URL="postgres://aqichat_user:aqichat_password@localhost:5432/aqichat?sslmode=disable"
            export JWT_SECRET="dev-jwt-secret-key"
            ;;
        staging)
            export COMPOSE_PROJECT_NAME="${PROJECT_NAME}_staging"
            export DATABASE_URL="postgres://aqichat_user:aqichat_password@localhost:5432/aqichat_staging?sslmode=disable"
            export JWT_SECRET="staging-jwt-secret-key"
            ;;
        prod)
            export COMPOSE_PROJECT_NAME="${PROJECT_NAME}_prod"
            # 生产环境应该从安全的地方读取这些值
            if [ -f ".env.prod" ]; then
                source .env.prod
            else
                log_error "生产环境配置文件 .env.prod 不存在"
                exit 1
            fi
            ;;
        *)
            log_error "未知环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    log_success "环境变量设置完成: $ENVIRONMENT"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    cd docker
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    cd docker
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services_health
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    cd docker
    docker-compose down
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    stop_services
    start_services
    
    log_success "服务重启完成"
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    
    cd docker
    docker-compose logs -f --tail=100
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    
    cd docker
    docker-compose ps
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    # 检查后端API
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8080/health > /dev/null; then
            log_success "后端服务健康检查通过"
            break
        else
            log_warning "等待后端服务启动... ($attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "后端服务健康检查失败"
        return 1
    fi
    
    # 检查数据库连接
    if docker-compose exec -T postgres pg_isready -U aqichat_user -d aqichat > /dev/null; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    log_success "所有服务健康检查通过"
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    cd docker
    docker-compose exec backend ./main migrate
    
    log_success "数据库迁移完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    cd docker
    docker-compose exec -T postgres pg_dump -U aqichat_user aqichat > "../backups/$backup_file"
    
    log_success "数据库备份完成: $backup_file"
}

# 清理
cleanup() {
    log_info "清理资源..."
    
    cd docker
    docker-compose down -v --remove-orphans
    docker system prune -f
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "AqiChat 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  dev      开发环境 (默认)"
    echo "  staging  测试环境"
    echo "  prod     生产环境"
    echo ""
    echo "操作:"
    echo "  start    启动服务 (默认)"
    echo "  stop     停止服务"
    echo "  restart  重启服务"
    echo "  logs     查看日志"
    echo "  status   查看状态"
    echo "  build    构建镜像"
    echo "  migrate  数据库迁移"
    echo "  backup   备份数据库"
    echo "  cleanup  清理资源"
    echo "  help     显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 dev start"
    echo "  $0 prod restart"
    echo "  $0 staging logs"
}

# 主函数
main() {
    case $ACTION in
        start)
            check_dependencies
            setup_environment
            start_services
            ;;
        stop)
            setup_environment
            stop_services
            ;;
        restart)
            check_dependencies
            setup_environment
            restart_services
            ;;
        logs)
            setup_environment
            show_logs
            ;;
        status)
            setup_environment
            check_status
            ;;
        build)
            check_dependencies
            setup_environment
            build_images
            ;;
        migrate)
            setup_environment
            migrate_database
            ;;
        backup)
            setup_environment
            backup_database
            ;;
        cleanup)
            setup_environment
            cleanup
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 创建必要的目录
mkdir -p backups
mkdir -p logs

# 执行主函数
main
