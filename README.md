# AqiChat - 跨平台聊天软件

一个基于Flutter+Dart前端和Go后端的跨平台聊天应用，支持移动端和桌面端。

## 项目架构

```
aqichat/
├── frontend/           # Flutter前端项目
│   ├── lib/
│   │   ├── main.dart
│   │   ├── models/     # 数据模型
│   │   ├── screens/    # 页面UI
│   │   ├── widgets/    # 自定义组件
│   │   ├── services/   # 网络服务
│   │   ├── providers/  # 状态管理
│   │   └── utils/      # 工具类
│   ├── android/        # Android配置
│   ├── ios/           # iOS配置
│   ├── windows/       # Windows配置
│   ├── macos/         # macOS配置
│   └── linux/         # Linux配置
├── backend/           # Go后端项目
│   ├── main.go
│   ├── handlers/      # HTTP处理器
│   ├── models/        # 数据模型
│   ├── services/      # 业务逻辑
│   ├── middleware/    # 中间件
│   ├── database/      # 数据库相关
│   ├── websocket/     # WebSocket处理
│   └── utils/         # 工具函数
├── docs/              # 项目文档
└── docker/            # Docker配置
```

## 技术栈

### 前端 (Flutter)
- **框架**: Flutter 3.x
- **语言**: Dart
- **状态管理**: Provider / Riverpod
- **网络请求**: Dio
- **WebSocket**: web_socket_channel
- **本地存储**: SharedPreferences / Hive
- **UI组件**: Material Design 3

### 后端 (Go)
- **框架**: Gin
- **数据库**: PostgreSQL
- **ORM**: GORM
- **WebSocket**: Gorilla WebSocket
- **认证**: JWT
- **配置管理**: Viper
- **日志**: Logrus

### 数据库
- **主数据库**: PostgreSQL
- **缓存**: Redis (可选)

## 核心功能

### 第一阶段 (MVP)
- [x] 用户注册/登录
- [x] 好友管理
- [x] 一对一聊天
- [x] 消息历史记录
- [x] 实时消息推送

### 第二阶段
- [ ] 群聊功能
- [ ] 文件传输
- [ ] 语音消息
- [ ] 消息加密
- [ ] 离线消息

### 第三阶段
- [ ] 视频通话
- [ ] 屏幕共享
- [ ] 消息撤回
- [ ] 消息转发
- [ ] 表情包

## 快速开始

### 环境要求
- Flutter SDK 3.0+
- Go 1.19+
- PostgreSQL 13+
- Docker & Docker Compose (推荐)
- Git

### 方式一：Docker 部署（推荐）

1. 克隆项目
```bash
git clone <repository-url>
cd aqichat
```

2. 配置环境
```bash
cp .env.example .env.dev
# 编辑 .env.dev 文件设置数据库等配置
```

3. 启动所有服务
```bash
./scripts/deploy.sh dev start
```

4. 验证部署
```bash
./scripts/test_api.sh test
```

### 方式二：手动部署

1. 启动数据库
```bash
# 使用 Docker 启动 PostgreSQL
docker run -d --name aqichat-postgres \
  -e POSTGRES_DB=aqichat \
  -e POSTGRES_USER=aqichat_user \
  -e POSTGRES_PASSWORD=aqichat_password \
  -p 5432:5432 postgres:15-alpine
```

2. 启动后端
```bash
cd backend
go mod tidy
export DATABASE_URL="postgres://aqichat_user:aqichat_password@localhost:5432/aqichat?sslmode=disable"
export JWT_SECRET="your-secret-key"
go run main.go
```

3. 启动前端
```bash
cd frontend
flutter pub get
flutter run
```

### 访问应用

- **后端API**: http://localhost:8080
- **WebSocket**: ws://localhost:8080/ws
- **健康检查**: http://localhost:8080/health
- **Flutter应用**: 根据 `flutter run` 输出的地址访问

## 开发规范

### Git提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

### 代码规范
- 前端遵循Dart官方代码规范
- 后端遵循Go官方代码规范
- 使用有意义的变量和函数命名
- 添加必要的注释

## 部署

### Docker部署
```bash
docker-compose up -d
```

### 手动部署
详见 `docs/deployment.md`

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 项目状态

✅ **已完成功能**
- 用户注册/登录系统
- JWT认证机制
- 好友管理（添加、删除、搜索）
- 实时聊天功能
- WebSocket实时通信
- 消息历史记录
- 跨平台支持（Android、iOS、Windows、macOS、Linux）
- Docker容器化部署
- API文档和部署文档

🚧 **待开发功能**
- 群聊功能
- 文件传输
- 语音/视频通话
- 消息加密
- 推送通知
- 表情包支持

## 测试

### 后端API测试
```bash
# 运行API测试脚本
./scripts/test_api.sh test
```

### Flutter应用测试
```bash
cd frontend
flutter test
```

## 部署

详细部署说明请参考 [部署文档](docs/deployment.md)

### 开发环境
```bash
./scripts/deploy.sh dev start
```

### 生产环境
```bash
./scripts/deploy.sh prod start
```

## API文档

详细API文档请参考 [API文档](docs/api.md)

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看 [部署文档](docs/deployment.md) 和 [API文档](docs/api.md)
2. 检查 [Issues](../../issues) 中是否有类似问题
3. 创建新的 Issue 描述您的问题

## 许可证

MIT License

## 致谢

感谢所有为这个项目做出贡献的开发者！
