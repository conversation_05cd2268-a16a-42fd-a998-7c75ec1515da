import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../models/message.dart';
import '../../models/user.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/message_bubble.dart';
import '../../widgets/loading_animation.dart';
import '../../widgets/slide_animation.dart';
import '../../services/mock_api_service.dart';

class MessageSearchScreen extends StatefulWidget {
  const MessageSearchScreen({super.key});

  @override
  State<MessageSearchScreen> createState() => _MessageSearchScreenState();
}

class _MessageSearchScreenState extends State<MessageSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final MockApiService _mockApi = MockApiService();
  
  List<Message> _searchResults = [];
  List<String> _recentSearches = [];
  bool _isLoading = false;
  bool _hasSearched = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _loadRecentSearches();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadRecentSearches() async {
    // 模拟加载最近搜索记录
    setState(() {
      _recentSearches = [
        '工作安排',
        '会议时间',
        '项目进度',
        '周末聚会',
      ];
    });
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _hasSearched = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _currentQuery = query.trim();
    });

    try {
      // 模拟搜索延迟
      await Future.delayed(const Duration(milliseconds: 800));
      
      // 模拟搜索结果
      final results = await _mockSearchMessages(query.trim());
      
      // 添加到最近搜索
      if (!_recentSearches.contains(query.trim())) {
        setState(() {
          _recentSearches.insert(0, query.trim());
          if (_recentSearches.length > 10) {
            _recentSearches = _recentSearches.take(10).toList();
          }
        });
      }

      setState(() {
        _searchResults = results;
        _hasSearched = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasSearched = true;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('搜索失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<List<Message>> _mockSearchMessages(String query) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser!;
    
    // 模拟搜索结果
    final mockResults = <Message>[];
    
    if (query.contains('工作') || query.contains('项目')) {
      mockResults.addAll([
        Message(
          id: 1,
          senderId: 2,
          receiverId: currentUser.id,
          content: '今天的工作安排已经发给你了，请查收',
          type: MessageType.text,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
          sender: _getMockUser(2),
          receiver: currentUser,
          isRead: true,
        ),
        Message(
          id: 2,
          senderId: 3,
          receiverId: currentUser.id,
          content: '项目进度报告需要在明天之前提交',
          type: MessageType.text,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
          sender: _getMockUser(3),
          receiver: currentUser,
          isRead: true,
        ),
      ]);
    }
    
    if (query.contains('会议') || query.contains('时间')) {
      mockResults.add(
        Message(
          id: 3,
          senderId: 4,
          receiverId: currentUser.id,
          content: '明天下午3点开会议，请准时参加',
          type: MessageType.text,
          createdAt: DateTime.now().subtract(const Duration(hours: 5)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
          sender: _getMockUser(4),
          receiver: currentUser,
          isRead: true,
        ),
      );
    }
    
    if (query.contains('聚会') || query.contains('周末')) {
      mockResults.add(
        Message(
          id: 4,
          senderId: 2,
          receiverId: currentUser.id,
          content: '周末聚会的地点定在市中心的那家餐厅',
          type: MessageType.text,
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          updatedAt: DateTime.now().subtract(const Duration(days: 2)),
          sender: _getMockUser(2),
          receiver: currentUser,
          isRead: true,
        ),
      );
    }
    
    return mockResults;
  }

  User _getMockUser(int id) {
    switch (id) {
      case 2:
        return User(
          id: 2,
          username: 'alice_wonder',
          email: '<EMAIL>',
          nickname: 'Alice Wonder',
          avatar: '',
          status: UserStatus.online,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      case 3:
        return User(
          id: 3,
          username: 'bob_builder',
          email: '<EMAIL>',
          nickname: 'Bob Builder',
          avatar: '',
          status: UserStatus.away,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      case 4:
        return User(
          id: 4,
          username: 'charlie_brown',
          email: '<EMAIL>',
          nickname: 'Charlie Brown',
          avatar: '',
          status: UserStatus.offline,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      default:
        return User(
          id: id,
          username: 'user$id',
          email: 'user$<EMAIL>',
          nickname: 'User $id',
          avatar: '',
          status: UserStatus.online,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
    }
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchResults = [];
      _hasSearched = false;
      _currentQuery = '';
    });
  }

  void _clearRecentSearches() {
    setState(() {
      _recentSearches = [];
    });
  }

  void _selectRecentSearch(String query) {
    _searchController.text = query;
    _performSearch(query);
  }

  Widget _highlightText(String text, String query) {
    if (query.isEmpty) return Text(text);
    
    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    
    if (!lowerText.contains(lowerQuery)) {
      return Text(text);
    }
    
    final spans = <TextSpan>[];
    int start = 0;
    
    while (true) {
      final index = lowerText.indexOf(lowerQuery, start);
      if (index == -1) {
        spans.add(TextSpan(text: text.substring(start)));
        break;
      }
      
      if (index > start) {
        spans.add(TextSpan(text: text.substring(start, index)));
      }
      
      spans.add(TextSpan(
        text: text.substring(index, index + query.length),
        style: const TextStyle(
          backgroundColor: Colors.yellow,
          fontWeight: FontWeight.bold,
        ),
      ));
      
      start = index + query.length;
    }
    
    return RichText(text: TextSpan(children: spans, style: DefaultTextStyle.of(context).style));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('搜索消息'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索消息内容...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: _clearSearch,
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) {
                setState(() {});
              },
              onSubmitted: _performSearch,
              textInputAction: TextInputAction.search,
            ),
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingAnimation(
          size: 60,
          message: '搜索中...',
        ),
      );
    }

    if (!_hasSearched) {
      return _buildRecentSearches();
    }

    if (_searchResults.isEmpty) {
      return _buildNoResults();
    }

    return _buildSearchResults();
  }

  Widget _buildRecentSearches() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_recentSearches.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '最近搜索',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: _clearRecentSearches,
                  child: const Text('清空'),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _recentSearches.length,
              itemBuilder: (context, index) {
                final search = _recentSearches[index];
                return ListItemAnimation(
                  index: index,
                  child: ListTile(
                    leading: const Icon(Icons.history, color: Colors.grey),
                    title: Text(search),
                    trailing: IconButton(
                      icon: const Icon(Icons.north_west, size: 16),
                      onPressed: () => _selectRecentSearch(search),
                    ),
                    onTap: () => _selectRecentSearch(search),
                  ),
                );
              },
            ),
          ),
        ] else ...[
          const Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '搜索消息内容',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '输入关键词搜索聊天记录',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            '未找到相关消息',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '尝试使用其他关键词搜索',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            '找到 ${_searchResults.length} 条相关消息',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final message = _searchResults[index];
              final authProvider = Provider.of<AuthProvider>(context, listen: false);
              final isMe = message.senderId == authProvider.currentUser?.id;

              return ListItemAnimation(
                index: index,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 消息发送者和时间
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                        child: Row(
                          children: [
                            Text(
                              isMe ? '我' : (message.sender.nickname ?? message.sender.username),
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              _formatTime(message.createdAt),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // 消息内容（高亮关键词）
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        child: _highlightText(message.content, _currentQuery),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
