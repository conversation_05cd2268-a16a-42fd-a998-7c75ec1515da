{"inputs": ["D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\.dart_tool\\flutter_build\\fbfbcae587d40e1d9df02cbf58f8ad61\\app.dill", "D:\\FlutterSdk\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "D:\\FlutterSdk\\flutter\\bin\\cache\\engine.stamp", "D:\\FlutterSdk\\flutter\\bin\\cache\\engine.stamp", "D:\\FlutterSdk\\flutter\\bin\\cache\\engine.stamp", "D:\\FlutterSdk\\flutter\\bin\\cache\\engine.stamp", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\pubspec.yaml", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "D:\\FlutterSdk\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\.dart_tool\\flutter_build\\fbfbcae587d40e1d9df02cbf58f8ad61\\native_assets.json", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\boolean_selector-2.1.2\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image-3.4.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image_platform_interface-4.1.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image_web-1.3.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio_web_adapter-2.1.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fake_async-1.3.3\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_lints-5.0.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\intl-0.19.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\leak_tracker-10.0.9\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\lints-5.1.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\matcher-0.12.17\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\meta-1.16.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\nested-1.0.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.5\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_android-2.4.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_darwin-2.4.2\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stack_trace-1.12.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\test_api-0.7.4\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vm_service-15.0.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web-1.1.1\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\LICENSE", "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\xdg_directories-1.1.0\\LICENSE", "D:\\FlutterSdk\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "D:\\FlutterSdk\\flutter\\packages\\flutter\\LICENSE"], "outputs": ["D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}