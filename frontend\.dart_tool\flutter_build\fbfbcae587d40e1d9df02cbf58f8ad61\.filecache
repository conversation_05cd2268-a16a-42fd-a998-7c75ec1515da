{"version": 2, "files": [{"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "hash": "3afe080744ca0b8ff964e17b1ce3dec4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "hash": "9051680cd2078f92c9c56831272643d5"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\.dart_tool\\package_config_subset", "hash": "22ee04a3b31fcfe80c3c17e5eb286960"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "hash": "9b89a526ec4c8a6c46b3a4bcf3cca0b5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_android-2.4.1\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart", "hash": "8bb77b9696ef695fe266346f1f4f818c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "hash": "35e08f69f693f32fd66c73e92bbeac3d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\lib\\src\\utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "hash": "9b9ab2c0595d95cef9e27ae03e36991d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "hash": "aec52d42480a7d3a5be9e7eae6370ebe"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "hash": "f0a22d14bdc393cf0ac6b80d64c2d742"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\widgets\\image_message.dart", "hash": "61249115e47c29c46cd4cd907664d3a3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "hash": "1d7927056badbebd8f79686e3ee05ffc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "hash": "9621a4c337d24a926ff50126ce88d7fe"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\utils.dart", "hash": "599be812b0d48a34af027e2c896771e9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart", "hash": "054abd6fdcd55ea92434e7f591f3785b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "hash": "8351b22319b7a91a7b398c3dcccbc3af"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "hash": "df77eb750191b494da865a219383e691"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart", "hash": "d073924ebcc169bd1a47c3f695925478"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "hash": "e7ca145d8d308187bc0127bd941b1759"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\slider_value_indicator_shape.dart", "hash": "8288f862fad2f1a37af607a60b8ab395"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "hash": "a5b2132b6c446c705361301cb9c2e261"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "hash": "dcb90d146b259cd68b023ffa77428ab8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "hash": "4326580ee93b3a584477cc185146eb2f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\logger.dart", "hash": "0cdc9d79f7fc4d0920bc6a8fc02e6872"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "hash": "482df57db20848c7bbf606b03e258a69"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "hash": "0fbda02660f6ca3d9bb5b9ce5ab88548"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart", "hash": "0d91c8c6ebb2c8abf67598e76180c4f5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\src\\copy\\io_sink.dart", "hash": "32f085d3870d940e473ec08f067bd7ab"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\non_storing_object_provider.dart", "hash": "21cb059be81989938ccfbda405ae9a65"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "hash": "7975814e8804652eda7f8b726d3913b2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart", "hash": "082f7b9db25627e7eefcba106260a079"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "hash": "492d0c49593bf708c5b65837efbabd39"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\lib\\src\\extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "hash": "f96d4d30da8226fee102c4c76dddc6dc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "hash": "2babfd7c893533e7f7b3d1edcc075b64"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\widgets\\connection_status.dart", "hash": "7efca5b136629b302777154105491ced"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "hash": "bfd7a00b9fef90dbf3b6d14f6a2f2901"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "hash": "ee424e7e1baf8744e2386a37728238fc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\src\\typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "hash": "a400dfa676904fa8a84c48146ff8e554"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "hash": "c5331010d9a48f689ab698e89196a9d7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "hash": "ada88e231d33ef7c2855cecc98e8c6a2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\scheduler.dart", "hash": "3ac176a9235973980af3b75bd0c237ff"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\delegate\\sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\stream_channel_transformer.dart", "hash": "bf9deb76f520208269fedb1ee05992bc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\cache_info_repository.dart", "hash": "d423d24bacc39262f492386b09a7ee7b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "hash": "cbeff2d69d62fda433fa85b2fa331821"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "hash": "f0092135c4cff7e4933b157a9d09ce9a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\lib\\ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "hash": "13dd9b1f72f655ece9886a13c5bd2018"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "hash": "a3f622bd8417a8f8a1789761a4cb572a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "hash": "b11666131177a6ebe97ffd60e3dac32a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\options.dart", "hash": "fd4b31aeef96e63881bfcd44031ae269"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\home\\profile_screen.dart", "hash": "4b9852548860e64c6d95eb4a362b0ddf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "hash": "e47677f74fdbf02610d897c45cbf26e8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "hash": "c2253d864a54af232fe869aeafd95a61"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\compute\\compute.dart", "hash": "12b8cbac25c7ad95ce53c2f8869a1b5d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\lib\\term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "hash": "021f3c262c5cd50dc0ad2764995358c6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "hash": "4171ccc0ef5ccf7a3355589667cc0450"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "hash": "1ea047f0b3833f762bb5bff4a50d7707"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "hash": "c613d7434cec89361a3b831cc91f86cc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\platform\\platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "hash": "3f591c8127c07a81900c2b23dc82a909"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart", "hash": "53c1ff11b2873a67a9e4c0277a31a856"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\friends\\search_users_screen.dart", "hash": "5cddb2919d9d8ded32e70c08042ff78e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "hash": "beb0225376869e0c92a19831596494dd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "hash": "034c9a74f518b43df9ce8af9343c11cd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\lints-5.1.1\\LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "hash": "de48d59f2b8a22c29dd37492fb4443ec"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "hash": "d932135a74692037b9bbbbb8fffffd5d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "hash": "feaa27101434fc1590c19d42ec8b407f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\utils\\utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "hash": "c15b0c06c9817813063ea4c1023d57fa"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "hash": "8de2935a1149d12828ad69c5e6ac5392"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.5\\lib\\path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart", "hash": "efb419a0003e1d1be50b9cd93b9547a0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image-3.4.1\\lib\\src\\cached_image_widget.dart", "hash": "4310ddfcafc039210f0221a343c43164"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "hash": "57699e53ee54d843153d1ef4dd086d64"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart", "hash": "bb30dae6bf385cbf0124346923f5c158"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "hash": "0b279dcbda1933bafe0bd7d322e2000f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "hash": "f0bf47fdaf6f5a5be43f49d2d73a3145"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "hash": "c2e8ba48563eaf38bd3b50761b60e973"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "hash": "31fac5b5882f19d242724b858b97d228"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory.dart", "hash": "4ce56dab766f683c213da41402d17049"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "hash": "787f46e13d1a634284a8403fe1fbed15"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart", "hash": "2d2665cc38035cce3d915b6893800623"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\lib\\src\\multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "hash": "7a018faf565163ffd45ba5e46e1d73ab"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "hash": "d77e212c79f98b6ea9c29750e6e21643"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\form_data.dart", "hash": "bfd57391197129cbe3c47c75b2c21672"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "hash": "d8b218966d397dae64303cdd7d11b33b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "hash": "2796f86b16cde28eab42232889d082ed"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\rendering.dart", "hash": "31b6e401282dccfbeae67ee82469cbf6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\result\\download_progress.dart", "hash": "1e0f86acf6978afd1769e17506893606"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\clock.dart", "hash": "d52c28f679ecf880a21c3ba08df59267"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\flutter_cache_manager.dart", "hash": "4d339d186836b857e23b70679d7544c4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\image_transformers.dart", "hash": "10404d098f485bca549850751b3e93b9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "hash": "e761fb30d10c79da8dbae27ffd274efc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart", "hash": "243ee4a9d79acc2c478caf01088decfb"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "hash": "b32917544d6176139cfa3618ca522b14"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\constants\\app_constants.dart", "hash": "e325b92e2f41f319079583cb75345092"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\delegate\\stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "hash": "c85987e2ad461c8b574f838c3e7a366d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "hash": "59ba7bdfc57111a2c59ae46a61e0daa1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file_stat.dart", "hash": "1b430815bdc7bab3a240f27e745f8977"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "hash": "fa98c8c6d036ea07d9ee49ea7d30f25c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "hash": "834ed7d1da7d9cd0a0af98bb92ee6231"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\cancel_token.dart", "hash": "c9f037c19c2e4adfe331d9e56f2e72c6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "hash": "3da0a4c6b5e4be03fa0e8e2871e98d01"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "hash": "9fda069b501810ac8093f3c0ad80e3f4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\dio_exception.dart", "hash": "2747964c64fe300f15d15123727cbcf6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "hash": "d7eafa3cf0f7e30ce2e5c01279154b40"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "hash": "1dd5b37860108a769459a04adbc418ed"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "hash": "494881558ae50a1d5273ceb66b43ed85"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "hash": "d714b72a4f8f2b38a6144a6d8bfbfd74"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\placeholders.dart", "hash": "4ccaab1f2ffd61fd5998a2fe8a9be886"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "hash": "575b2fdb620f6faf33b5a89b54898754"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart", "hash": "d8563d6c5693445540a807bb73dff06f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "hash": "8a8f9aeb0001ca5b48ca4ea93a0f4831"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "hash": "07230264b6ad4306fed087103930fd35"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "hash": "0ce04284aaa974663c60ab9f03332ff4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "hash": "d773ee48068ac90b654336d1ec93541e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\transformers\\util\\consolidate_bytes.dart", "hash": "b4446a7a4d053aaa35a7bc6968b4794a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\lib\\path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "hash": "7eee695ba96e5afa80abfaf59973617a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_object.dart", "hash": "470452529b3925fdb9a5865578558e83"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "hash": "93ea53606b5bcb9f94323580b8a73a66"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "hash": "e91a73519c927b9535a83b357801e052"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "hash": "540938d0d6c6e1ac0f1ae99b254c8423"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\web_socket_channel.dart", "hash": "77227643b5a655ab1fd24917c5a4675c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart", "hash": "ebbb5c061b2adb32764c3c2d1ec6ef3b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "hash": "29666cfc794fd78ec2462f291975ca43"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart", "hash": "7d5370ce28d67f1901da31171aebf7e1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "hash": "8cc8a350c6fd356e165251a8754c95d5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\sqflite.dart", "hash": "f7be2d6ca06ea6e4deeee0e441b71d6a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\subscription_stream.dart", "hash": "45f0e675fa74d765bee71cf2c553bd58"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "hash": "c909abaa7c0b6b52aa22512668ededb0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "hash": "fb42af8514d87dbb35b1d9ad465896f2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\memory.dart", "hash": "647e49fd7e2b6707e82858420b630c46"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "hash": "716cd061dfa876531364acffce466bc5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "hash": "1be64f7d80a3db5d33ac240131681562"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\cache_managers.dart", "hash": "d4a2cc62bec6dff9fcdc94bc868ea014"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "hash": "92c59c394e56bfb4a3d764c714a45c1c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "hash": "6f4d72d38afe16a9eeb2d21bc14e2460"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\errors.dart", "hash": "2a7dd605fd24026f238835990b2af51c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "hash": "bc7649ff9f28e8e22232071ca174f891"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\.dart_tool\\flutter_build\\fbfbcae587d40e1d9df02cbf58f8ad61\\app.dill", "hash": "b06681a76b324f0c0aa7b1dd1b23f027"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\compute\\compute_io.dart", "hash": "e990b24e6368a3aa33f21b4695cfcfab"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "hash": "ac7068865efbe78773199ef71b213440"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "hash": "68a71f0dca77a83028d4c2a3dff924da"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart", "hash": "29dc810717daabf9c43e0c29a9535662"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "hash": "bb3c82c5a4ed9444e9e365800f49c19b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_zip.dart", "hash": "1dac993c7444b99a17f2dcf45acaca97"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\progress_indicators.dart", "hash": "c2c2286fb7180be54cc4fd8b03ba9dea"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file.dart", "hash": "d9343422c8a6829bd05698de67232591"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "hash": "451a797396c48722517ac4fca1ec1c62"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\value_utils.dart", "hash": "c112ad2acb33c46fcd09f4f2b7d2675e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "hash": "bdd3a31817dfc052c506f3a7e2556fcb"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart", "hash": "2d6edcb951c82d23cc3590b236a2e0f5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\lib\\characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\operations.dart", "hash": "5dbef5156368d0f25b59750608e025a6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "hash": "7a8436b3682b625fdf4b1dbccda9e895"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "hash": "d70b537edfcda82387a991a99e6700d1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "hash": "7e1916e19ed191fb74d54f4e67662098"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\io.dart", "hash": "1ad7532928963229cf586db9dcaef3ca"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\result\\file_response.dart", "hash": "8a4e81e8fccc01dc69bbc847b75a31b5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "hash": "28d7ea566a4cd9f1e4455efd86f424b0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "hash": "ffe5ffa81a95dfa29637da7f638fffbe"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "hash": "4ed46ce370a37c49a136430aad05d4f4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart", "hash": "193d1801b76c52075287857a01d3fe83"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart", "hash": "61e660e12e1c2bd4b570416482c8f18f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "hash": "1245b0b05f60c57164018c5c918aa7d3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\stream_channel_completer.dart", "hash": "a64b855dc42d91c681b48548816fec8b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart", "hash": "04ebb81cbcdd308bf43cb79bf12631e2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\sqflite_import.dart", "hash": "e0a5a25c69f7362ae3d6e493dfc611ee"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "hash": "f31cd1126983d313d533c2f530bd1c33"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "hash": "049721b49522de261cc343650a755864"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "hash": "e4523a14b383a509595ff9501c8cedf3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "hash": "********************************"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\semantics.dart", "hash": "a9d4e4b3f6357c540f77101737a25e4e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "hash": "4faf3d2a26408c96acccabef79b5080c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "hash": "f1be26fd7d1129f7c28d979d72027718"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "hash": "869bbc5dd32224e5adaeea14f49a95c3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "hash": "16cc4a3c97bbfb29764163f9783954cf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "hash": "eaa572d06b6edfa39d1b51f873c658be"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\plugin_platform_interface-2.1.8\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_menu_anchor.dart", "hash": "50154334bdb9b9e012f6e1a4c9f2cbea"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\widgets.dart", "hash": "85ba1a3bc73d7ffd54faea23f6c727fb"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\delegate\\stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "hash": "9d93cea34a3d8324122b5becaedf54fe"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "hash": "5588e04d2462fa3c03dc939826b0c8d1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "hash": "0fb5a81c723bbd506468f1ed5a811a48"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "hash": "90f2bfd8a277caf2dfdda31d1db65bf6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "hash": "c8fe24b1eb8cfbe5bcd7293c6991be37"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "hash": "0908983136fa6ff9c6aa302e79ee7bb6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\cupertino.dart", "hash": "d0d99e4aed47c375c97c9822cfdaaa28"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "hash": "c1329f33c1eee5551ec4b5b013167e71"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart", "hash": "c0aa6aacbfe02a42de016558a0f011b4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "hash": "97f8d480ec6ac1778506d29f498a1e6c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "hash": "9f9fef5d5732d2b083ce7c05aff2e265"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "hash": "38d5b82b40702a77199cd5c8be506036"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "hash": "8b2a5e0028524c90fb341f103369e087"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "hash": "dc8584d821b4ba6c384e7fae602c6808"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\json_cache_info_repository.dart", "hash": "f8113503c91cd843d744fa61b0b15ba6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "hash": "6557b0521d669043fe957bb915c97e38"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart", "hash": "b62b9e4874beca4adb96d6ebfd8e8dfb"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\memory_link.dart", "hash": "92be3b74ebf2b10ee5852ddbbc825971"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart", "hash": "9f7270100e64dae8750b9ae54cde56e4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\response.dart", "hash": "2a02594ad813d39d23460e2abfd2551d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\meta-1.16.0\\lib\\meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "hash": "8141617d0d061a10cb35d7c324354241"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\models\\message.dart", "hash": "bc8211d5d004bc0eb4794f16fdd949a6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "hash": "254681ce32061015aea72e79d4a7b8ef"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "hash": "ae3ea28df9363a4eb82872e4a3573f77"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "hash": "cb97906222d39edbd20f2db91ee3776e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "hash": "29eb69935c586ca665d0f6f7aa84a06d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\compat\\file_fetcher.dart", "hash": "808718676ca459f8941aa519a73bbff2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\mixin\\import_mixin.dart", "hash": "50ef33e165498030b82cc4c8d8408597"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "hash": "939bb15a6ae926447a3f2e4c0f3a5d39"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\cache_info_repositories.dart", "hash": "8cf88d8eac6c9b46a6fb3877f9fc35d2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "hash": "917ff734e7ac8749015973f521ffcae4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "hash": "bf3b061fba244f7be1a407f9a282d92e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "hash": "********************************"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "hash": "9de52faf4ac5b8490e2763cc6969b95b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "hash": "525ce10fb97ccc6f3407f8e962043ca5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "hash": "4caf5b7962b2f058f4c5e33d69bf7ce7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart", "hash": "d460667f13c0c9c2ddb60902d2d92663"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart", "hash": "b614d8172098403c683c68aafa3e92e8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "hash": "feae31fa46eea795709f39bcb7b8d05d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\pubspec.yaml", "hash": "273223e916a7ec4cfee444b5823f6711"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\leak_tracker-10.0.9\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\lib\\src\\characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\meta-1.16.0\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\widgets\\user_avatar.dart", "hash": "be82a4ebafb4758e9cc3b9f3be1bf554"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "hash": "24dd1f01d0ce298347e47fd60702007c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image-3.4.1\\LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "hash": "34d5859bd13d7c941916846f41ef3b31"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "hash": "bf2ed9d304c7f4d1e982b118bbe93bf2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "hash": "6a7f49ff645804c67a62656a0046ca5d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "hash": "d82db0f248ca051490d30d8b76686730"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "hash": "9c7196e67b951143d548d72aaa0e75ec"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "hash": "d29e9119669959c9cc3eba800cc79d90"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "hash": "03eb0db4ace470b8834f6330a9473b70"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "hash": "35d5b2c786045d1aa7428d0b48b2b3b8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "hash": "8d5d3ccddf53eafd7a3094278afe8b93"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\node.dart", "hash": "2f9772d14db922d3a41fb27f6b6382fd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "hash": "2c907a9b2d913f98aea742b5ead8d39c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image_web-1.3.1\\LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\mixin\\constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\utils.dart", "hash": "9d122acee9d1f43dcdb2ea88fd1fc95f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\database_mixin.dart", "hash": "5e9d885bc066ae16bcca5bf065c9d51f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "hash": "5963af31f4a3213cf4ce04d2b1ca7eb2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "hash": "462f8fa010cd5b4b90e61714d27a9954"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "hash": "6da099b31166c133a52bfa3ab7a1b826"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "hash": "95454f3edcbc7a37488e2762d253a780"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "hash": "ba859da84d2120e70ef3404c552ab425"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "hash": "f0fdc3dcbed6a67203aefc93ab718392"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "hash": "f25ad5ad3704aecbea8e55cdf889b1b0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "hash": "9f2cdd0bd6f4ce722e2aaccb9516508c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "hash": "310649eda1830de4f669332f93865282"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "hash": "a5df6e3153b654738dfd5a67c799e6d5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image_platform_interface-4.1.1\\LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "hash": "391b9da05d70a8813ca2897c2600d0c3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\adapter.dart", "hash": "80079ed73f37411d422a28fb563580bd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "hash": "bcbe75353032d77c9b99a24bb590393e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "hash": "a86a7c938657004c2ef7f8562389f468"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "hash": "90d9ef8c98ba928aace1e5b7099c0844"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\matcher-0.12.17\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "hash": "a4a8a699953a05873a0565a3f20e60e4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "hash": "201255868d9b2a9a29f0dd4b846bf410"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "hash": "abe3a12abba3d1b3a88ddb1ff43ea51e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\web\\mime_converter.dart", "hash": "601a4561a6a4b9a0f99cdc39dbb67c0a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\sql_builder.dart", "hash": "1c4127d99af22e5232df8132ae79beeb"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\physics.dart", "hash": "ffd7e9991334466f08df7afe0c721048"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\file_system\\file_system_web.dart", "hash": "71b9fd89c14d2a8a39275d81a2500c5d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\sqflite_debug.dart", "hash": "a2cdec29e909752629150b24b9b18407"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "hash": "3130351c8333143b70539b0a0bef2c9d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\utils.dart", "hash": "7a6fe2bde5e3bf653cd473308d8402c5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\material.dart", "hash": "62df7d5f15f6938d0e55d1d4548e9962"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart", "hash": "73d837564acafc178f5bf73dc50994e0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "hash": "b61a4dbd857b74aa723c96fc1b53fa90"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\sqflite_android.dart", "hash": "23149dd1dabb201f41ccacb25e322741"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\_web_image_io.dart", "hash": "7f7fc8274f08decca0738d6873b9b8fa"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "hash": "62f20a61fcca0d9000827ee5e54a30f2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "hash": "734c0e931a329ed28d4016070d47edcf"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\src\\sink_completer.dart", "hash": "84460105d53071b467ff0064e441b9c8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "hash": "59a23ab4821a3fa4d98e0cc5c647667f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "hash": "02b2fe6a8dc8ea38fa70193518928cbc"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\widgets\\media_picker.dart", "hash": "68a991298e46748a4d08625675f3a87c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\meta-1.16.0\\lib\\meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\result.dart", "hash": "c6e362e3e6b16241c22db67cbbd6b85b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\web\\web_helper.dart", "hash": "60db0a181494c7db06a18464e2d6e796"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "hash": "68b4adec8e855b7c35d53c486652f424"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "hash": "16bbe2b548ec9e4ece91aa557d09db8f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\mixin\\platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\friends\\friend_requests_screen.dart", "hash": "95cedf0156334c2252de28bc5cf410fa"}, {"path": "D:\\FlutterSdk\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "hash": "fc17a3958e284d6b8530b650943fdacc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\utils\\utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "hash": "1f418dd0c28e28b509d8179a39898a5a"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\transformers\\fused_transformer.dart", "hash": "4cbacf46dc43afb0d059b0016010f45b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart", "hash": "3395f23737e3d211de3f452d3724d939"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "hash": "d36e7d651314b6c9bb80b1616ae188b2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "hash": "b2eb657328bd482e6b083d76dfbca76b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "hash": "9f5e1e102c551e6d1bdd48d2f865db6f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "hash": "85aa53b038be894edc8ed4b952643c56"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\json_document_transformer.dart", "hash": "eade7bbc9bacbd78204c7ffdde55ddbd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "hash": "3b88bbef523e78633182df3f70dbe5e4"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "hash": "1537324c8f79d4bc3eca8bd06cd2a87b"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\cache_manager.dart", "hash": "b188e0026dde1c7ef925b5efb80450ef"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\sqflite_darwin.dart", "hash": "644e5e32abaad61eb192128f412424ed"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart", "hash": "e0fceafe591ad5fd70a41190dd121f08"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "hash": "29426d64d0c201a4d7e7492434b13463"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "hash": "c6361b93d4a266527cc473cc2570f714"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\image\\fade_widget.dart", "hash": "e1d33f6f03e359759c131d64cf62c84f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "hash": "24314571db406eb9ca7f0823eedbe105"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "hash": "e924e0f63c6bf206211fb6a6f319af53"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "hash": "40110052956a9ba346edc09b14dc1c14"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "hash": "a263371650b0cd7ecdf0c5b3949b31a3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "hash": "aa39aa7acfb17e9127e921baa6a0f04e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\lib\\messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "hash": "b438b92d95aa74f212a0c40b725e10ba"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "hash": "e816d9ff65a2c6401329b0df85fa47c7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "hash": "4e4d2071604e3c34d057490624ed1c98"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "hash": "36b7c748941e821dea3c3c964032ac12"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "hash": "a05b35e402965727a6b38bfc20b6ea15"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "hash": "04958562cca050e70dfde058bc4c8b3c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "hash": "e92d23e94e235dd63649d0e26f8314b1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "hash": "7389265943ae4de96f69b6975797e3b3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "hash": "6f50583c3f17a5209d18ed90e5c521b9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\animation.dart", "hash": "e76c07fd6945a4eadb2aeebf87b643bd"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\home\\home_screen.dart", "hash": "ca5ee849d617f809a68680aed223d189"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "hash": "238464b246c0e0f60bc0fa9240909833"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "hash": "220eb17aa14783375f802e8d5cf5c49b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "hash": "7a81afed71f41a7b99c7a3d0d1521ae0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "hash": "7c956712fdb0e888676c78cc537b725f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "hash": "a0847dea6404a5a2413fb2690a3a614e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "hash": "46a5ea4411d0fef172edb219362737a3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "hash": "00f26750b380e279fd67c68c79734999"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "hash": "4d7ff70b73cfe04b14f559266b5a991e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart", "hash": "8760dd2e19b843a03e51134f84d82ca8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "hash": "16ff0bd0c4c1f269ee8a2ed5a1707c37"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart", "hash": "60f88878959c8f38a1f8cf481bc3d76a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "hash": "ecb57271d69343712d88a50442f2e8ed"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "hash": "86cac5e304f32a8cd937f64fee31ec7b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\src\\exception.dart", "hash": "a8875f2b3b371e151aab119edb22855a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "hash": "d268d6fb1caed2556859fc88f38f20d7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "hash": "ce79f0f4b543a5fc0d4137792329e870"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "hash": "89e311cb52134419a6ddf1fd93235ba5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\intl-0.19.0\\LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "hash": "7a804325c8972a8d7a59f08a66fb672c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "hash": "e101d4f99c70b9f1ab98f5969e2c2111"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\sqflite_database_factory.dart", "hash": "b2b96fda3b5d147408ecb71c2bbe73a7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart", "hash": "1772fb6df514c8211cbc291d7f4e529a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "hash": "2ced57b0fa711aca80714d917cb75cb7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "hash": "bc264448c0fc92fbe61a1cfa17b42d0b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\flutter_version.dart", "hash": "85c3698c9fbb35da307a5ed29c9f0108"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "hash": "3a20b4d301e83ddf2a700e43c3d13f19"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart", "hash": "df0a9878a16d3cd73cff00f496307544"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "hash": "7583d5890ed910b2c9a7ed24c1d1dce6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "hash": "fdf91b52ca7f1ba6753eb280113489f9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_sink_transformer\\handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\test_api-0.7.4\\LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "hash": "8e02b91714073f6161e7be7f9d8160c2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\widgets\\file_message.dart", "hash": "fcf49c3e3fd5f2992a56707ce9d8d16e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\boolean_selector-2.1.2\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\style\\posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\chat\\chat_screen.dart", "hash": "ea606f4a19a5520f52930adb3a92d93e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\platform\\platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\database.dart", "hash": "66f280c66f95d03902082cdd2b4255e1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "hash": "63bfda24ff899d90b8cf0de45c1c5c55"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart", "hash": "606636e865b06ca9bbefe3560a32fc7b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "hash": "d7b4e641207a9ef63e51ef095e392a48"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "hash": "5772596554ad33f70cd612ea70ce40a1"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\splash_screen.dart", "hash": "053ceb2ec1c2a791086d2e98be6ac5e4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "hash": "6ee7307afd79f7f32910b4db973200fe"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "hash": "51b88420df990277e38f7347ce6392ca"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "hash": "3e386920be87d5a0e9835148593dffe5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "hash": "ce7c719e306ad9ab02b19aafbd70178c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "hash": "aab7bf02fcfefca8bc2a8c24f574ceda"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "hash": "b4fee047182e4490f2e42cf2aa289637"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "hash": "34db9d7c9ebc27ae8cf7b0284f83365e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart", "hash": "69562fbf89b2450cf68b181723b06085"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "hash": "73e482e3ef72695bcdaaf4888ca76868"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\stream_channel_controller.dart", "hash": "7eb05cf7f12f7aa86e040f415a5a43be"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "hash": "3cd0fd2cdc8318929e613746e804999a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "hash": "e2389f2925d99315a85f2e8494b95d95"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "hash": "deb3149757b677ce04ef9df630a6f268"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "hash": "9dfe03ebe247c2eb3420b00c80b61a44"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "hash": "fbe26611bcfe5bde97092ae95ba95eec"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "hash": "18dc56cd10e06f3640239ccdbe740364"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "hash": "6c0b758a4a7b8966ccbd64932d1ceefc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "hash": "900672c4f3a8c395331517ee4a59ea2c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart", "hash": "fc53938b545fafdfb7a5ef073a1254b5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "hash": "164146a96eb3ced8381be57fbad8ca63"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\services\\api_service.dart", "hash": "6f378fd29a9acbaa5bb842244b434061"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\common.dart", "hash": "33f949ceca0aa8895b2fa0ae289f42d0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "hash": "59f3befbfab8a823bd8254bacd7cfca5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "hash": "b74031010357e2c6c8e502ed635f6d87"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "hash": "7498ab451e1ca95d81055ac4e3a25079"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "hash": "64a99cea669ce795e61ff3aed5eb0de8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\lib\\src\\default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "hash": "b365e48db065f0ba5206dc697fa3807e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\delegate\\event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart", "hash": "1eb9cc478ea7bda3219d6e73edcb2929"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "hash": "bd315d8c5aa8b4a998458cb3e8068b0c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "hash": "2fe41384c97b732ca4986150ae7a002e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "hash": "e0d86b7f5d94eb97a3a1f9393dd6b817"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\lib\\src\\utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\style\\url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "hash": "8939744fc50834903aba130d3b18765f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_darwin-2.4.2\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "hash": "f0c55410877496bd54ec3144eb434a27"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image-3.4.1\\lib\\cached_network_image.dart", "hash": "dd2d618db009ed3aa82488ca3b0e1261"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\painting.dart", "hash": "e6c5d07cbc53020acc5c08062c8b57af"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "hash": "58024a76590b0e38955acf1b045a1a00"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.5\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "hash": "8ba398f65bba2f40abf54b600e709975"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "hash": "cb6abede12366aefeffc99d8f9c4c885"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "hash": "7c9757d3cc07fc4355bb72187af0413e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "hash": "b071f66d17839dfc4b30db0836e7d5d6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "hash": "77c012e51ce5fb5b6758ed47ee29af56"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "hash": "dccc4c7ff5d008afb92d48caaec777c8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\delegating_stream_channel.dart", "hash": "d731e1b690975788d014e6df127b2a9a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "hash": "1cc862e37a6af483d4f60fdadd2f291d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "hash": "df6b7325835a6d1da457ca5056ab1860"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart", "hash": "0a756bb43feaebfaf3245f96d8275789"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart", "hash": "136b08c4413778ae615af5f45d39ed93"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart", "hash": "7bc0cec528063323a299b9ee81543099"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\base_cache_manager.dart", "hash": "53745062ff0e01e3d763823156d695da"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "hash": "add0c413747369b7460aa14539b29791"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\interceptors\\log.dart", "hash": "a7730cdfe094a3fdd076fcf5fe39ed65"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "hash": "f0f22cf747d09b92d955e41b12852d3c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart", "hash": "c2593e65f1a2367d83f0668470ab5f61"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "hash": "cde6a145081704bf14d1097f7ddb58de"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\dio.dart", "hash": "3467899798f7f8ca82797ccde4772534"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart", "hash": "d970423c6488cba52d3585e0221e57ba"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart", "hash": "e4d4fcce981dab2d3fb4a6a5532e2e78"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "hash": "3d6895a1ee4fa4ad4b5c4dee67bb38cd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\dio\\dio_for_native.dart", "hash": "6f053637ded96c67b342e6a80e7372f3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\exception.dart", "hash": "e625c15c91736f7c029d22eaf13a9599"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "hash": "8c6db6c750bdcb03ce0f7e6d89423546"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "hash": "8ad25d6c38ddc8ce7abb828b97f4571a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "hash": "19ca41c14be0e05637a511d945b2f810"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\web\\queue_item.dart", "hash": "937dad14a7958c57948525533b199296"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "hash": "8b1f55bdc7ddfffc5a4d677052b1b789"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart", "hash": "94d9e1be459979a63b16d671acb7b139"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "hash": "1e5182051ef0e48e07643f573e5f4bb0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "hash": "d65e92ce2b27908a96f7a6efbb9cdcb9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "hash": "23b4272b5eb55e3cf52556499d92ecad"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\octo_image.dart", "hash": "66d6d10e44ad1e696a8e632a5c4883d2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "hash": "da2fb5c7eaae1fd58b34e80c4dad2cca"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface\\file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\octo_set.dart", "hash": "0d750078c87ce8f99c60c3c76305c11a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "hash": "9f600f4245c898a4067344ec13a38169"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "hash": "f9aa2eb956d270d4df8b3a7cd5ac52d7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\close_guarantee_channel.dart", "hash": "1536ff203bc26bdb4841b82c51187a6d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "hash": "6c3746e3a4529b0367098600fb9d3d02"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "hash": "6e144abc5846b7e5eda10cec79c9051a"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\auth\\register_screen.dart", "hash": "97291e21f673d59abea95fa93e727b11"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "hash": "7cff3fd6b3f5da25b63942824d72a403"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_lints-5.0.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "hash": "8120214a606fbc8c98cfff2b27eca1cd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "hash": "861a19ff01e3f58d95d668b9fd4054f7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart", "hash": "dab909dedbbf46bb14d7a26091ac10b7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "hash": "61d6754850dbffcf4687388b630652ad"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\leak_tracker_testing-3.0.1\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "hash": "05a65ef87faed70eb436b68ecd4a93f6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\src\\copy\\web_socket_impl.dart", "hash": "d4588095b4b65ea683333ba810e78c37"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "hash": "2abc41676d17f2f12e4878a82007cb3e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "hash": "fd1e888f48f9b2411ee9f3d8a5146397"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "hash": "b7fb4ebe93c884f2ed505604fa1db488"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "hash": "1360d39c7acbf1ed4425b847c5bccc8a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "hash": "c7acb3b122d08afb8b40130e88a0dce7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "hash": "527f66bca3f4ace3771d5ffce977c225"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "hash": "2a5ea48301e3de49528660d81bbad42c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "hash": "c6f3d5ab867a5b665a50e0af11d048d7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\models\\user.dart", "hash": "7bfdbac0f0a9885d8705513d628e1444"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\dio.dart", "hash": "3059dceae50124dbd966f136c80016fe"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "hash": "c64b32c068f0d8679ed2b8d699659780"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\delegate\\stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "d2232e4bd0457a4a3eb6bd897846eb44"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "hash": "f45299fdabda0bd3b2ed77068c7d1de6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "hash": "8764a2799f8899639fbf335bf794c627"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "hash": "430a92b7fc96d89d9750f26f826484bc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "hash": "47474102c009e7099f3a9bf1d7ea8e06"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\file_system\\file_system_io.dart", "hash": "7caf4c65583e594208feee7e60872fea"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image_platform_interface-4.1.1\\lib\\cached_network_image_platform_interface.dart", "hash": "d9ccb5a0c8dcf64361a257c101d0e719"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\logger\\sqflite_logger.dart", "hash": "ab42e582c15854ab187bc7a07fb8baa5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart", "hash": "394fb82afe9f148cfe87611f68715c91"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "hash": "a4c1cab2135ba3ea8d1385e894ed0d09"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "hash": "5fc29a61e77f85ac27eab0b592b2029e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\multipart_file\\io_multipart_file.dart", "hash": "8094c68b4a15e6f73e09501aa6ff4a47"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "hash": "bacca4c03cd5121bb38b2cfcbde14197"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "hash": "d2e982be997d037d953557167bff1e53"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "hash": "ec3a5f7a8288542858278a8702578709"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart", "hash": "9af74e7990203514b88e8c03e2acede6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "hash": "51069c14005cc63df73f7c8db5520f31"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\services.dart", "hash": "ea36b74bc426acaba86c810396c43f8f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "hash": "1723ae3582276265ebad2922945dbeb2"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\.dart_tool\\flutter_build\\fbfbcae587d40e1d9df02cbf58f8ad61\\native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\response\\response_stream_handler.dart", "hash": "87061e866d20d4a66d6990c36638681f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "hash": "60129cbcd039cf3abac1ffaca12f3e55"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "hash": "a9e575d272fec21ee0baab114ecd62cb"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "hash": "2b2385e013688dc5ccafac580f8f6999"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\src\\typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "hash": "cb9617d35408474cec5c44f6d57c0faa"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "hash": "eafa783f39fb35b9bb8581b697c3ba52"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\disconnector.dart", "hash": "7732b755777af7281039ae8c5cb3eb78"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\redirect_record.dart", "hash": "91794c215a8aa39b862cfa4c96b9a398"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "hash": "ad1cdc9c1d06d8af94266565ad966f3e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "hash": "97fa80600d57e253a846a881c4aaae73"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "hash": "fd9b487c6b333f8de2f5b2fbe769536f"}, {"path": "D:\\FlutterSdk\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\mixin\\factory.dart", "hash": "63fa9307c55c93f4fde0e682e7da6503"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "hash": "28c62a3d01773f3a10cdd12523fe7a96"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "hash": "ff0c28954cbb930913ed52a41f11189a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "hash": "a481b9285f5d63f04e3b3e3fc2a6b44c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "hash": "994fb9ad204eeea38bdc0040b37283f2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "hash": "f06bc0318a4a0ecb95b7384aee5b21ca"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "hash": "4297b644d258ee7771676fea206a5118"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "hash": "f3a43f7f1e3cdd35326341d9fe51fdee"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "hash": "42e61179c9aef816ce39065a97feefac"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "hash": "34fbd21bc1ac0d1f0412b22e47041ece"}, {"path": "D:\\FlutterSdk\\flutter\\bin\\cache\\engine.stamp", "hash": "4436fe45d07530c8961e90a578571cfd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\sqlite_api.dart", "hash": "5494fe877262550facf407b379edae59"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "hash": "1bcd67a0035393d4f31f28ac522ae83f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\progress_stream\\io_progress_stream.dart", "hash": "6ea89c3bc6b0860bd7c16998d3950c3d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "hash": "363dc40bd108240cb513878e107a260d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "hash": "02bf5117b8c6def8ea98ff663a374da9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "hash": "f6879dbb0a0b22e90c61f21ebf5c440e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\stream_channel.dart", "hash": "b399357b285dbe1fc8e858ef3b8d20e1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart", "hash": "fa7146d472a712331eef3a404e2fabda"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart", "hash": "670717573525d5e0518a4063c6fd9231"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "hash": "a2701656bb3160ea810ab576c50cbd65"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio_web_adapter-2.1.1\\LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "hash": "e0e8407f511d45af90555246b991c170"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\lib\\src\\utils.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vm_service-15.0.0\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\open_options.dart", "hash": "296e60aee7732b001a79f3216058a381"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "hash": "cf39132e96645ad4603d01e2a71f2ba6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\scribe.dart", "hash": "fc0d5385b1ef6f854be4e193437b39b6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "hash": "ad4853392f910e8536f8a1bf485e2d14"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "hash": "833549c8f32a60f8bc7d2f221760844f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "hash": "823b3b44cc184688e1fd926c923ada17"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_group.dart", "hash": "d16df8af6c029bc5e12bedcb2d9ed464"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\transformers\\background_transformer.dart", "hash": "c3ab437aa0b03081adbfcdff7755b358"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\file_system\\file_system.dart", "hash": "a0432b1db3ddabe8c3edb6f542c9ef48"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "hash": "702ebe43a77fbc5d1e1ea457e0bce223"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\native_assets.dart", "hash": "a78f4580594141cadc5519d96f1cee73"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file_system.dart", "hash": "9007580fb76ae011692307f00e0a28f1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "hash": "39d8ca399102782c09b864fa92d51f3c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "hash": "bc19869e91597ad295ed0aa82807d433"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "hash": "93219dc70f767a24408583015695d38d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart", "hash": "1d183dd2fa481536757ae61cc77ed9da"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart", "hash": "764d664b6d2ebc59b9e54f316e64cc8c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "hash": "4988a75e1b6c9b1b2c2df65cd6318bf4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "hash": "7eb989577e5ba101feca9c465b25264b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\home\\chat_list_screen.dart", "hash": "b39e691c599acac5bcaedc2cdf3e09bb"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "hash": "d65982353903b8d6b3d8697fa43abc7b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "hash": "a4bd9e0add3bd13842fedfbd33ba0192"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "hash": "bcad667eb65d62ec08bcb8781cd89942"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "hash": "e657634a020bb8214e2507b5d0105d6b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "hash": "3199c921467a961819b9674fa5fcefe4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "hash": "413144882e92d0a27858427f45f214b9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "hash": "fb5592ffbdb3669d56c8d1cb23ed3033"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "hash": "99b984d75432877c137383cb96a09240"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_queue.dart", "hash": "cf0f2c674cec774d8fc0990ee818316f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\image\\image.dart", "hash": "e03a984efe74a058d3393aba7c55fe1f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "hash": "0071fe298793e366f6f5b16077adbf4c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "hash": "b649f669a0c7234ba97f959808a59864"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart", "hash": "dafc76ada4dc3b9bc9a365a7da786797"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "hash": "12d3b22764f1a64ff214cabee2858a00"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "hash": "de392e4c7043eeb731474c52267542d5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "hash": "36b808e976f035411a6b13a09cca7717"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "hash": "ed437fef28462d73a74c339fc59a6cda"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "hash": "03e34b6476f2a5b43080c9fefcefe9ea"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "hash": "976911a24895e34b12dc8b3227e70de9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "hash": "bbb8de6dfd910b8abb564969c260aca0"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "hash": "249b2817c1c912b9942e8acc7987adb0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "hash": "a334501a734a440f60aa69ce87071ef1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fake_async-1.3.3\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "hash": "4538b5225c156e895c5d603660cb1358"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "hash": "1fbfdb508cbaf318a89890747d632a67"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "hash": "f8ded7a3f53ded600055288202a36535"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_android-2.4.1\\lib\\sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image-3.4.1\\lib\\src\\image_provider\\cached_network_image_provider.dart", "hash": "8a55a3a014cc2ba2dea85787efc98ee4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\lib\\src\\basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "hash": "828a01a48b4a0451b0d1f9c531cc292c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "hash": "d48d52bc573d346cad979541a2f68329"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "hash": "b98145bd156a782be220cb3f652ba0a4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "hash": "9d53bc53e1a3cc6a033ea11c76fba050"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "hash": "da23e9a23f3c0bd8f03adb30165f3f2d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "hash": "314c8a74e984655102d473063387e50e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "hash": "77c6bf950eb7c87baf89b95368f6acc6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "hash": "09bb22b1c2675b310a94dd1d4e8d6634"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "hash": "0469ca72d371bc48cf7f0901c0cd1917"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\image_cache_manager.dart", "hash": "69c7e246c8fb227cdabc8a3d9a8316dc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "hash": "ffd5fbadea48a2028f714bb6a918cb45"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "hash": "aeac5f6dbff3475f74a377686e285d6b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "hash": "0289bdf9300d66bc785375eafd92b219"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "hash": "fb7f2cf0e21cde2881330bd17eedfd4f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "hash": "c87e92035314a4d3e52faf886355d0a9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_sink_transformer\\stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "hash": "d49f04d746fa2e01e165d7cdef3abf2d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart", "hash": "eb8af6a9d85f134dd7d7c1ae2dd07e29"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "hash": "ef78266d8f88424f55e3135455ee90cf"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "hash": "97fc1400dd55cb4fceecb33260f0f978"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\lib\\src\\allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart", "hash": "eabc0ff413b2fef64e2d0ce3554556be"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\lib\\src\\clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "hash": "a4474d2d39dd9373580879eb4bd18ab7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "hash": "c58ff4d729abd2cae831be9ce43e48c4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "hash": "b149267c83ef4fca8800213bc7331992"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart", "hash": "4db88ed53fb502c2c73cf2554abaf766"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "hash": "4a77eafe460177c2a7183ec127faabff"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "hash": "c692323b8d9e3ed3c4c134ba07ab94e6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "hash": "37114dfadd8f365fa911320571efe5a5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "hash": "262d4e04ee86cda8a7853dd7cca0584d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "hash": "ca40852851f196d4416da400314a610a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface\\file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "hash": "89a52c840b1bed53ea3c5479d6190762"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "hash": "72ede1aea65e26bd0e0b44da56f41473"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart", "hash": "6790958a6027b5951c84e721543745a1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "hash": "0364ab7e57329ec7705fdf09d706de02"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\provider.dart", "hash": "7c0851720900806fa2a397a81c81875e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "hash": "808beacf0746544002dd1206a6c7d387"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "hash": "6c685d31c42c2f60c5082e01574011e4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\LICENSE", "hash": "ca58010597a5732e6aa48c6517ab7daf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart", "hash": "845617099022609b14f6ff93a5e4ddb0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\cancelable_operation.dart", "hash": "57ef1f2eff2168c2e2ba1c3e4e60e05a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\chunked_coding\\charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\adapters\\io_adapter.dart", "hash": "b7579897a220a029c3ea36d6d48b4144"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "hash": "d99d22e8a62a3ce1fa8b04b21702e1f6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file_system_entity.dart", "hash": "c69896f9c186aab01f7d11624f5c7d4a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart", "hash": "7f694f69cb60ba144f59ff752805476b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "hash": "6a314e7c16349ce1a46736abaa73df76"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "hash": "d7c562d566e2e21539ea8288f21e7876"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "hash": "9ee10f71d143dd2afab234cf329b6404"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "hash": "637c88e489948abd53ca429504bba60d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "hash": "c3b78ed37835fb910da73d494152f0a3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "hash": "4213cdf8a94244f8d19a59844563dd53"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart", "hash": "9601fa00a20190d932ac3e402cbd243c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart", "hash": "632055fb4cb7c96929a29b8ee8a887af"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\sqflite.dart", "hash": "5c96fe82a9bf2dc00db9d93c2c0a41a9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "hash": "f11954058bc60e8c9403c769a89da56f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "hash": "e11d89b7d2933ba28814915b300af866"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "hash": "ff300e7139d4b384c9ba4c12a100f68b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "hash": "4205ba2309c32163616dd63f6264de11"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "hash": "8d7eed34bbeebcd7bddc0284a516f91c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\src\\channel.dart", "hash": "0a0d117cdab6f2b135f35a03c98e9c13"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\web\\file_service.dart", "hash": "857464ce6f576c4020591d501ebcaaa7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "hash": "59471e40595be9401faf6958667e9baf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "hash": "66d26b00d63b959556197f0f060e119e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "hash": "5c32703ac32c4835d961b3c55f8f8498"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "hash": "b8fb4215060bb74d8025202cabeab63a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart", "hash": "d3c9b4f0e500f74ef525ca325c595e66"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "hash": "7b9a23372e88dcc5453cc21b5ea3f89e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\result\\file_info.dart", "hash": "2f3e8198efb4b9ec92c0126b25986acc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\parameter.dart", "hash": "08b1358e505b0414dc60489b750ba2b6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\config\\config.dart", "hash": "6e1f276f9f7416f792db31fd51b3e3ea"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface\\directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "hash": "2a36080f4719a0b4485bff0582d9894b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "hash": "e22a38dff2e5b45230581797ecf557e1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface\\link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "hash": "43be915d6729f5b59a5dc76fd923b31b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\dio_mixin.dart", "hash": "e103c51878b3741ffe4d81896876f3ef"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\result\\error.dart", "hash": "a10eafbc71350955a16e4e787402780b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart", "hash": "3f407711541a40aa7584973e8f8dc03b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "hash": "59ad3f592944fc890a2333c208b755a8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "hash": "0bc32d9519ad188655722c1b5df8e896"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\default_cache_manager.dart", "hash": "8ad6f50f623fbd97c2aa23d86d3c22ad"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "hash": "********************************"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "hash": "dc64ebb08fcf29bdc05884be98af7daf"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "hash": "8631e44e103ca1be44ae10252f3dbacf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "hash": "2535b76e646e95222aa41ab42951de84"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "hash": "4cf81f473e6af6b63729d72f41fe712e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "hash": "6ebce6d2b9c6488571c530cdd3c0a723"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "hash": "5b1c50bdd6d4c64f4c6bf4cba82d48e4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\collection_utils.dart", "hash": "add5f0afe8e318e91950e5725be6f333"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "hash": "bde473d632d84c502c0fc66dadf378bd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "hash": "e037b8db819b66de43cfc6cfaaee04ca"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart", "hash": "06e19293b41e3c13506f5eacecfa4afc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\interface\\error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_sink_transformer\\typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "hash": "230a3518091834c1ebaba0eda6ad491e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "hash": "413dfcfb58932aef16c971e1148c1d3f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "hash": "8d1fba4e53f7807f89979186ed5b8549"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\lib\\src\\relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "hash": "f877b5be1de63e54015d1292624915da"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\style\\windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "hash": "51733e4ec61656353b10519c6af9ce23"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\lib\\src\\generated\\top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "hash": "e39c804b77ec1bf1f6e4d77e362192c1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\lib\\src\\generated\\unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "hash": "fb5f7eb90b6c4b891e8a1cda244c0a30"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\config\\_config_io.dart", "hash": "7d313ac68ec3f410b31e39f450fdaf0f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\multi_channel.dart", "hash": "51eb44211d0dcb7fd159fd3232f15135"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "hash": "9448b37dbe6df9b3e7d651a67b46578a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "hash": "235a9cbd9dfd2869ed39062656c7e07b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "hash": "77c0e52dd42c70e9f234e8493da90d99"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "hash": "e07d9fca82a9a505388343f64824e06b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "hash": "38a87ff489a47bc024400dc863be326d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "hash": "615c74b96a89de42a5207de4daf66eae"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\stream_sink_transformer\\reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "hash": "9628979f9e240d20f992073c54489890"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "hash": "118be27ff90e0836323d9c71078cb122"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image-3.4.1\\lib\\src\\image_provider\\multi_image_stream_completer.dart", "hash": "4870aa3bcaa04ecc633da01dbd2c9560"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\expansible.dart", "hash": "21529000763daf1b8f70fd6bf123cf5a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_preview.dart", "hash": "f367fce949a6742ccbb25667c84c9b6d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "hash": "0afbdd6f1125195ec28ff55922e51d50"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "hash": "de9074b4c33d599d09ff4a2b953bc3c8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\delegate\\future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "hash": "bae66752808be6b5aaf6c0c266e9d40e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "hash": "c337b850a7c3d9b2239394993aeeab6d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\single_subscription_transformer.dart", "hash": "789cc727406d0343a1dddb5018570adf"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "hash": "064436cb7f327e308f036ef9b5c40b04"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "hash": "6bc38d604adab605e07dadaa4f8c3319"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "hash": "ffeb03ba26ab6fd8a8c92231f4a64bec"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "hash": "68c2698d9480a0bf5a6211ab2145716c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "hash": "99fb2e65ec78997546349e740526b24c"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "hash": "8af2b6b4632d42b4e1701ecda1a3448a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "hash": "68d77490fd54976fbcdc4cd48e97fd7d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "hash": "50666ddb8af05ad2fbc9f5980fad7785"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\lib\\src\\generated\\ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\xdg_directories-1.1.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "hash": "e3737de39b4843c820044597cd5f3b5f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "hash": "********************************"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "hash": "8a5f786f5d88821fda1f83bc51501a31"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\octo_image-2.1.0\\lib\\src\\image\\image_handler.dart", "hash": "a9ad1aa35c1b9117f15a379ef03480dd"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\providers\\chat_provider.dart", "hash": "a07844a07bb4273f15fa887481da4bce"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\lib\\synchronized.dart", "hash": "044e7c8ac3258945fe17e90e1a4fff51"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\lib\\clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "hash": "697b345e3b00008c024c87f80dc0b87f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\result\\result.dart", "hash": "08e7cd384cfc0214e088945638139ce9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart", "hash": "399b64e5839e1a71d027672c0e0facc6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "hash": "840f928248b5e234ff1fbf4ea6b27b6d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "hash": "d8a7b96e6465831f5cf6de56ccddf6b4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "hash": "769e3974284ea91f60d3322d7322a979"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "hash": "693635b5258fe5f1cda720cf224f158c"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "hash": "2efbb41d7877d10aac9d091f58ccd7b9"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "hash": "5af5a2fb59f9c015abbda254a35ea7a6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "hash": "1652ed4c7d6405a0ff49b79d241a37f3"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "hash": "52d3612bdffadcf247e4570767678989"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "hash": "d765dc0eb274578ea0585d7066a563d5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "hash": "905e412b364d027891e90040b270ebd1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart", "hash": "de961c25d71b7a769062627541bfbcbd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "hash": "c6dd0c20e5521905acdd0e209727ec65"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "hash": "384464b3480155e7652d42b36d8447a8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "hash": "c32b3be564aac574be8ab4fde5dc7a2f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "hash": "f20b8958b0c35e9bbea75a43c9cf0e59"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "hash": "977b101929ac6f80c9dab61b4f232bda"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart", "hash": "ae57ac152dc8bd45a57735cab6c0a634"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\lib\\src\\stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\transformers\\sync_transformer.dart", "hash": "787074c3d370e068052721d16acefd9e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\memory_random_access_file.dart", "hash": "576c23d693f7712935103974ed9312ee"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "hash": "b06681a76b324f0c0aa7b1dd1b23f027"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\inherited_provider.dart", "hash": "dd618a65e1f3400d8224fedb42a1881b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "hash": "a1df4901b8e8927621e70484bed93522"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "hash": "f3ce35c15285bb22d0a813b27365491a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart", "hash": "d943510867769f5dbb0919992d94abf7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "hash": "ba72bd9a2dac60d690accde65a1ba704"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "hash": "2285a845b6ab95def71dcf8f121b806b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "hash": "2cd153115fb83cfe237aaec0909df2dc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web-1.1.1\\LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\main.dart", "hash": "dff3b60e5589d1175401d9718b4d18a7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\chunked_coding\\decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "hash": "d13b021628351bce6b686e67998d5eb3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart", "hash": "2fac118c3201a4bac960d7430ddda0c6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "hash": "40a8505ec0b7cd7676ab5efb486432bd"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\auth\\login_screen.dart", "hash": "df411ddb8ad633a8391f98175ae69caf"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "hash": "e0f8ae49e5195d1949887769d1e6117d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "hash": "fbc4e27193ffde95719ac936bc35071b"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\screens\\home\\friends_screen.dart", "hash": "f0f5513ab5466fb19f53c35941aa7064"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "hash": "8cf53323efec8458fddf0baf6ab7624e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "hash": "fbf98f47029acb307401fb5299babd1b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "hash": "********************************"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "hash": "15308b82e6b7d5df3058f8e769084a67"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "hash": "8088a1e9032c8c67b16c9d46d58329fa"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "hash": "a0836fe16e69a1daeee421a5dd73f761"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\.dart_tool\\flutter_build\\fbfbcae587d40e1d9df02cbf58f8ad61\\dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "hash": "63701253bb9a85c00b68c2d51358bad6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "hash": "851b8e20b77e3b6cc21ad90cdbc1a1f4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\cache_store.dart", "hash": "b72b9cd4de477e80296c7f58bc9f5f30"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "hash": "30e5fccf0da79e15eb5c9e94d97c6489"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "hash": "1d3152d32f76b361fabfc04ad1eb74b4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "hash": "ba0c8dd8a234a449050e76d22738480f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\headers.dart", "hash": "12ada90523ca5fc00e317c0a59889a1c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\services\\mock_api_service.dart", "hash": "a9e3ad8782a2807ddcddc326df374cb7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "hash": "9ed92432ec854ecbc9df6407d886463d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart", "hash": "f2045075d36258ce6ae2e6be59b46fed"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "hash": "d0a86046f0bc5262b7752d63d91a5005"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "hash": "67b6dc5d338ed04947c83bdfa5e47db5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\src\\copy\\web_socket.dart", "hash": "1a32955bfdf293adc4632af4bc611bcd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\providers\\auth_provider.dart", "hash": "cb64040ae9f534c39c3062014f5c5ba0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "hash": "e4743fa26fcac7a9adf779220fcd5e14"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "hash": "007e3dfc7780af8def8a39f3be6e0ebb"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "hash": "bb8226c417f9729ca72c35b8c6b9e43b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "hash": "907abd90f09ee08374f744c4cebc6791"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "hash": "039f1af2b4e1859cb3852994632bac52"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\cached_network_image-3.4.1\\lib\\src\\image_provider\\_image_loader.dart", "hash": "af9339e8836ca91cbc9c8fd6b2de7cc6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "hash": "cb6197499c79351555076b3a8ecbe352"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "hash": "6383ecc859333da15aaf986d9897a7d6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "hash": "fe53f754a8905eaf7ccb7d3007ec8ab7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\delegate\\stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "hash": "8f0501360ede174505eaf6fc29b19626"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "hash": "2ed82d0ee4672e99dcdec5652737899f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "hash": "7e9cc2885777664a4d8a28ceaaf4343a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\lib\\src\\utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "hash": "16b7d416940fc53eca969b344627767f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\src\\_connect_io.dart", "hash": "97d42b77a7fe92d562988b9fa3a3efbd"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "hash": "f63442b56372cdf284974de30fba136c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\nested-1.0.0\\LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "hash": "5c63d2e99643f92534b54d58dbac13b0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "hash": "825b4e65b06dcc1b8822108f596b56b0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\lib\\src\\iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\lib\\src\\generated\\glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\foundation.dart", "hash": "f2027810282f79cfd11771b68f8bf50d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "hash": "ab8431c89597be48ecb083aebdd9166a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "hash": "c3a5ae50b2ebde819d504e56afdfed77"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "hash": "da8c02e232d7b61d4c38ed6f84826f17"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\lib\\src\\reentrant_lock.dart", "hash": "7cff949e3b7ac960b63441117b2f6734"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart", "hash": "50f215628ea075eee0da174502e2853d"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\memory_directory.dart", "hash": "96ef4798e4cf4560148762dd71bd180a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "hash": "0b594cddd30fe9d0d34a42f5328a2b38"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart", "hash": "********************************"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart", "hash": "6ac08531373f9a0cf8243363385bdab2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart", "hash": "624431304ab3076b73b09e0b33e35e4f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\lib\\src\\chunked_coding\\encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "hash": "c33e24b9037c1e3435006428541f2685"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\helper_methods.dart", "hash": "0d0350902fa7b7c829baf0666f1a74dd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "hash": "dbdb73fcbd1f5bef4680a288af14888c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "hash": "a10e928bda19ab337bd21e3783f3bd2b"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\transformer.dart", "hash": "49dba21de16234aaed28f8fd898543a7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\batch.dart", "hash": "d88008fc349dd84def0654263c6d16be"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\synchronized-3.4.0\\lib\\src\\lock_extension.dart", "hash": "92197f660f809dbb94c7d3d67b9f24e0"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.2\\lib\\src\\compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "hash": "7a67893da4c81183e350cc0ecc25667b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "hash": "2b8123df092f0565cbb936af3168dc37"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart", "hash": "29139d4dafc54a4ce0f3bb186aec6841"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "hash": "203d46859b027ddaea624814ff36aab4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\lib\\src\\arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "hash": "ccf5dc03f13f3e26baef323b1ff68d0b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "hash": "4082637928b7b78efd6c2bf895818e6e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "hash": "4ab023737ac892222c19c6a32b176f9e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "hash": "f10d1c73e161d0807716dee4ef148c99"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "hash": "40a04e94b4e68da57cf200df6c661280"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart", "hash": "b06018282363c4cfcf6d9693d15b29a2"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\lib\\src\\characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart", "hash": "49bd72b2f6d097b43a24ecd799f3d75d"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "hash": "6c49f36c204d9c39ed8344462e4342f9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "hash": "cbfb29e40dd6f4239a86817edb3e9033"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "hash": "9849718d7826b98fa09e037accf9ae21"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "hash": "04e3db38362ad6238e0bd8394acf5b5e"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\factory_mixin.dart", "hash": "47258dc751a1217744986101e934f62c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\lib\\src\\guarantee_channel.dart", "hash": "68928ed10e0a0df76e1e28584b7256c1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "hash": "c5df707bc55d571491bbfe69ab9e3329"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_darwin-2.4.2\\lib\\sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "hash": "a39ee9973bb91bf7fbbf9501861510be"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart", "hash": "febef97c3e452a11afff18c7b4eedad7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart", "hash": "1c6f4bde2f41b6d50498e3026f24dd1a"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart", "hash": "13a2211c81f2ef05128957664b806d54"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "hash": "cdab58168d88bf8cc4f48120b49ae69c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart", "hash": "deb256229ac9a60b1f5dee744af2411c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "hash": "aff356351126de3409e033a766831f87"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "hash": "2d546104cf408c2277ebf73c3a60d051"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\interceptors\\imply_content_type.dart", "hash": "9955b767fdde0baa759d3431267e5ed5"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\async.dart", "hash": "13c2765ada00f970312dd9680a866556"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\interceptor.dart", "hash": "9c6333c301963385de32595f9442d6ba"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "hash": "6169ff2b33fd5e84357f9296b4bca8a7"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart", "hash": "18939fc6e74f9b391a45ae341fe30128"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "hash": "57ef3d363dc1ad0dd0d7c9f3042b66cc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "hash": "f9cf43c94d23a1e1093f4f1cfd789d18"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "hash": "c03e0a490116f5c2494671b818253be6"}, {"path": "D:\\Users\\aqi2711\\Documents\\aqichat\\frontend\\lib\\services\\websocket_service.dart", "hash": "4db4e1c31977f4398d48c0785b7fc8ca"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "hash": "9ed5b69358848b7dc3a809fad7ffb031"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "hash": "473a3ebe500198a12ebbc76b84ae9784"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "hash": "b7856aab58a26c2a46a4735f1d2ed4b7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\gestures.dart", "hash": "7b6199dff5808c0464b865fe03c4c616"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "hash": "b430b3c9acbccd3305bde7d5057b8c3c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\stack_trace-1.12.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "hash": "d17cba679c8b1c94e10dfe2419b3d581"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart", "hash": "ae1aeff6936f7ec3426e0b531f57a9f1"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "hash": "eb0361b270543f45fe1841c22539289f"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "hash": "e71bfc49f52579c2e458315e9067527e"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "hash": "99712ab0098106c505b424f8542edeca"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "hash": "f2138801d3af6774b0cdb3ff8f966b8c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart", "hash": "2309d30caa73271cde990976da967809"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "hash": "8ebfd4103b3ffc0ad516a44a2ee40748"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "hash": "dc8de4fb0846c2035cd04064058c8ef4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\lib\\src\\byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "hash": "375378eb6cf9297d6bcfe260059193a8"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "hash": "bca784909c10deeb9795f155b7706c92"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\multipart_file.dart", "hash": "4b7bd97845d5fc94f590ed6e58f1c1c1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart", "hash": "16421b0a1dc5136a0b6a71b8d63b3a27"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "hash": "fe8a2d81943a5849c5e939a993b7dd39"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "hash": "e1309fdfc73f3921dc1d2b212d57d217"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\lib\\src\\backends\\memory\\style.dart", "hash": "825ec1b2847bd00ad5cd840c7ddc4d6f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "hash": "294a71aea06bc7ad5af2724726f746f7"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.5\\lib\\src\\factory.dart", "hash": "a79e2b9a182eb762fadaab05e9269edc"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "hash": "65d7d9aae72d673e52ab256ebc374bf5"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "hash": "8757b92070b761566d91ba1be6b1b76c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\cache_object_provider.dart", "hash": "39e587e00bba5c8a7978fd25cf983cc8"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "hash": "217b7c2fd7b1eccde5897e1f17fdccf9"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "hash": "4574091bd15f30d7976e2cd392814a71"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\lib\\src\\transformers\\util\\transform_empty_to_null.dart", "hash": "579bb0bd41c172690d80937bc1ce3b4c"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "hash": "166a96f7e7817372a8e04dc681869e61"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\web_socket_channel-2.4.0\\lib\\status.dart", "hash": "36dc7e69fb658976511ae6de7407181c"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\lib\\src\\span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "hash": "933d793ffbf8f34eeb5f26962e83590f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "hash": "f8867a31bedba73738dabade00f04fea"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "hash": "75c871ac5db5adf93b61d050adad59a4"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "hash": "a7d808c966912115e46d4353372163dd"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\lib\\src\\typed\\stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\lib\\src\\path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "hash": "********************************"}, {"path": "C:\\src\\flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\nested-1.0.0\\lib\\nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\painting\\_web_image_info_io.dart", "hash": "c8ba4eb7463f2816b6b5c054b0876f2f"}, {"path": "D:\\FlutterSdk\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "hash": "13bceb5508fcefacc9ed46137d43844e"}]}