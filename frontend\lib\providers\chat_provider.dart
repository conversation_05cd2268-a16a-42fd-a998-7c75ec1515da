import 'package:flutter/foundation.dart';
import '../models/message.dart';
import '../services/api_service.dart';
import '../services/websocket_service.dart';
import '../services/mock_api_service.dart';
import '../services/real_api_service.dart';

class ChatProvider extends ChangeNotifier {
  final ApiClient _apiClient;
  final MockApiService _mockApi = MockApiService();
  final RealApiService _realApi = RealApiService();
  final WebSocketService _wsService = WebSocketService();

  List<Conversation> _conversations = [];
  List<Message> _currentMessages = [];
  Conversation? _currentConversation;
  bool _isLoading = false;
  String? _error;
  bool _isConnected = false;
  final bool _useMockApi = true; // 临时设置为true使用模拟API

  List<Conversation> get conversations => _conversations;
  List<Message> get currentMessages => _currentMessages;
  Conversation? get currentConversation => _currentConversation;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isConnected => _isConnected;

  ChatProvider(this._apiClient) {
    _initializeWebSocket();
  }

  void _initializeWebSocket() {
    // 监听WebSocket连接状态
    _wsService.connectionStream.listen((connected) {
      _isConnected = connected;
      notifyListeners();
    });

    // 监听WebSocket消息
    _wsService.messageStream.listen((wsMessage) {
      _handleWebSocketMessage(wsMessage);
    });
  }

  Future<void> connectWebSocket() async {
    final accessToken = _apiClient.accessToken;
    if (accessToken != null) {
      await _wsService.connect(accessToken);
    }
  }

  Future<void> disconnectWebSocket() async {
    await _wsService.disconnect();
  }

  Future<void> loadConversations() async {
    // 如果使用真实API但未认证，则不加载
    if (!_useMockApi && !_realApi.isAuthenticated) {
      _conversations = [];
      notifyListeners();
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      if (_useMockApi) {
        final conversations = await _mockApi.getConversations();
        _conversations = conversations;
      } else {
        final conversations = await _realApi.getConversations();
        _conversations = conversations;
      }
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load conversations: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<void> loadMessages(int conversationId, {int page = 1, int limit = 50}) async {
    _setLoading(true);
    _clearError();

    try {
      List<Message> messages;
      if (_useMockApi) {
        messages = await _mockApi.getMessages(conversationId, page, limit);
      } else {
        messages = await _realApi.getMessages(conversationId, page, limit);
      }

      if (page == 1) {
        _currentMessages = messages.reversed.toList(); // 反转以显示最新消息在底部
      } else {
        _currentMessages.insertAll(0, messages.reversed.toList());
      }

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load messages: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<void> sendMessage({
    required int receiverId,
    required String content,
    MessageType type = MessageType.text,
  }) async {
    try {
      Message message;
      if (_useMockApi) {
        final request = SendMessageRequest(
          receiverId: receiverId,
          content: content,
          type: type,
        );
        message = await _mockApi.sendMessage(request);
      } else {
        final request = SendMessageRequest(
          receiverId: receiverId,
          content: content,
          type: type,
        );
        message = await _realApi.sendMessage(request);
      }

      // 添加到当前消息列表
      _currentMessages.add(message);

      // 更新会话列表
      await loadConversations();

      notifyListeners();
    } catch (e) {
      _setError('Failed to send message: ${e.toString()}');
    }
  }

  Future<void> markMessageAsRead(int messageId) async {
    try {
      if (_useMockApi) {
        // Mock API doesn't have markAsRead method, skip
      } else {
        await _realApi.markAsRead(messageId);
      }
      
      // 更新本地消息状态
      final messageIndex = _currentMessages.indexWhere((m) => m.id == messageId);
      if (messageIndex != -1) {
        _currentMessages[messageIndex] = _currentMessages[messageIndex].copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      print('Failed to mark message as read: $e');
    }
  }

  void setCurrentConversation(Conversation? conversation) {
    _currentConversation = conversation;
    _currentMessages.clear();
    
    if (conversation != null) {
      loadMessages(conversation.id);
    }
    
    notifyListeners();
  }

  void sendTypingStatus(int receiverId, bool isTyping) {
    if (_isConnected) {
      _wsService.sendTypingStatus(receiverId, isTyping);
    }
  }

  void _handleWebSocketMessage(WSMessage wsMessage) {
    switch (wsMessage.type) {
      case WSMessageType.message:
        _handleNewMessage(wsMessage.data);
        break;
      case WSMessageType.typing:
        _handleTypingStatus(wsMessage.data);
        break;
      case WSMessageType.userOnline:
      case WSMessageType.userOffline:
        _handleUserStatusChange(wsMessage.data);
        break;
      case WSMessageType.messageRead:
        _handleMessageRead(wsMessage.data);
        break;
    }
  }

  void _handleNewMessage(Map<String, dynamic> data) {
    try {
      // 这里需要根据实际的数据结构来解析消息
      // 简化处理，实际应用中需要更完整的消息解析
      final messageId = data['id'] as int;
      final senderId = data['sender_id'] as int;
      final receiverId = data['receiver_id'] as int;
      final content = data['content'] as String;
      final type = MessageType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => MessageType.text,
      );
      
      // 如果是当前会话的消息，添加到消息列表
      if (_currentConversation != null &&
          ((senderId == _currentConversation!.friend.id) ||
           (receiverId == _currentConversation!.friend.id))) {
        
        // 这里需要构造完整的Message对象
        // 简化处理，实际应用中需要更完整的消息构造
        loadMessages(_currentConversation!.id); // 重新加载消息
      }
      
      // 更新会话列表
      loadConversations();
    } catch (e) {
      print('Error handling new message: $e');
    }
  }

  void _handleTypingStatus(Map<String, dynamic> data) {
    // 处理打字状态
    // 这里可以更新UI显示某个用户正在打字
    notifyListeners();
  }

  void _handleUserStatusChange(Map<String, dynamic> data) {
    // 处理用户在线状态变化
    // 更新好友列表中的用户状态
    notifyListeners();
  }

  void _handleMessageRead(Map<String, dynamic> data) {
    // 处理消息已读状态
    final messageId = data['message_id'] as int;
    
    // 更新本地消息状态
    final messageIndex = _currentMessages.indexWhere((m) => m.id == messageId);
    if (messageIndex != -1) {
      _currentMessages[messageIndex] = _currentMessages[messageIndex].copyWith(
        isRead: true,
        readAt: DateTime.now(),
      );
      notifyListeners();
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    _wsService.dispose();
    super.dispose();
  }
}
