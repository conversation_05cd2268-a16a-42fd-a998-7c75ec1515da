import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../constants/app_constants.dart';
import '../../models/message.dart';
import '../../models/user.dart';
import '../../providers/chat_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/user_avatar.dart';
import '../../widgets/media_picker.dart';
import '../../widgets/image_message.dart';
import '../../widgets/file_message.dart';
import '../../widgets/message_status_indicator.dart';
import '../../widgets/online_status_indicator.dart';
import '../../widgets/slide_animation.dart';
import '../../widgets/qq_style_chat_input.dart';
import '../../services/video_call_service.dart';

class ChatScreen extends StatefulWidget {
  final Conversation conversation;

  const ChatScreen({
    super.key,
    required this.conversation,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final VideoCallService _videoCallService = VideoCallService();
  bool _isTyping = false;
  bool _showTypingIndicator = false;
  bool _isSendButtonPressed = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final chatProvider = Provider.of<ChatProvider>(context, listen: false);
      chatProvider.setCurrentConversation(widget.conversation);
    });

    // 监听输入变化
    _messageController.addListener(_onTextChanged);

    // 模拟对方正在输入
    _simulateTypingIndicator();
  }

  void _onTextChanged() {
    final text = _messageController.text.trim();
    if (text.isNotEmpty && !_isTyping) {
      setState(() {
        _isTyping = true;
      });
      // 这里可以发送正在输入的WebSocket消息
    } else if (text.isEmpty && _isTyping) {
      setState(() {
        _isTyping = false;
      });
      // 这里可以发送停止输入的WebSocket消息
    }
  }

  void _simulateTypingIndicator() {
    // 模拟对方偶尔正在输入
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showTypingIndicator = true;
        });
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              _showTypingIndicator = false;
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _messageController.removeListener(_onTextChanged);
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            UserAvatar(
              user: widget.conversation.friend,
              radius: 18,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.conversation.friend.displayName,
                    style: const TextStyle(fontSize: 16),
                  ),
                  Text(
                    _getStatusText(widget.conversation.friend.status),
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppConstants.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // TODO: 实现更多选项
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child: Consumer<ChatProvider>(
              builder: (context, chatProvider, child) {
                if (chatProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (chatProvider.currentMessages.isEmpty) {
                  return const Center(
                    child: Text(
                      '还没有消息，开始聊天吧！',
                      style: TextStyle(
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(AppConstants.smallPadding),
                  itemCount: chatProvider.currentMessages.length,
                  itemBuilder: (context, index) {
                    final message = chatProvider.currentMessages[index];
                    return SlideInAnimation(
                      begin: Offset(message.senderId == Provider.of<AuthProvider>(context, listen: false).currentUser?.id ? 1.0 : -1.0, 0.0),
                      duration: const Duration(milliseconds: 300),
                      child: _buildMessageBubble(message),
                    );
                  },
                );
              },
            ),
          ),

          // 正在输入指示器
          if (_showTypingIndicator)
            TypingIndicator(
              userName: widget.conversation.friend.displayName,
              isVisible: _showTypingIndicator,
            ),

          // 输入框
          QQStyleChatInput(
            onSendMessage: _sendTextMessage,
            onSendVoice: _sendVoiceMessage,
            onSendImage: _sendImageFile,
            onSendVideo: _sendVideoFile,
            onStartVoiceCall: _startVoiceCall,
            onStartVideoCall: _startVideoCall,
            onSendLocation: _sendLocationMessage,
            onSendContact: _sendContactMessage,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(Message message) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isMe = message.senderId == authProvider.currentUser?.id;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe) ...[
            UserAvatar(
              user: message.sender,
              radius: 16,
              showOnlineStatus: false,
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 10,
              ),
              decoration: BoxDecoration(
                color: isMe 
                    ? AppConstants.sentMessageColor 
                    : AppConstants.receivedMessageColor,
                borderRadius: BorderRadius.circular(18),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildMessageContent(message, isMe),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatTime(message.createdAt),
                        style: TextStyle(
                          color: isMe
                              ? AppConstants.sentMessageTextColor.withOpacity(0.7)
                              : AppConstants.receivedMessageTextColor.withOpacity(0.7),
                          fontSize: 12,
                        ),
                      ),
                      if (isMe) ...[
                        const SizedBox(width: 4),
                        MessageStatusIndicator(
                          status: message.status,
                          size: 14,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            UserAvatar(
              user: message.sender,
              radius: 16,
              showOnlineStatus: false,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // 媒体选择按钮
          IconButton(
            icon: const Icon(
              Icons.add,
              color: AppConstants.primaryColor,
            ),
            onPressed: _showMediaPicker,
            tooltip: '添加媒体',
          ),

          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: AppStrings.typeMessage,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.withOpacity(0.1),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          BounceAnimation(
            isAnimating: _isSendButtonPressed,
            child: CircleAvatar(
              backgroundColor: AppConstants.primaryColor,
              child: IconButton(
                icon: const Icon(
                  Icons.send,
                  color: Colors.white,
                  size: 20,
                ),
                onPressed: () {
                  setState(() {
                    _isSendButtonPressed = true;
                  });
                  _sendMessage();
                  Future.delayed(const Duration(milliseconds: 200), () {
                    if (mounted) {
                      setState(() {
                        _isSendButtonPressed = false;
                      });
                    }
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: content,
    );

    _messageController.clear();
    _scrollToBottom();
  }

  void _showMediaPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MediaPicker(
        onMediaSelected: (path, type) {
          _handleMediaSelected(path, type);
        },
        onImageFromCamera: () {
          Navigator.of(context).pop();
          _sendImageMessage('https://picsum.photos/400/300?random=1', '拍照图片');
        },
        onImageFromGallery: () {
          Navigator.of(context).pop();
          _sendImageMessage('https://picsum.photos/400/300?random=2', '相册图片');
        },
        onFile: () {
          Navigator.of(context).pop();
          _sendFileMessage('示例文档.pdf', '2.5 MB');
        },
        onLocation: () {
          Navigator.of(context).pop();
          _sendLocationMessage();
        },
      ),
    );
  }

  void _handleMediaSelected(String path, MediaType type) {
    switch (type) {
      case MediaType.image:
        _sendImageMessage(path, '图片');
        break;
      case MediaType.file:
        final fileName = path.split('/').last;
        _sendFileMessage(fileName, '文件');
        break;
      case MediaType.audio:
        _sendAudioMessage(path, 30); // 假设30秒时长
        break;
      case MediaType.video:
        _sendVideoMessage(path, '视频');
        break;
    }
  }

  void _sendImageMessage(String imageUrl, String description) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: imageUrl,
      type: MessageType.image,
    );
    _scrollToBottom();
  }

  void _sendFileMessage(String fileName, String fileSize) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '$fileName|$fileSize|https://example.com/files/$fileName',
      type: MessageType.file,
    );
    _scrollToBottom();
  }

  void _sendLocationMessage() {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '我的位置|39.9042,116.4074|北京市朝阳区',
      type: MessageType.text, // 暂时用文本类型
    );
    _scrollToBottom();
  }

  void _sendAudioMessage(String audioPath, int duration) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final fileName = audioPath.split('/').last;
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '$fileName|${duration}s|$audioPath',
      type: MessageType.file, // 暂时用文件类型表示音频
    );
    _scrollToBottom();

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('语音消息发送成功 (${duration}秒)'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _sendVideoMessage(String videoPath, String description) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final fileName = videoPath.split('/').last;
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '$fileName|$description|$videoPath',
      type: MessageType.image, // 暂时用图片类型表示视频
    );
    _scrollToBottom();

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('视频消息发送成功: $description'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Widget _buildMessageContent(Message message, bool isMe) {
    switch (message.type) {
      case MessageType.image:
        return ImageMessage(
          imageUrl: message.content,
          isMe: isMe,
          maxWidth: 200,
        );

      case MessageType.file:
        final parts = message.content.split('|');
        final fileName = parts.isNotEmpty ? parts[0] : '未知文件';
        final fileSize = parts.length > 1 ? parts[1] : '0 B';
        final fileUrl = parts.length > 2 ? parts[2] : null;

        return FileMessage(
          fileName: fileName,
          fileSize: fileSize,
          fileUrl: fileUrl,
          isMe: isMe,
        );

      case MessageType.text:
      default:
        return Text(
          message.content,
          style: TextStyle(
            color: isMe
                ? AppConstants.sentMessageTextColor
                : AppConstants.receivedMessageTextColor,
            fontSize: 16,
          ),
        );
    }
  }

  String _getStatusText(dynamic status) {
    switch (status.toString()) {
      case 'UserStatus.online':
        return AppStrings.online;
      case 'UserStatus.offline':
        return AppStrings.offline;
      case 'UserStatus.away':
        return AppStrings.away;
      case 'UserStatus.busy':
        return AppStrings.busy;
      default:
        return AppStrings.offline;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  // 新的输入栏回调方法
  void _sendTextMessage(String message) {
    if (message.trim().isEmpty) return;

    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: message.trim(),
      type: MessageType.text,
    );
    _scrollToBottom();
  }

  void _sendVoiceMessage(String voicePath) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final fileName = voicePath.split('/').last;
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '$fileName|30s|$voicePath', // 模拟30秒语音
      type: MessageType.audio,
    );
    _scrollToBottom();
  }

  void _sendImageFile(XFile imageFile) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: imageFile.path,
      type: MessageType.image,
    );
    _scrollToBottom();
  }

  void _sendVideoFile(XFile videoFile) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final fileName = videoFile.name;
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '$fileName|视频消息|${videoFile.path}',
      type: MessageType.video,
    );
    _scrollToBottom();
  }

  void _sendFile(XFile file) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final fileName = file.name;
    final fileSize = '未知大小'; // 实际应用中需要获取文件大小
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '$fileName|$fileSize|${file.path}',
      type: MessageType.file,
    );
    _scrollToBottom();
  }

  void _sendContactMessage(User contact) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      receiverId: widget.conversation.friend.id,
      content: '${contact.nickname ?? contact.username}|${contact.id}|个人名片',
      type: MessageType.text, // 暂时用文本类型
    );
    _scrollToBottom();
  }

  void _startVoiceCall() {
    _videoCallService.startAudioCall(widget.conversation.friend);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('正在发起语音通话给 ${widget.conversation.friend.displayName}...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _startVideoCall() {
    _videoCallService.startVideoCall(widget.conversation.friend);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('正在发起视频通话给 ${widget.conversation.friend.displayName}...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
