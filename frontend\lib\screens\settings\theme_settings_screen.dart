import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/slide_animation.dart';

class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({super.key});

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen> {
  ThemeMode _selectedThemeMode = ThemeMode.system;
  Color _selectedPrimaryColor = AppConstants.primaryColor;
  double _fontSize = 16.0;
  String _selectedFont = 'System';

  final List<ThemeOption> _themeOptions = [
    ThemeOption(
      mode: ThemeMode.system,
      title: '跟随系统',
      subtitle: '根据系统设置自动切换',
      icon: Icons.brightness_auto,
    ),
    ThemeOption(
      mode: ThemeMode.light,
      title: '浅色模式',
      subtitle: '始终使用浅色主题',
      icon: Icons.brightness_7,
    ),
    ThemeOption(
      mode: ThemeMode.dark,
      title: '深色模式',
      subtitle: '始终使用深色主题',
      icon: Icons.brightness_2,
    ),
  ];

  final List<ColorOption> _colorOptions = [
    ColorOption(color: const Color(0xFF2196F3), name: '蓝色'),
    ColorOption(color: const Color(0xFF4CAF50), name: '绿色'),
    ColorOption(color: const Color(0xFFFF9800), name: '橙色'),
    ColorOption(color: const Color(0xFF9C27B0), name: '紫色'),
    ColorOption(color: const Color(0xFFF44336), name: '红色'),
    ColorOption(color: const Color(0xFF607D8B), name: '蓝灰色'),
    ColorOption(color: const Color(0xFF795548), name: '棕色'),
    ColorOption(color: const Color(0xFF009688), name: '青色'),
  ];

  final List<String> _fontOptions = [
    'System',
    'Roboto',
    'Noto Sans',
    'Source Sans Pro',
    'Open Sans',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('主题设置'),
        backgroundColor: _selectedPrimaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 主题模式
          SlideInAnimation(
            begin: const Offset(0.0, -1.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '主题模式',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    ..._themeOptions.map((option) => RadioListTile<ThemeMode>(
                      title: Row(
                        children: [
                          Icon(option.icon, color: _selectedPrimaryColor),
                          const SizedBox(width: 12),
                          Text(option.title),
                        ],
                      ),
                      subtitle: Text(option.subtitle),
                      value: option.mode,
                      groupValue: _selectedThemeMode,
                      onChanged: (ThemeMode? value) {
                        if (value != null) {
                          setState(() {
                            _selectedThemeMode = value;
                          });
                          _saveThemeSettings();
                        }
                      },
                      activeColor: _selectedPrimaryColor,
                    )),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 主题色彩
          SlideInAnimation(
            begin: const Offset(-1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '主题色彩',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                        childAspectRatio: 1,
                      ),
                      itemCount: _colorOptions.length,
                      itemBuilder: (context, index) {
                        final colorOption = _colorOptions[index];
                        final isSelected = colorOption.color.value == _selectedPrimaryColor.value;
                        
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedPrimaryColor = colorOption.color;
                            });
                            _saveThemeSettings();
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: colorOption.color,
                              shape: BoxShape.circle,
                              border: isSelected
                                  ? Border.all(color: Colors.white, width: 3)
                                  : null,
                              boxShadow: isSelected
                                  ? [
                                      BoxShadow(
                                        color: colorOption.color.withValues(alpha: 0.5),
                                        blurRadius: 8,
                                        spreadRadius: 2,
                                      ),
                                    ]
                                  : null,
                            ),
                            child: isSelected
                                ? const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 24,
                                  )
                                : null,
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    Text(
                      '当前选择: ${_colorOptions.firstWhere((option) => option.color.value == _selectedPrimaryColor.value).name}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 字体设置
          SlideInAnimation(
            begin: const Offset(1.0, 0.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '字体设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // 字体大小
                    Row(
                      children: [
                        const Text('字体大小'),
                        const Spacer(),
                        Text('${_fontSize.toInt()}'),
                      ],
                    ),
                    Slider(
                      value: _fontSize,
                      min: 12.0,
                      max: 24.0,
                      divisions: 12,
                      activeColor: _selectedPrimaryColor,
                      onChanged: (value) {
                        setState(() {
                          _fontSize = value;
                        });
                      },
                      onChangeEnd: (value) {
                        _saveThemeSettings();
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 字体预览
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '这是字体预览文本\nThis is a font preview text',
                        style: TextStyle(fontSize: _fontSize),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 字体选择
                    DropdownButtonFormField<String>(
                      value: _selectedFont,
                      decoration: const InputDecoration(
                        labelText: '字体',
                        border: OutlineInputBorder(),
                      ),
                      items: _fontOptions.map((font) => DropdownMenuItem(
                        value: font,
                        child: Text(font),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedFont = value;
                          });
                          _saveThemeSettings();
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 聊天界面预览
          SlideInAnimation(
            begin: const Offset(0.0, 1.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '聊天界面预览',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    Container(
                      height: 200,
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          // 模拟聊天头部
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: _selectedPrimaryColor,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(8),
                              ),
                            ),
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 16,
                                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                                  child: const Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '好友名称',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: _fontSize * 0.9,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          // 模拟消息
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                children: [
                                  // 接收的消息
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.grey[200],
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Text(
                                        '你好！',
                                        style: TextStyle(fontSize: _fontSize * 0.9),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  
                                  // 发送的消息
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        color: _selectedPrimaryColor,
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Text(
                                        '你好，很高兴认识你！',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: _fontSize * 0.9,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 重置按钮
          SlideInAnimation(
            begin: const Offset(0.0, 1.0),
            child: SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: _resetToDefault,
                style: OutlinedButton.styleFrom(
                  foregroundColor: _selectedPrimaryColor,
                  side: BorderSide(color: _selectedPrimaryColor),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('恢复默认设置'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveThemeSettings() {
    // 在实际应用中，这里会保存主题设置到本地存储
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('主题设置已保存'),
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _resetToDefault() {
    setState(() {
      _selectedThemeMode = ThemeMode.system;
      _selectedPrimaryColor = AppConstants.primaryColor;
      _fontSize = 16.0;
      _selectedFont = 'System';
    });
    _saveThemeSettings();
  }
}

class ThemeOption {
  final ThemeMode mode;
  final String title;
  final String subtitle;
  final IconData icon;

  ThemeOption({
    required this.mode,
    required this.title,
    required this.subtitle,
    required this.icon,
  });
}

class ColorOption {
  final Color color;
  final String name;

  ColorOption({
    required this.color,
    required this.name,
  });
}
