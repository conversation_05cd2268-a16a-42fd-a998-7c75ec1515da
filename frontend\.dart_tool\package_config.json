{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-76.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "_macros", "rootUri": "file:///D:/FlutterSdk/flutter/bin/cache/dart-sdk/pkg/_macros", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "analyzer", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/analyzer-6.11.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "args", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/build-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_config", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_daemon", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_resolvers", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner_core", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-9.1.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "built_collection", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.10.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.4", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "clock", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/dart_style-2.3.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dio", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/dio-5.8.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_web_adapter", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "fake_async", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_picker", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/file_picker-6.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_linux", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/file_selector_macos-0.9.4+3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "file_selector_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///D:/FlutterSdk/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_cache_manager", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_lints", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_riverpod", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_test", "rootUri": "file:///D:/FlutterSdk/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///D:/FlutterSdk/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "frontend_server_client", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "go_router", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/go_router-12.1.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "graphs", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hive", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/hive-2.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_flutter", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/hive_flutter-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_generator", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/hive_generator-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_multi_server", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.12+23", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "image_picker_for_web", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "intl", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "io", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "js", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "json_annotation", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_serializable", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.9.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "leak_tracker", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "logging", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "macros", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/macros-0.1.3-main.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "package_config", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "platform", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pool", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "protobuf", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/protobuf-3.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "provider", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "retrofit", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/retrofit-4.5.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "retrofit_generator", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/retrofit_generator-8.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "riverpod", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "rxdart", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "shared_preferences", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_web_socket", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-3.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sky_engine", "rootUri": "file:///D:/FlutterSdk/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_gen", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/source_gen-1.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "source_helper", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "source_span", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "state_notifier", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/state_notifier-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stream_channel", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.4.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "term_glyph", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timing", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "tuple", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/tuple-2.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "typed_data", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "uuid", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-2.4.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "win32", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/win32-5.14.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "xdg_directories", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "yaml", "rootUri": "file:///C:/src/flutter/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "aqichat", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///D:/FlutterSdk/flutter", "flutterVersion": "3.32.4", "pubCache": "file:///C:/src/flutter/.pub-cache"}