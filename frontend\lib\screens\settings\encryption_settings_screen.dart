import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../services/encryption_service.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/slide_animation.dart';

class EncryptionSettingsScreen extends StatefulWidget {
  const EncryptionSettingsScreen({super.key});

  @override
  State<EncryptionSettingsScreen> createState() => _EncryptionSettingsScreenState();
}

class _EncryptionSettingsScreenState extends State<EncryptionSettingsScreen> {
  final EncryptionService _encryptionService = EncryptionService();
  EncryptionSettings _settings = EncryptionSettings();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟加载设置
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 在实际应用中，这里会从本地存储或服务器加载设置
      setState(() {
        _settings = EncryptionSettings();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载设置失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟保存设置
      await Future.delayed(const Duration(milliseconds: 500));
      
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('设置已保存'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存设置失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _generateNewKeyPair() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重新生成密钥'),
        content: const Text(
          '重新生成密钥对将使所有现有的加密对话无法解密。\n\n'
          '建议在生成新密钥前备份重要消息。\n\n'
          '确定要继续吗？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 生成新的密钥对
      _encryptionService.generateKeyPair(currentUser.id);
      
      await Future.delayed(const Duration(milliseconds: 500));
      
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('新密钥对已生成'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('生成密钥失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showEncryptionInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('端到端加密'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '什么是端到端加密？',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                '端到端加密确保只有您和对话的另一方能够阅读发送的消息。即使是我们也无法读取您的消息。',
              ),
              SizedBox(height: 16),
              Text(
                '加密算法',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• 消息加密：AES-256-GCM'),
              Text('• 密钥交换：ECDH'),
              Text('• 消息签名：ECDSA'),
              SizedBox(height: 16),
              Text(
                '安全特性',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• 前向安全性'),
              Text('• 消息完整性验证'),
              Text('• 密钥自动轮换'),
              Text('• 离线消息加密'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('加密设置'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showEncryptionInfo,
            tooltip: '加密说明',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // 加密状态
                SlideInAnimation(
                  begin: const Offset(0.0, -1.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.security,
                                color: AppConstants.primaryColor,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                '加密状态',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          
                          if (currentUser != null) ...[
                            ListTile(
                              leading: const Icon(Icons.vpn_key, color: Colors.green),
                              title: const Text('密钥对状态'),
                              subtitle: Text(
                                _encryptionService.getPublicKey(currentUser.id) != null
                                    ? '已生成'
                                    : '未生成',
                              ),
                              trailing: _encryptionService.getPublicKey(currentUser.id) != null
                                  ? const Icon(Icons.check_circle, color: Colors.green)
                                  : const Icon(Icons.warning, color: Colors.orange),
                            ),
                            
                            ListTile(
                              leading: const Icon(Icons.fingerprint, color: Colors.blue),
                              title: const Text('公钥指纹'),
                              subtitle: Text(
                                _encryptionService.getPublicKey(currentUser.id)?.substring(0, 16) ?? '未生成',
                                style: const TextStyle(fontFamily: 'monospace'),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // 加密设置
                SlideInAnimation(
                  begin: const Offset(-1.0, 0.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '加密设置',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          SwitchListTile(
                            title: const Text('自动加密新对话'),
                            subtitle: const Text('新的私聊对话将自动启用端到端加密'),
                            value: _settings.autoEncrypt,
                            onChanged: (value) {
                              setState(() {
                                _settings = _settings.copyWith(autoEncrypt: value);
                              });
                              _saveSettings();
                            },
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('验证消息签名'),
                            subtitle: const Text('验证收到消息的完整性和真实性'),
                            value: _settings.verifySignatures,
                            onChanged: (value) {
                              setState(() {
                                _settings = _settings.copyWith(verifySignatures: value);
                              });
                              _saveSettings();
                            },
                            activeColor: AppConstants.primaryColor,
                          ),
                          
                          SwitchListTile(
                            title: const Text('群聊加密'),
                            subtitle: const Text('为群聊启用端到端加密'),
                            value: _settings.enableForGroups,
                            onChanged: (value) {
                              setState(() {
                                _settings = _settings.copyWith(enableForGroups: value);
                              });
                              _saveSettings();
                            },
                            activeColor: AppConstants.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // 密钥管理
                SlideInAnimation(
                  begin: const Offset(1.0, 0.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '密钥管理',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          ListTile(
                            leading: const Icon(Icons.refresh, color: AppConstants.primaryColor),
                            title: const Text('重新生成密钥对'),
                            subtitle: const Text('生成新的加密密钥对'),
                            onTap: _generateNewKeyPair,
                          ),
                          
                          ListTile(
                            leading: const Icon(Icons.schedule, color: Colors.orange),
                            title: const Text('密钥轮换间隔'),
                            subtitle: Text('${_settings.keyRotationInterval.inDays} 天'),
                            onTap: () {
                              // TODO: 显示密钥轮换间隔设置对话框
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('密钥轮换间隔设置功能开发中'),
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            },
                          ),
                          
                          ListTile(
                            leading: const Icon(Icons.backup, color: Colors.blue),
                            title: const Text('备份密钥'),
                            subtitle: const Text('导出密钥以便在其他设备使用'),
                            onTap: () {
                              // TODO: 实现密钥备份功能
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('密钥备份功能开发中'),
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
