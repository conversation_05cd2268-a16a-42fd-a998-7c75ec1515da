import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:file_picker/file_picker.dart'; // 暂时禁用
import '../constants/app_constants.dart';

// 媒体类型枚举
enum MediaType {
  image,
  file,
  audio,
  video,
}

class MediaPicker extends StatelessWidget {
  final Function(String path, MediaType type)? onMediaSelected;
  final VoidCallback? onImageFromCamera;
  final VoidCallback? onImageFromGallery;
  final VoidCallback? onFile;
  final VoidCallback? onLocation;

  const MediaPicker({
    super.key,
    this.onMediaSelected,
    this.onImageFromCamera,
    this.onImageFromGallery,
    this.onFile,
    this.onLocation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // 标题
          const Text(
            '选择媒体类型',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 20),
          
          // 选项网格
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildOption(
                icon: Icons.camera_alt,
                label: '拍照',
                color: AppConstants.primaryColor,
                onTap: onImageFromCamera ?? () => _pickImage(context, ImageSource.camera),
              ),
              _buildOption(
                icon: Icons.photo_library,
                label: '相册',
                color: Colors.green,
                onTap: onImageFromGallery ?? () => _pickImage(context, ImageSource.gallery),
              ),
              _buildOption(
                icon: Icons.attach_file,
                label: '文件',
                color: Colors.orange,
                onTap: onFile ?? () => _pickFile(context),
              ),
              _buildOption(
                icon: Icons.mic,
                label: '语音',
                color: Colors.red,
                onTap: () => _showAudioRecorder(context),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 取消按钮
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: const Text(
                '取消',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
          
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[200]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void show(
    BuildContext context, {
    VoidCallback? onImageFromCamera,
    VoidCallback? onImageFromGallery,
    VoidCallback? onFile,
    VoidCallback? onLocation,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MediaPicker(
        onImageFromCamera: onImageFromCamera,
        onImageFromGallery: onImageFromGallery,
        onFile: onFile,
        onLocation: onLocation,
      ),
    );
  }

  Future<void> _pickImage(BuildContext context, ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null && onMediaSelected != null) {
        if (context.mounted) {
          Navigator.of(context).pop();
        }
        onMediaSelected!(image.path, MediaType.image);
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('选择图片失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _pickFile(BuildContext context) async {
    try {
      // 暂时禁用文件选择功能
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('文件选择功能暂时不可用'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      // FilePickerResult? result = await FilePicker.platform.pickFiles(
      //   type: FileType.any,
      //   allowMultiple: false,
      // );

      // if (result != null && result.files.single.path != null && onMediaSelected != null) {
      //   if (context.mounted) {
      //     Navigator.of(context).pop();
      //   }
      //   onMediaSelected!(result.files.single.path!, MediaType.file);
      // }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('选择文件失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showAudioRecorder(BuildContext context) {
    Navigator.of(context).pop();

    // 显示语音录制对话框
    showDialog(
      context: context,
      builder: (context) => AudioRecorderDialog(
        onAudioRecorded: (path) {
          if (onMediaSelected != null) {
            onMediaSelected!(path, MediaType.audio);
          }
        },
      ),
    );
  }
}

// 语音录制对话框
class AudioRecorderDialog extends StatefulWidget {
  final Function(String path) onAudioRecorded;

  const AudioRecorderDialog({
    super.key,
    required this.onAudioRecorded,
  });

  @override
  State<AudioRecorderDialog> createState() => _AudioRecorderDialogState();
}

class _AudioRecorderDialogState extends State<AudioRecorderDialog>
    with TickerProviderStateMixin {
  bool _isRecording = false;
  bool _isPaused = false;
  int _recordDuration = 0;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startRecording() {
    setState(() {
      _isRecording = true;
      _isPaused = false;
    });
    _animationController.repeat(reverse: true);

    // 模拟录音计时
    _startTimer();
  }

  void _stopRecording() {
    setState(() {
      _isRecording = false;
      _isPaused = false;
    });
    _animationController.stop();

    // 模拟保存录音文件
    Navigator.of(context).pop();
    widget.onAudioRecorded('audio_${DateTime.now().millisecondsSinceEpoch}.m4a');

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('录音完成，时长: ${_formatDuration(_recordDuration)}'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _pauseRecording() {
    setState(() {
      _isPaused = !_isPaused;
    });

    if (_isPaused) {
      _animationController.stop();
    } else {
      _animationController.repeat(reverse: true);
    }
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (_isRecording && mounted) {
        setState(() {
          _recordDuration++;
        });
        if (!_isPaused) {
          _startTimer();
        }
      }
    });
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '语音录制',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),

            // 录音时长显示
            Text(
              _formatDuration(_recordDuration),
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 24),

            // 录音按钮
            AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _isRecording ? _scaleAnimation.value : 1.0,
                  child: GestureDetector(
                    onTap: _isRecording ? null : _startRecording,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: _isRecording ? Colors.red : AppConstants.primaryColor,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: (_isRecording ? Colors.red : AppConstants.primaryColor)
                                .withValues(alpha: 0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Icon(
                        _isRecording ? Icons.stop : Icons.mic,
                        color: Colors.white,
                        size: 36,
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),

            // 控制按钮
            if (_isRecording) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: _pauseRecording,
                    icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
                    label: Text(_isPaused ? '继续' : '暂停'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _stopRecording,
                    icon: const Icon(Icons.stop),
                    label: const Text('完成'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ] else ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  ElevatedButton(
                    onPressed: _startRecording,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('开始录音'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
