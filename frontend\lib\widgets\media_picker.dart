import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class MediaPicker extends StatelessWidget {
  final VoidCallback? onImageFromCamera;
  final VoidCallback? onImageFromGallery;
  final VoidCallback? onFile;
  final VoidCallback? onLocation;

  const MediaPicker({
    super.key,
    this.onImageFromCamera,
    this.onImageFromGallery,
    this.onFile,
    this.onLocation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // 标题
          const Text(
            '选择媒体类型',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 20),
          
          // 选项网格
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildOption(
                icon: Icons.camera_alt,
                label: '拍照',
                color: AppConstants.primaryColor,
                onTap: onImageFromCamera,
              ),
              _buildOption(
                icon: Icons.photo_library,
                label: '相册',
                color: Colors.green,
                onTap: onImageFromGallery,
              ),
              _buildOption(
                icon: Icons.attach_file,
                label: '文件',
                color: Colors.orange,
                onTap: onFile,
              ),
              _buildOption(
                icon: Icons.location_on,
                label: '位置',
                color: Colors.red,
                onTap: onLocation,
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 取消按钮
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: const Text(
                '取消',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
          
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[200]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void show(
    BuildContext context, {
    VoidCallback? onImageFromCamera,
    VoidCallback? onImageFromGallery,
    VoidCallback? onFile,
    VoidCallback? onLocation,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MediaPicker(
        onImageFromCamera: onImageFromCamera,
        onImageFromGallery: onImageFromGallery,
        onFile: onFile,
        onLocation: onLocation,
      ),
    );
  }
}
