import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../models/user.dart';
import '../../widgets/user_avatar.dart';
import '../../widgets/slide_animation.dart';

class ProfileViewScreen extends StatelessWidget {
  final User user;
  final bool isCurrentUser;

  const ProfileViewScreen({
    super.key,
    required this.user,
    this.isCurrentUser = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isCurrentUser ? '我的资料' : '用户资料'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (isCurrentUser)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                Navigator.pushNamed(context, '/profile/edit');
              },
              tooltip: '编辑资料',
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 头部背景
            Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppConstants.primaryColor,
                    AppConstants.primaryColor.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 头像
                  SlideInAnimation(
                    direction: SlideDirection.top,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: ClipOval(
                        child: UserAvatar(user: user, radius: 60),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // 昵称
                  SlideInAnimation(
                    direction: SlideDirection.bottom,
                    delay: const Duration(milliseconds: 200),
                    child: Text(
                      user.nickname ?? user.username,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  
                  // 在线状态
                  SlideInAnimation(
                    direction: SlideDirection.bottom,
                    delay: const Duration(milliseconds: 400),
                    child: Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _getStatusColor(user.status),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _getStatusText(user.status),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 用户信息卡片
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 基本信息
                  ListItemAnimation(
                    index: 0,
                    child: _buildInfoCard(
                      title: '基本信息',
                      children: [
                        _buildInfoRow(
                          icon: Icons.person,
                          label: '用户名',
                          value: '@${user.username}',
                        ),
                        _buildInfoRow(
                          icon: Icons.email,
                          label: '邮箱',
                          value: user.email,
                        ),
                        if (user.bio != null && user.bio!.isNotEmpty)
                          _buildInfoRow(
                            icon: Icons.info,
                            label: '个人简介',
                            value: user.bio!,
                            isMultiline: true,
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 账户信息
                  ListItemAnimation(
                    index: 1,
                    child: _buildInfoCard(
                      title: '账户信息',
                      children: [
                        _buildInfoRow(
                          icon: Icons.calendar_today,
                          label: '注册时间',
                          value: _formatDate(user.createdAt),
                        ),
                        if (user.lastSeen != null)
                          _buildInfoRow(
                            icon: Icons.access_time,
                            label: '最后在线',
                            value: _formatLastSeen(user.lastSeen!),
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 操作按钮
                  if (!isCurrentUser) ...[
                    ListItemAnimation(
                      index: 2,
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                // TODO: 发送消息
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('与 ${user.nickname ?? user.username} 开始聊天'),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              },
                              icon: const Icon(Icons.chat),
                              label: const Text('发送消息'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppConstants.primaryColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () {
                                // TODO: 添加好友
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('已向 ${user.nickname ?? user.username} 发送好友请求'),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              },
                              icon: const Icon(Icons.person_add),
                              label: const Text('添加好友'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: AppConstants.primaryColor,
                                side: const BorderSide(color: AppConstants.primaryColor),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    bool isMultiline = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return Colors.green;
      case UserStatus.away:
        return Colors.orange;
      case UserStatus.busy:
        return Colors.red;
      case UserStatus.offline:
        return Colors.grey;
    }
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return '在线';
      case UserStatus.away:
        return '离开';
      case UserStatus.busy:
        return '忙碌';
      case UserStatus.offline:
        return '离线';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}年${date.month}月${date.day}日';
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return _formatDate(lastSeen);
    }
  }
}
