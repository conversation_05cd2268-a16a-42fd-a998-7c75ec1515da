import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class FriendsScreen extends StatefulWidget {
  const FriendsScreen({super.key});

  @override
  State<FriendsScreen> createState() => _FriendsScreenState();
}

class _FriendsScreenState extends State<FriendsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.friends),
        actions: [
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: () {
              // TODO: 实现添加好友功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('添加好友功能开发中...')),
              );
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppConstants.textHint,
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 16),
            Text(
              AppStrings.noFriends,
              style: TextStyle(
                color: AppConstants.textSecondary,
                fontSize: 16,
              ),
            ),
            Sized<PERSON>ox(height: 8),
            Text(
              '点击右上角添加好友',
              style: TextStyle(
                color: AppConstants.textHint,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
