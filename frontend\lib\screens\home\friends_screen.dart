import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../friends/user_search_screen.dart';
import '../friends/friend_requests_screen.dart';
import '../../widgets/slide_animation.dart';
import '../../widgets/user_avatar.dart';
import '../../models/user.dart';
import '../../services/mock_api_service.dart';
import '../../widgets/loading_animation.dart';

class FriendsScreen extends StatefulWidget {
  const FriendsScreen({super.key});

  @override
  State<FriendsScreen> createState() => _FriendsScreenState();
}

class _FriendsScreenState extends State<FriendsScreen> {
  final MockApiService _mockApi = MockApiService();
  List<User> _friends = [];
  bool _isLoading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadFriends();
  }

  Future<void> _loadFriends() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      // 模拟加载好友列表
      await Future.delayed(const Duration(seconds: 1));

      // 模拟好友数据
      _friends = [
        User(
          id: 2,
          username: 'alice_wonder',
          email: '<EMAIL>',
          nickname: 'Alice Wonder',
          avatar: '',
          status: UserStatus.online,
          lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now(),
        ),
        User(
          id: 3,
          username: 'bob_builder',
          email: '<EMAIL>',
          nickname: 'Bob Builder',
          avatar: '',
          status: UserStatus.away,
          lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
          createdAt: DateTime.now().subtract(const Duration(days: 25)),
          updatedAt: DateTime.now(),
        ),
        User(
          id: 4,
          username: 'charlie_brown',
          email: '<EMAIL>',
          nickname: 'Charlie Brown',
          avatar: '',
          status: UserStatus.offline,
          lastSeen: DateTime.now().subtract(const Duration(days: 1)),
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          updatedAt: DateTime.now(),
        ),
      ];

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = '加载好友列表失败: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.friends),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FriendRequestsScreen(),
                ),
              );
            },
            tooltip: '好友请求',
          ),
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UserSearchScreen(),
                ),
              );
            },
            tooltip: '添加好友',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingAnimation(
          size: 60,
          message: '加载好友列表中...',
        ),
      );
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _error,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFriends,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_friends.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppConstants.textHint,
            ),
            SizedBox(height: 16),
            Text(
              '暂无好友',
              style: TextStyle(
                color: AppConstants.textSecondary,
                fontSize: 16,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '点击右上角添加好友',
              style: TextStyle(
                color: AppConstants.textHint,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFriends,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _friends.length,
        itemBuilder: (context, index) {
          final friend = _friends[index];
          return ListItemAnimation(
            index: index,
            child: _buildFriendItem(friend),
          );
        },
      ),
    );
  }

  Widget _buildFriendItem(User friend) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: UserAvatar(
          user: friend,
          radius: 24,
        ),
        title: Text(
          friend.nickname ?? friend.username,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '@${friend.username}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _getStatusText(friend),
              style: TextStyle(
                fontSize: 12,
                color: _getStatusColor(friend.status),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleFriendAction(friend, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'chat',
              child: Row(
                children: [
                  Icon(Icons.chat, size: 18),
                  SizedBox(width: 8),
                  Text('发送消息'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person, size: 18),
                  SizedBox(width: 8),
                  Text('查看资料'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: Row(
                children: [
                  Icon(Icons.person_remove, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('删除好友', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getStatusText(User user) {
    switch (user.status) {
      case UserStatus.online:
        return '在线';
      case UserStatus.away:
        return '离开';
      case UserStatus.busy:
        return '忙碌';
      case UserStatus.offline:
        if (user.lastSeen != null) {
          final difference = DateTime.now().difference(user.lastSeen!);
          if (difference.inMinutes < 60) {
            return '${difference.inMinutes}分钟前在线';
          } else if (difference.inHours < 24) {
            return '${difference.inHours}小时前在线';
          } else {
            return '${difference.inDays}天前在线';
          }
        }
        return '离线';
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.online:
        return Colors.green;
      case UserStatus.away:
        return Colors.orange;
      case UserStatus.busy:
        return Colors.red;
      case UserStatus.offline:
        return Colors.grey;
    }
  }

  void _handleFriendAction(User friend, String action) {
    switch (action) {
      case 'chat':
        // TODO: 导航到聊天界面
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('与 ${friend.nickname} 开始聊天'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
      case 'profile':
        // TODO: 显示用户资料
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('查看 ${friend.nickname} 的资料'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
      case 'remove':
        _showRemoveFriendDialog(friend);
        break;
    }
  }

  void _showRemoveFriendDialog(User friend) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除好友'),
        content: Text('确定要删除好友 ${friend.nickname} 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _removeFriend(friend);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _removeFriend(User friend) {
    setState(() {
      _friends.removeWhere((f) => f.id == friend.id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已删除好友 ${friend.nickname}'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: '撤销',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              _friends.add(friend);
            });
          },
        ),
      ),
    );
  }
}
