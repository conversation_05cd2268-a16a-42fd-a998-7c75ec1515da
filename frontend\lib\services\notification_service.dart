import 'dart:async';
import 'package:flutter/material.dart';
import '../models/message.dart';
import '../models/user.dart';

/// 通知服务
/// 处理推送通知、消息提醒、免打扰模式等功能
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // 通知设置
  NotificationSettings _settings = NotificationSettings();
  
  // 免打扰时间段
  final List<DoNotDisturbPeriod> _doNotDisturbPeriods = [];
  
  // 事件流
  final StreamController<NotificationEvent> _eventController = 
      StreamController<NotificationEvent>.broadcast();
  
  Stream<NotificationEvent> get eventStream => _eventController.stream;

  /// 获取通知设置
  NotificationSettings get settings => _settings;

  /// 更新通知设置
  Future<void> updateSettings(NotificationSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
    
    _eventController.add(NotificationEvent(
      type: NotificationEventType.settingsUpdated,
      settings: _settings,
    ));
  }

  /// 检查是否在免打扰时间
  bool isInDoNotDisturbMode() {
    if (!_settings.enableDoNotDisturb) return false;
    
    final now = DateTime.now();
    final currentTime = TimeOfDay.fromDateTime(now);
    
    for (final period in _doNotDisturbPeriods) {
      if (period.isActive && _isTimeInRange(currentTime, period.startTime, period.endTime)) {
        return true;
      }
    }
    
    return false;
  }

  /// 显示消息通知
  Future<void> showMessageNotification(Message message, User sender) async {
    // 检查通知设置
    if (!_settings.enableNotifications) return;
    if (isInDoNotDisturbMode()) return;
    
    // 检查消息类型通知设置
    if (!_shouldShowNotificationForMessageType(message.type)) return;
    
    try {
      final notification = AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: sender.nickname ?? sender.username,
        body: _getNotificationBody(message),
        type: NotificationType.message,
        data: {
          'messageId': message.id.toString(),
          'senderId': sender.id.toString(),
          'senderName': sender.nickname ?? sender.username,
        },
        createdAt: DateTime.now(),
      );

      // 模拟显示系统通知
      await _showSystemNotification(notification);
      
      _eventController.add(NotificationEvent(
        type: NotificationEventType.notificationShown,
        notification: notification,
      ));
    } catch (e) {
      _eventController.add(NotificationEvent(
        type: NotificationEventType.notificationError,
        error: e.toString(),
      ));
    }
  }

  /// 显示好友请求通知
  Future<void> showFriendRequestNotification(User requester) async {
    if (!_settings.enableNotifications || !_settings.enableFriendRequestNotifications) return;
    if (isInDoNotDisturbMode()) return;
    
    try {
      final notification = AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '新的好友请求',
        body: '${requester.nickname ?? requester.username} 想要添加您为好友',
        type: NotificationType.friendRequest,
        data: {
          'requesterId': requester.id.toString(),
          'requesterName': requester.nickname ?? requester.username,
        },
        createdAt: DateTime.now(),
      );

      await _showSystemNotification(notification);
      
      _eventController.add(NotificationEvent(
        type: NotificationEventType.notificationShown,
        notification: notification,
      ));
    } catch (e) {
      _eventController.add(NotificationEvent(
        type: NotificationEventType.notificationError,
        error: e.toString(),
      ));
    }
  }

  /// 显示群聊邀请通知
  Future<void> showGroupInviteNotification(String groupName, User inviter) async {
    if (!_settings.enableNotifications || !_settings.enableGroupNotifications) return;
    if (isInDoNotDisturbMode()) return;
    
    try {
      final notification = AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '群聊邀请',
        body: '${inviter.nickname ?? inviter.username} 邀请您加入群聊 "$groupName"',
        type: NotificationType.groupInvite,
        data: {
          'groupName': groupName,
          'inviterId': inviter.id.toString(),
          'inviterName': inviter.nickname ?? inviter.username,
        },
        createdAt: DateTime.now(),
      );

      await _showSystemNotification(notification);
      
      _eventController.add(NotificationEvent(
        type: NotificationEventType.notificationShown,
        notification: notification,
      ));
    } catch (e) {
      _eventController.add(NotificationEvent(
        type: NotificationEventType.notificationError,
        error: e.toString(),
      ));
    }
  }

  /// 添加免打扰时间段
  void addDoNotDisturbPeriod(DoNotDisturbPeriod period) {
    _doNotDisturbPeriods.add(period);
    _saveDoNotDisturbPeriods();
    
    _eventController.add(NotificationEvent(
      type: NotificationEventType.doNotDisturbUpdated,
      doNotDisturbPeriods: List.from(_doNotDisturbPeriods),
    ));
  }

  /// 移除免打扰时间段
  void removeDoNotDisturbPeriod(String id) {
    _doNotDisturbPeriods.removeWhere((period) => period.id == id);
    _saveDoNotDisturbPeriods();
    
    _eventController.add(NotificationEvent(
      type: NotificationEventType.doNotDisturbUpdated,
      doNotDisturbPeriods: List.from(_doNotDisturbPeriods),
    ));
  }

  /// 获取免打扰时间段列表
  List<DoNotDisturbPeriod> getDoNotDisturbPeriods() {
    return List.unmodifiable(_doNotDisturbPeriods);
  }

  /// 清除所有通知
  Future<void> clearAllNotifications() async {
    // 模拟清除系统通知
    await Future.delayed(const Duration(milliseconds: 100));
    
    _eventController.add(NotificationEvent(
      type: NotificationEventType.notificationsCleared,
    ));
  }

  // 私有方法

  bool _shouldShowNotificationForMessageType(MessageType type) {
    switch (type) {
      case MessageType.text:
        return _settings.enableTextNotifications;
      case MessageType.image:
        return _settings.enableImageNotifications;
      case MessageType.audio:
        return _settings.enableAudioNotifications;
      case MessageType.video:
        return _settings.enableVideoNotifications;
      case MessageType.file:
        return _settings.enableFileNotifications;
      default:
        return true;
    }
  }

  String _getNotificationBody(Message message) {
    switch (message.type) {
      case MessageType.text:
        return message.content.length > 50 
            ? '${message.content.substring(0, 50)}...'
            : message.content;
      case MessageType.image:
        return '[图片]';
      case MessageType.audio:
        return '[语音消息]';
      case MessageType.video:
        return '[视频]';
      case MessageType.file:
        return '[文件]';
      default:
        return '[消息]';
    }
  }

  Future<void> _showSystemNotification(AppNotification notification) async {
    // 模拟显示系统通知
    await Future.delayed(const Duration(milliseconds: 100));
    print('显示通知: ${notification.title} - ${notification.body}');
  }

  bool _isTimeInRange(TimeOfDay current, TimeOfDay start, TimeOfDay end) {
    final currentMinutes = current.hour * 60 + current.minute;
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;
    
    if (startMinutes <= endMinutes) {
      // 同一天内的时间段
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // 跨天的时间段
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }

  Future<void> _saveSettings() async {
    // 在实际应用中，这里会保存到本地存储
  }

  Future<void> _saveDoNotDisturbPeriods() async {
    // 在实际应用中，这里会保存到本地存储
  }

  void dispose() {
    _eventController.close();
  }
}

/// 通知设置
class NotificationSettings {
  final bool enableNotifications;
  final bool enableSoundNotifications;
  final bool enableVibrationNotifications;
  final bool enableTextNotifications;
  final bool enableImageNotifications;
  final bool enableAudioNotifications;
  final bool enableVideoNotifications;
  final bool enableFileNotifications;
  final bool enableFriendRequestNotifications;
  final bool enableGroupNotifications;
  final bool enableDoNotDisturb;
  final String notificationSound;

  NotificationSettings({
    this.enableNotifications = true,
    this.enableSoundNotifications = true,
    this.enableVibrationNotifications = true,
    this.enableTextNotifications = true,
    this.enableImageNotifications = true,
    this.enableAudioNotifications = true,
    this.enableVideoNotifications = true,
    this.enableFileNotifications = true,
    this.enableFriendRequestNotifications = true,
    this.enableGroupNotifications = true,
    this.enableDoNotDisturb = false,
    this.notificationSound = 'default',
  });

  NotificationSettings copyWith({
    bool? enableNotifications,
    bool? enableSoundNotifications,
    bool? enableVibrationNotifications,
    bool? enableTextNotifications,
    bool? enableImageNotifications,
    bool? enableAudioNotifications,
    bool? enableVideoNotifications,
    bool? enableFileNotifications,
    bool? enableFriendRequestNotifications,
    bool? enableGroupNotifications,
    bool? enableDoNotDisturb,
    String? notificationSound,
  }) {
    return NotificationSettings(
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableSoundNotifications: enableSoundNotifications ?? this.enableSoundNotifications,
      enableVibrationNotifications: enableVibrationNotifications ?? this.enableVibrationNotifications,
      enableTextNotifications: enableTextNotifications ?? this.enableTextNotifications,
      enableImageNotifications: enableImageNotifications ?? this.enableImageNotifications,
      enableAudioNotifications: enableAudioNotifications ?? this.enableAudioNotifications,
      enableVideoNotifications: enableVideoNotifications ?? this.enableVideoNotifications,
      enableFileNotifications: enableFileNotifications ?? this.enableFileNotifications,
      enableFriendRequestNotifications: enableFriendRequestNotifications ?? this.enableFriendRequestNotifications,
      enableGroupNotifications: enableGroupNotifications ?? this.enableGroupNotifications,
      enableDoNotDisturb: enableDoNotDisturb ?? this.enableDoNotDisturb,
      notificationSound: notificationSound ?? this.notificationSound,
    );
  }
}

/// 免打扰时间段
class DoNotDisturbPeriod {
  final String id;
  final String name;
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final List<int> weekdays; // 1-7, 1=Monday
  final bool isActive;

  DoNotDisturbPeriod({
    required this.id,
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.weekdays,
    this.isActive = true,
  });

  DoNotDisturbPeriod copyWith({
    String? id,
    String? name,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    List<int>? weekdays,
    bool? isActive,
  }) {
    return DoNotDisturbPeriod(
      id: id ?? this.id,
      name: name ?? this.name,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      weekdays: weekdays ?? this.weekdays,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// 应用通知
class AppNotification {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final Map<String, String> data;
  final DateTime createdAt;

  AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.createdAt,
  });
}

/// 通知类型
enum NotificationType {
  message,
  friendRequest,
  groupInvite,
  system,
}

/// 通知事件
class NotificationEvent {
  final NotificationEventType type;
  final AppNotification? notification;
  final NotificationSettings? settings;
  final List<DoNotDisturbPeriod>? doNotDisturbPeriods;
  final String? error;

  NotificationEvent({
    required this.type,
    this.notification,
    this.settings,
    this.doNotDisturbPeriods,
    this.error,
  });
}

/// 通知事件类型
enum NotificationEventType {
  notificationShown,
  notificationError,
  settingsUpdated,
  doNotDisturbUpdated,
  notificationsCleared,
}
