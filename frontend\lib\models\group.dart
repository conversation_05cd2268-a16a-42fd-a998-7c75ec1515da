class Group {
  final int id;
  final String name;
  final String? description;
  final String? avatar;
  final int ownerId;
  final List<GroupMember> members;
  final GroupSettings settings;
  final DateTime createdAt;
  final DateTime updatedAt;

  Group({
    required this.id,
    required this.name,
    this.description,
    this.avatar,
    required this.ownerId,
    required this.members,
    required this.settings,
    required this.createdAt,
    required this.updatedAt,
  });

  Group copyWith({
    int? id,
    String? name,
    String? description,
    String? avatar,
    int? ownerId,
    List<GroupMember>? members,
    GroupSettings? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Group(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      avatar: avatar ?? this.avatar,
      ownerId: ownerId ?? this.ownerId,
      members: members ?? this.members,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory Group.fromJson(Map<String, dynamic> json) {
    return Group(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      avatar: json['avatar'],
      ownerId: json['owner_id'],
      members: (json['members'] as List<dynamic>?)
          ?.map((m) => GroupMember.fromJson(m))
          .toList() ?? [],
      settings: GroupSettings.fromJson(json['settings'] ?? {}),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'avatar': avatar,
      'owner_id': ownerId,
      'members': members.map((m) => m.toJson()).toList(),
      'settings': settings.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // 获取群成员数量
  int get memberCount => members.length;

  // 检查用户是否是群主
  bool isOwner(int userId) => ownerId == userId;

  // 检查用户是否是管理员
  bool isAdmin(int userId) {
    final member = members.firstWhere(
      (m) => m.userId == userId,
      orElse: () => GroupMember(
        userId: userId,
        role: GroupRole.member,
        joinedAt: DateTime.now(),
      ),
    );
    return member.role == GroupRole.admin || isOwner(userId);
  }

  // 检查用户是否在群中
  bool isMember(int userId) {
    return members.any((m) => m.userId == userId);
  }

  // 获取活跃成员数量
  int get activeMemberCount {
    return members.where((m) => m.status == GroupMemberStatus.active).length;
  }
}

class GroupMember {
  final int userId;
  final String? nickname;
  final GroupRole role;
  final GroupMemberStatus status;
  final DateTime joinedAt;
  final DateTime? lastActiveAt;

  GroupMember({
    required this.userId,
    this.nickname,
    required this.role,
    this.status = GroupMemberStatus.active,
    required this.joinedAt,
    this.lastActiveAt,
  });

  GroupMember copyWith({
    int? userId,
    String? nickname,
    GroupRole? role,
    GroupMemberStatus? status,
    DateTime? joinedAt,
    DateTime? lastActiveAt,
  }) {
    return GroupMember(
      userId: userId ?? this.userId,
      nickname: nickname ?? this.nickname,
      role: role ?? this.role,
      status: status ?? this.status,
      joinedAt: joinedAt ?? this.joinedAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
    );
  }

  factory GroupMember.fromJson(Map<String, dynamic> json) {
    return GroupMember(
      userId: json['user_id'],
      nickname: json['nickname'],
      role: GroupRole.values.firstWhere(
        (r) => r.name == json['role'],
        orElse: () => GroupRole.member,
      ),
      status: GroupMemberStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => GroupMemberStatus.active,
      ),
      joinedAt: DateTime.parse(json['joined_at']),
      lastActiveAt: json['last_active_at'] != null
          ? DateTime.parse(json['last_active_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'nickname': nickname,
      'role': role.name,
      'status': status.name,
      'joined_at': joinedAt.toIso8601String(),
      'last_active_at': lastActiveAt?.toIso8601String(),
    };
  }
}

class GroupSettings {
  final bool allowMemberInvite;
  final bool allowMemberEdit;
  final bool muteNotifications;
  final GroupJoinPolicy joinPolicy;
  final int? maxMembers;

  GroupSettings({
    this.allowMemberInvite = true,
    this.allowMemberEdit = false,
    this.muteNotifications = false,
    this.joinPolicy = GroupJoinPolicy.inviteOnly,
    this.maxMembers,
  });

  GroupSettings copyWith({
    bool? allowMemberInvite,
    bool? allowMemberEdit,
    bool? muteNotifications,
    GroupJoinPolicy? joinPolicy,
    int? maxMembers,
  }) {
    return GroupSettings(
      allowMemberInvite: allowMemberInvite ?? this.allowMemberInvite,
      allowMemberEdit: allowMemberEdit ?? this.allowMemberEdit,
      muteNotifications: muteNotifications ?? this.muteNotifications,
      joinPolicy: joinPolicy ?? this.joinPolicy,
      maxMembers: maxMembers ?? this.maxMembers,
    );
  }

  factory GroupSettings.fromJson(Map<String, dynamic> json) {
    return GroupSettings(
      allowMemberInvite: json['allow_member_invite'] ?? true,
      allowMemberEdit: json['allow_member_edit'] ?? false,
      muteNotifications: json['mute_notifications'] ?? false,
      joinPolicy: GroupJoinPolicy.values.firstWhere(
        (p) => p.name == json['join_policy'],
        orElse: () => GroupJoinPolicy.inviteOnly,
      ),
      maxMembers: json['max_members'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allow_member_invite': allowMemberInvite,
      'allow_member_edit': allowMemberEdit,
      'mute_notifications': muteNotifications,
      'join_policy': joinPolicy.name,
      'max_members': maxMembers,
    };
  }
}

enum GroupRole {
  owner,
  admin,
  member,
}

enum GroupMemberStatus {
  active,
  muted,
  banned,
  left,
}

enum GroupJoinPolicy {
  open,        // 任何人都可以加入
  inviteOnly,  // 仅邀请
  requestOnly, // 需要申请
}
