import 'dart:async';
import 'dart:math';
import '../models/user.dart';

/// 视频通话服务
/// 处理视频通话、音频通话的建立、管理和控制
class VideoCallService {
  static final VideoCallService _instance = VideoCallService._internal();
  factory VideoCallService() => _instance;
  VideoCallService._internal();

  // 通话状态
  CallState _currentCallState = CallState.idle;
  CallSession? _currentSession;
  
  // 事件流
  final StreamController<CallEvent> _eventController = 
      StreamController<CallEvent>.broadcast();
  
  Stream<CallEvent> get eventStream => _eventController.stream;

  /// 获取当前通话状态
  CallState get currentCallState => _currentCallState;

  /// 获取当前通话会话
  CallSession? get currentSession => _currentSession;

  /// 发起视频通话
  Future<bool> startVideoCall(User targetUser) async {
    if (_currentCallState != CallState.idle) {
      return false;
    }

    try {
      _currentCallState = CallState.outgoing;
      _currentSession = CallSession(
        id: _generateSessionId(),
        caller: User(id: 1, username: 'current_user', email: '<EMAIL>'),
        callee: targetUser,
        type: CallType.video,
        startTime: DateTime.now(),
        status: CallStatus.connecting,
      );

      _eventController.add(CallEvent(
        type: CallEventType.callStarted,
        session: _currentSession!,
      ));

      // 模拟连接过程
      await Future.delayed(const Duration(seconds: 2));

      // 模拟对方接听或拒绝
      final isAccepted = Random().nextBool();
      
      if (isAccepted) {
        _currentCallState = CallState.connected;
        _currentSession = _currentSession!.copyWith(
          status: CallStatus.connected,
          connectedTime: DateTime.now(),
        );

        _eventController.add(CallEvent(
          type: CallEventType.callConnected,
          session: _currentSession!,
        ));

        return true;
      } else {
        _currentCallState = CallState.idle;
        _currentSession = _currentSession!.copyWith(
          status: CallStatus.rejected,
          endTime: DateTime.now(),
        );

        _eventController.add(CallEvent(
          type: CallEventType.callRejected,
          session: _currentSession!,
        ));

        _currentSession = null;
        return false;
      }
    } catch (e) {
      _currentCallState = CallState.idle;
      _currentSession = null;
      
      _eventController.add(CallEvent(
        type: CallEventType.callFailed,
        error: e.toString(),
      ));

      return false;
    }
  }

  /// 发起音频通话
  Future<bool> startAudioCall(User targetUser) async {
    if (_currentCallState != CallState.idle) {
      return false;
    }

    try {
      _currentCallState = CallState.outgoing;
      _currentSession = CallSession(
        id: _generateSessionId(),
        caller: User(id: 1, username: 'current_user', email: '<EMAIL>'),
        callee: targetUser,
        type: CallType.audio,
        startTime: DateTime.now(),
        status: CallStatus.connecting,
      );

      _eventController.add(CallEvent(
        type: CallEventType.callStarted,
        session: _currentSession!,
      ));

      // 模拟连接过程
      await Future.delayed(const Duration(seconds: 2));

      // 模拟对方接听或拒绝
      final isAccepted = Random().nextBool();
      
      if (isAccepted) {
        _currentCallState = CallState.connected;
        _currentSession = _currentSession!.copyWith(
          status: CallStatus.connected,
          connectedTime: DateTime.now(),
        );

        _eventController.add(CallEvent(
          type: CallEventType.callConnected,
          session: _currentSession!,
        ));

        return true;
      } else {
        _currentCallState = CallState.idle;
        _currentSession = _currentSession!.copyWith(
          status: CallStatus.rejected,
          endTime: DateTime.now(),
        );

        _eventController.add(CallEvent(
          type: CallEventType.callRejected,
          session: _currentSession!,
        ));

        _currentSession = null;
        return false;
      }
    } catch (e) {
      _currentCallState = CallState.idle;
      _currentSession = null;
      
      _eventController.add(CallEvent(
        type: CallEventType.callFailed,
        error: e.toString(),
      ));

      return false;
    }
  }

  /// 接听通话
  Future<void> acceptCall() async {
    if (_currentCallState != CallState.incoming || _currentSession == null) {
      return;
    }

    _currentCallState = CallState.connected;
    _currentSession = _currentSession!.copyWith(
      status: CallStatus.connected,
      connectedTime: DateTime.now(),
    );

    _eventController.add(CallEvent(
      type: CallEventType.callConnected,
      session: _currentSession!,
    ));
  }

  /// 拒绝通话
  Future<void> rejectCall() async {
    if (_currentCallState != CallState.incoming || _currentSession == null) {
      return;
    }

    _currentCallState = CallState.idle;
    _currentSession = _currentSession!.copyWith(
      status: CallStatus.rejected,
      endTime: DateTime.now(),
    );

    _eventController.add(CallEvent(
      type: CallEventType.callRejected,
      session: _currentSession!,
    ));

    _currentSession = null;
  }

  /// 结束通话
  Future<void> endCall() async {
    if (_currentSession == null) return;

    _currentCallState = CallState.idle;
    _currentSession = _currentSession!.copyWith(
      status: CallStatus.ended,
      endTime: DateTime.now(),
    );

    _eventController.add(CallEvent(
      type: CallEventType.callEnded,
      session: _currentSession!,
    ));

    _currentSession = null;
  }

  /// 切换麦克风状态
  Future<void> toggleMicrophone() async {
    if (_currentSession == null) return;

    _currentSession = _currentSession!.copyWith(
      isMicrophoneEnabled: !_currentSession!.isMicrophoneEnabled,
    );

    _eventController.add(CallEvent(
      type: CallEventType.microphoneToggled,
      session: _currentSession!,
    ));
  }

  /// 切换摄像头状态
  Future<void> toggleCamera() async {
    if (_currentSession == null) return;

    _currentSession = _currentSession!.copyWith(
      isCameraEnabled: !_currentSession!.isCameraEnabled,
    );

    _eventController.add(CallEvent(
      type: CallEventType.cameraToggled,
      session: _currentSession!,
    ));
  }

  /// 切换扬声器状态
  Future<void> toggleSpeaker() async {
    if (_currentSession == null) return;

    _currentSession = _currentSession!.copyWith(
      isSpeakerEnabled: !_currentSession!.isSpeakerEnabled,
    );

    _eventController.add(CallEvent(
      type: CallEventType.speakerToggled,
      session: _currentSession!,
    ));
  }

  /// 切换前后摄像头
  Future<void> switchCamera() async {
    if (_currentSession == null) return;

    _currentSession = _currentSession!.copyWith(
      isFrontCamera: !_currentSession!.isFrontCamera,
    );

    _eventController.add(CallEvent(
      type: CallEventType.cameraSwitched,
      session: _currentSession!,
    ));
  }

  /// 模拟接收来电
  void simulateIncomingCall(User caller, CallType type) {
    if (_currentCallState != CallState.idle) return;

    _currentCallState = CallState.incoming;
    _currentSession = CallSession(
      id: _generateSessionId(),
      caller: caller,
      callee: User(id: 1, username: 'current_user', email: '<EMAIL>'),
      type: type,
      startTime: DateTime.now(),
      status: CallStatus.ringing,
    );

    _eventController.add(CallEvent(
      type: CallEventType.incomingCall,
      session: _currentSession!,
    ));
  }

  String _generateSessionId() {
    return 'call_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  void dispose() {
    _eventController.close();
  }
}

/// 通话状态
enum CallState {
  idle,
  outgoing,
  incoming,
  connected,
}

/// 通话类型
enum CallType {
  audio,
  video,
}

/// 通话状态
enum CallStatus {
  connecting,
  ringing,
  connected,
  rejected,
  ended,
  failed,
}

/// 通话会话
class CallSession {
  final String id;
  final User caller;
  final User callee;
  final CallType type;
  final DateTime startTime;
  final DateTime? connectedTime;
  final DateTime? endTime;
  final CallStatus status;
  final bool isMicrophoneEnabled;
  final bool isCameraEnabled;
  final bool isSpeakerEnabled;
  final bool isFrontCamera;

  CallSession({
    required this.id,
    required this.caller,
    required this.callee,
    required this.type,
    required this.startTime,
    this.connectedTime,
    this.endTime,
    required this.status,
    this.isMicrophoneEnabled = true,
    this.isCameraEnabled = true,
    this.isSpeakerEnabled = false,
    this.isFrontCamera = true,
  });

  CallSession copyWith({
    String? id,
    User? caller,
    User? callee,
    CallType? type,
    DateTime? startTime,
    DateTime? connectedTime,
    DateTime? endTime,
    CallStatus? status,
    bool? isMicrophoneEnabled,
    bool? isCameraEnabled,
    bool? isSpeakerEnabled,
    bool? isFrontCamera,
  }) {
    return CallSession(
      id: id ?? this.id,
      caller: caller ?? this.caller,
      callee: callee ?? this.callee,
      type: type ?? this.type,
      startTime: startTime ?? this.startTime,
      connectedTime: connectedTime ?? this.connectedTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      isMicrophoneEnabled: isMicrophoneEnabled ?? this.isMicrophoneEnabled,
      isCameraEnabled: isCameraEnabled ?? this.isCameraEnabled,
      isSpeakerEnabled: isSpeakerEnabled ?? this.isSpeakerEnabled,
      isFrontCamera: isFrontCamera ?? this.isFrontCamera,
    );
  }

  /// 获取通话时长
  Duration get duration {
    if (connectedTime == null) return Duration.zero;
    final endTime = this.endTime ?? DateTime.now();
    return endTime.difference(connectedTime!);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'caller': caller.toJson(),
      'callee': callee.toJson(),
      'type': type.index,
      'startTime': startTime.toIso8601String(),
      'connectedTime': connectedTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'status': status.index,
      'isMicrophoneEnabled': isMicrophoneEnabled,
      'isCameraEnabled': isCameraEnabled,
      'isSpeakerEnabled': isSpeakerEnabled,
      'isFrontCamera': isFrontCamera,
    };
  }
}

/// 通话事件
class CallEvent {
  final CallEventType type;
  final CallSession? session;
  final String? error;

  CallEvent({
    required this.type,
    this.session,
    this.error,
  });
}

/// 通话事件类型
enum CallEventType {
  callStarted,
  incomingCall,
  callConnected,
  callRejected,
  callEnded,
  callFailed,
  microphoneToggled,
  cameraToggled,
  speakerToggled,
  cameraSwitched,
}
