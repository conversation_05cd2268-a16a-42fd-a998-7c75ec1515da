import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../models/user.dart';
import '../../models/group.dart';
import '../../widgets/user_avatar.dart';
import '../../widgets/loading_animation.dart';
import '../../widgets/slide_animation.dart';
import '../../services/mock_api_service.dart';

class CreateGroupScreen extends StatefulWidget {
  const CreateGroupScreen({super.key});

  @override
  State<CreateGroupScreen> createState() => _CreateGroupScreenState();
}

class _CreateGroupScreenState extends State<CreateGroupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final MockApiService _mockApi = MockApiService();
  
  bool _isLoading = false;
  String? _groupAvatar;
  List<User> _friends = [];
  List<User> _selectedMembers = [];
  GroupJoinPolicy _joinPolicy = GroupJoinPolicy.inviteOnly;
  bool _allowMemberInvite = true;
  int? _maxMembers;

  @override
  void initState() {
    super.initState();
    _loadFriends();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadFriends() async {
    try {
      // 模拟加载好友列表
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        _friends = [
          User(
            id: 2,
            username: 'alice_wonder',
            email: '<EMAIL>',
            nickname: 'Alice Wonder',
            avatar: '',
            status: UserStatus.online,
            createdAt: DateTime.now().subtract(const Duration(days: 30)),
            updatedAt: DateTime.now(),
          ),
          User(
            id: 3,
            username: 'bob_builder',
            email: '<EMAIL>',
            nickname: 'Bob Builder',
            avatar: '',
            status: UserStatus.away,
            createdAt: DateTime.now().subtract(const Duration(days: 25)),
            updatedAt: DateTime.now(),
          ),
          User(
            id: 4,
            username: 'charlie_brown',
            email: '<EMAIL>',
            nickname: 'Charlie Brown',
            avatar: '',
            status: UserStatus.offline,
            createdAt: DateTime.now().subtract(const Duration(days: 20)),
            updatedAt: DateTime.now(),
          ),
        ];
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载好友列表失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _pickGroupAvatar() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _groupAvatar = image.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('选择群头像失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _createGroup() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedMembers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请至少选择一个群成员'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser!;

      // 模拟创建群聊
      await Future.delayed(const Duration(seconds: 2));

      // 创建群成员列表（包括群主）
      final members = [
        GroupMember(
          userId: currentUser.id,
          nickname: currentUser.nickname,
          role: GroupRole.owner,
          joinedAt: DateTime.now(),
        ),
        ..._selectedMembers.map((user) => GroupMember(
          userId: user.id,
          nickname: user.nickname,
          role: GroupRole.member,
          joinedAt: DateTime.now(),
        )),
      ];

      final group = Group(
        id: DateTime.now().millisecondsSinceEpoch,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        avatar: _groupAvatar,
        ownerId: currentUser.id,
        members: members,
        settings: GroupSettings(
          allowMemberInvite: _allowMemberInvite,
          joinPolicy: _joinPolicy,
          maxMembers: _maxMembers,
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('群聊 "${group.name}" 创建成功'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
        
        // 返回到上一页并传递创建的群聊信息
        Navigator.of(context).pop(group);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('创建群聊失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('创建群聊'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _createGroup,
              child: const Text(
                '创建',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 群头像
            SlideInAnimation(
              begin: const Offset(0.0, -1.0),
              child: Center(
                child: Stack(
                  children: [
                    GestureDetector(
                      onTap: _pickGroupAvatar,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppConstants.primaryColor,
                            width: 3,
                          ),
                          color: Colors.grey[200],
                        ),
                        child: ClipOval(
                          child: _groupAvatar != null
                              ? Image.asset(
                                  _groupAvatar!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.group,
                                      size: 50,
                                      color: Colors.grey,
                                    );
                                  },
                                )
                              : const Icon(
                                  Icons.group,
                                  size: 50,
                                  color: Colors.grey,
                                ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: AppConstants.primaryColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            Center(
              child: TextButton(
                onPressed: _pickGroupAvatar,
                child: const Text(
                  '设置群头像',
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 群名称
            SlideInAnimation(
              begin: const Offset(-1.0, 0.0),
              child: TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: '群名称',
                  prefixIcon: Icon(Icons.group),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入群名称';
                  }
                  if (value.trim().length < 2) {
                    return '群名称至少需要2个字符';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 16),

            // 群描述
            SlideInAnimation(
              begin: const Offset(1.0, 0.0),
              child: TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: '群描述（可选）',
                  prefixIcon: Icon(Icons.description),
                  border: OutlineInputBorder(),
                  hintText: '介绍一下这个群聊...',
                ),
                maxLines: 3,
                maxLength: 200,
              ),
            ),
            const SizedBox(height: 24),

            // 选择群成员
            SlideInAnimation(
              begin: const Offset(0.0, 1.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.people, color: AppConstants.primaryColor),
                          const SizedBox(width: 8),
                          Text(
                            '选择群成员 (${_selectedMembers.length}/${_friends.length})',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_friends.isEmpty)
                        const Center(
                          child: Text(
                            '暂无好友可添加',
                            style: TextStyle(color: Colors.grey),
                          ),
                        )
                      else
                        ...(_friends.map((friend) => ListTile(
                          leading: UserAvatar(user: friend, radius: 20),
                          title: Text(friend.nickname ?? friend.username),
                          subtitle: Text('@${friend.username}'),
                          trailing: Checkbox(
                            value: _selectedMembers.contains(friend),
                            onChanged: (bool? value) {
                              setState(() {
                                if (value == true) {
                                  _selectedMembers.add(friend);
                                } else {
                                  _selectedMembers.remove(friend);
                                }
                              });
                            },
                            activeColor: AppConstants.primaryColor,
                          ),
                          onTap: () {
                            setState(() {
                              if (_selectedMembers.contains(friend)) {
                                _selectedMembers.remove(friend);
                              } else {
                                _selectedMembers.add(friend);
                              }
                            });
                          },
                        ))),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 群设置
            SlideInAnimation(
              begin: const Offset(0.0, 1.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.settings, color: AppConstants.primaryColor),
                          SizedBox(width: 8),
                          Text(
                            '群设置',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // 允许成员邀请
                      SwitchListTile(
                        title: const Text('允许成员邀请其他人'),
                        subtitle: const Text('群成员可以邀请新成员加入'),
                        value: _allowMemberInvite,
                        onChanged: (value) {
                          setState(() {
                            _allowMemberInvite = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                      ),

                      // 加入方式
                      ListTile(
                        title: const Text('加入方式'),
                        subtitle: Text(_getJoinPolicyText(_joinPolicy)),
                        trailing: DropdownButton<GroupJoinPolicy>(
                          value: _joinPolicy,
                          onChanged: (GroupJoinPolicy? newValue) {
                            if (newValue != null) {
                              setState(() {
                                _joinPolicy = newValue;
                              });
                            }
                          },
                          items: GroupJoinPolicy.values.map((policy) {
                            return DropdownMenuItem<GroupJoinPolicy>(
                              value: policy,
                              child: Text(_getJoinPolicyText(policy)),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getJoinPolicyText(GroupJoinPolicy policy) {
    switch (policy) {
      case GroupJoinPolicy.open:
        return '公开群聊';
      case GroupJoinPolicy.inviteOnly:
        return '仅邀请';
      case GroupJoinPolicy.requestOnly:
        return '需要申请';
    }
  }
}
