import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../models/user.dart';
import '../../models/group.dart';
import '../../models/message.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/user_avatar.dart';
import '../../widgets/loading_animation.dart';
import '../../widgets/slide_animation.dart';
import '../group/group_chat_screen.dart';
import 'message_search_screen.dart';

class GlobalSearchScreen extends StatefulWidget {
  const GlobalSearchScreen({super.key});

  @override
  State<GlobalSearchScreen> createState() => _GlobalSearchScreenState();
}

class _GlobalSearchScreenState extends State<GlobalSearchScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;
  
  List<User> _userResults = [];
  List<Group> _groupResults = [];
  List<Message> _messageResults = [];
  bool _isLoading = false;
  bool _hasSearched = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _userResults = [];
        _groupResults = [];
        _messageResults = [];
        _hasSearched = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _currentQuery = query.trim();
    });

    try {
      // 模拟搜索延迟
      await Future.delayed(const Duration(milliseconds: 800));
      
      // 并行搜索用户、群聊和消息
      final results = await Future.wait([
        _searchUsers(query.trim()),
        _searchGroups(query.trim()),
        _searchMessages(query.trim()),
      ]);

      setState(() {
        _userResults = results[0] as List<User>;
        _groupResults = results[1] as List<Group>;
        _messageResults = results[2] as List<Message>;
        _hasSearched = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasSearched = true;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('搜索失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<List<User>> _searchUsers(String query) async {
    // 模拟用户搜索
    final allUsers = [
      User(
        id: 2,
        username: 'alice_wonder',
        email: '<EMAIL>',
        nickname: 'Alice Wonder',
        avatar: '',
        status: UserStatus.online,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      User(
        id: 3,
        username: 'bob_builder',
        email: '<EMAIL>',
        nickname: 'Bob Builder',
        avatar: '',
        status: UserStatus.away,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      User(
        id: 4,
        username: 'charlie_brown',
        email: '<EMAIL>',
        nickname: 'Charlie Brown',
        avatar: '',
        status: UserStatus.offline,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    return allUsers.where((user) {
      final lowerQuery = query.toLowerCase();
      return user.username.toLowerCase().contains(lowerQuery) ||
             (user.nickname?.toLowerCase().contains(lowerQuery) ?? false) ||
             user.email.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  Future<List<Group>> _searchGroups(String query) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser!;
    
    // 模拟群聊搜索
    final allGroups = [
      Group(
        id: 1,
        name: '工作讨论组',
        description: '日常工作交流和讨论',
        avatar: null,
        ownerId: currentUser.id,
        members: [
          GroupMember(
            userId: currentUser.id,
            nickname: currentUser.nickname,
            role: GroupRole.owner,
            joinedAt: DateTime.now(),
          ),
        ],
        settings: GroupSettings(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Group(
        id: 2,
        name: '朋友聚会',
        description: '周末聚会安排',
        avatar: null,
        ownerId: 2,
        members: [
          GroupMember(
            userId: currentUser.id,
            nickname: currentUser.nickname,
            role: GroupRole.member,
            joinedAt: DateTime.now(),
          ),
        ],
        settings: GroupSettings(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    return allGroups.where((group) {
      final lowerQuery = query.toLowerCase();
      return group.name.toLowerCase().contains(lowerQuery) ||
             (group.description?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  Future<List<Message>> _searchMessages(String query) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser!;
    
    // 模拟消息搜索（简化版）
    final mockResults = <Message>[];
    
    if (query.contains('工作') || query.contains('项目')) {
      mockResults.add(
        Message(
          id: 1,
          senderId: 2,
          receiverId: currentUser.id,
          content: '今天的工作安排已经发给你了，请查收',
          type: MessageType.text,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
          sender: _getMockUser(2),
          receiver: currentUser,
          isRead: true,
        ),
      );
    }
    
    return mockResults;
  }

  User _getMockUser(int id) {
    return User(
      id: id,
      username: 'user$id',
      email: 'user$<EMAIL>',
      nickname: 'User $id',
      avatar: '',
      status: UserStatus.online,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _userResults = [];
      _groupResults = [];
      _messageResults = [];
      _hasSearched = false;
      _currentQuery = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('全局搜索'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(110),
          child: Column(
            children: [
              // 搜索框
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: '搜索用户、群聊、消息...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: _clearSearch,
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                  onSubmitted: _performSearch,
                  textInputAction: TextInputAction.search,
                ),
              ),
              
              // 标签栏
              if (_hasSearched)
                TabBar(
                  controller: _tabController,
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white70,
                  indicatorColor: Colors.white,
                  tabs: [
                    Tab(text: '用户 (${_userResults.length})'),
                    Tab(text: '群聊 (${_groupResults.length})'),
                    Tab(text: '消息 (${_messageResults.length})'),
                  ],
                ),
            ],
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingAnimation(
          size: 60,
          message: '搜索中...',
        ),
      );
    }

    if (!_hasSearched) {
      return _buildSearchSuggestions();
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildUserResults(),
        _buildGroupResults(),
        _buildMessageResults(),
      ],
    );
  }

  Widget _buildSearchSuggestions() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '全局搜索',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '搜索用户、群聊和消息',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserResults() {
    if (_userResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_search, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('未找到相关用户', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _userResults.length,
      itemBuilder: (context, index) {
        final user = _userResults[index];
        return ListItemAnimation(
          index: index,
          child: ListTile(
            leading: UserAvatar(user: user, radius: 20),
            title: Text(user.nickname ?? user.username),
            subtitle: Text('@${user.username}'),
            trailing: const Icon(Icons.chat_bubble_outline),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('点击与 ${user.nickname ?? user.username} 聊天'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildGroupResults() {
    if (_groupResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.group_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('未找到相关群聊', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _groupResults.length,
      itemBuilder: (context, index) {
        final group = _groupResults[index];
        return ListItemAnimation(
          index: index,
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppConstants.primaryColor.withValues(alpha: 0.1),
              ),
              child: const Icon(Icons.group, color: AppConstants.primaryColor),
            ),
            title: Text(group.name),
            subtitle: Text(group.description ?? '${group.memberCount} 名成员'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => GroupChatScreen(group: group),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildMessageResults() {
    if (_messageResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.message_outlined, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('未找到相关消息', style: TextStyle(color: Colors.grey)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const MessageSearchScreen(),
                  ),
                );
              },
              child: const Text('详细搜索消息'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _messageResults.length,
      itemBuilder: (context, index) {
        final message = _messageResults[index];
        return ListItemAnimation(
          index: index,
          child: ListTile(
            leading: UserAvatar(user: message.sender, radius: 20),
            title: Text(message.sender.nickname ?? message.sender.username),
            subtitle: Text(
              message.content,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Text(
              _formatTime(message.createdAt),
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('点击与 ${message.sender.nickname ?? message.sender.username} 聊天'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        );
      },
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${dateTime.month}/${dateTime.day}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
