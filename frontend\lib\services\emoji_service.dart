import 'dart:async';

/// 表情包服务
/// 处理表情包、贴纸、自定义表情等功能
class EmojiService {
  static final EmojiService _instance = EmojiService._internal();
  factory EmojiService() => _instance;
  EmojiService._internal();

  // 表情包数据
  final List<EmojiCategory> _categories = [];
  final List<CustomEmoji> _customEmojis = [];
  final List<String> _recentEmojis = [];
  
  // 事件流
  final StreamController<EmojiEvent> _eventController = 
      StreamController<EmojiEvent>.broadcast();
  
  Stream<EmojiEvent> get eventStream => _eventController.stream;

  /// 初始化表情包数据
  Future<void> initialize() async {
    await _loadDefaultEmojis();
    await _loadCustomEmojis();
    await _loadRecentEmojis();
  }

  /// 获取表情包分类
  List<EmojiCategory> getCategories() {
    return List.unmodifiable(_categories);
  }

  /// 获取自定义表情包
  List<CustomEmoji> getCustomEmojis() {
    return List.unmodifiable(_customEmojis);
  }

  /// 获取最近使用的表情
  List<String> getRecentEmojis() {
    return List.unmodifiable(_recentEmojis);
  }

  /// 添加表情到最近使用
  void addToRecent(String emoji) {
    _recentEmojis.remove(emoji);
    _recentEmojis.insert(0, emoji);
    
    // 限制最近使用的数量
    if (_recentEmojis.length > 30) {
      _recentEmojis.removeLast();
    }
    
    _saveRecentEmojis();
    
    _eventController.add(EmojiEvent(
      type: EmojiEventType.recentUpdated,
      emoji: emoji,
    ));
  }

  /// 搜索表情包
  List<String> searchEmojis(String query) {
    if (query.isEmpty) return [];
    
    final results = <String>[];
    final lowerQuery = query.toLowerCase();
    
    // 搜索默认表情
    for (final category in _categories) {
      for (final emoji in category.emojis) {
        if (emoji.keywords.any((keyword) => keyword.toLowerCase().contains(lowerQuery)) ||
            emoji.name.toLowerCase().contains(lowerQuery)) {
          results.add(emoji.emoji);
        }
      }
    }
    
    // 搜索自定义表情
    for (final customEmoji in _customEmojis) {
      if (customEmoji.name.toLowerCase().contains(lowerQuery) ||
          customEmoji.tags.any((tag) => tag.toLowerCase().contains(lowerQuery))) {
        results.add(customEmoji.url);
      }
    }
    
    return results;
  }

  /// 添加自定义表情包
  Future<bool> addCustomEmoji(String name, String url, List<String> tags) async {
    try {
      final customEmoji = CustomEmoji(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        url: url,
        tags: tags,
        createdAt: DateTime.now(),
      );
      
      _customEmojis.add(customEmoji);
      await _saveCustomEmojis();
      
      _eventController.add(EmojiEvent(
        type: EmojiEventType.customEmojiAdded,
        customEmoji: customEmoji,
      ));
      
      return true;
    } catch (e) {
      _eventController.add(EmojiEvent(
        type: EmojiEventType.error,
        error: e.toString(),
      ));
      return false;
    }
  }

  /// 删除自定义表情包
  Future<bool> removeCustomEmoji(String id) async {
    try {
      final index = _customEmojis.indexWhere((emoji) => emoji.id == id);
      if (index == -1) return false;
      
      final removedEmoji = _customEmojis.removeAt(index);
      await _saveCustomEmojis();
      
      _eventController.add(EmojiEvent(
        type: EmojiEventType.customEmojiRemoved,
        customEmoji: removedEmoji,
      ));
      
      return true;
    } catch (e) {
      _eventController.add(EmojiEvent(
        type: EmojiEventType.error,
        error: e.toString(),
      ));
      return false;
    }
  }

  /// 加载默认表情包
  Future<void> _loadDefaultEmojis() async {
    _categories.clear();
    
    // 笑脸表情
    _categories.add(EmojiCategory(
      name: '笑脸',
      icon: '😀',
      emojis: [
        EmojiData('😀', '开心', ['开心', '笑', '高兴']),
        EmojiData('😃', '大笑', ['大笑', '开心', '兴奋']),
        EmojiData('😄', '笑眯眯', ['笑眯眯', '开心', '愉快']),
        EmojiData('😁', '露齿笑', ['露齿笑', '开心', '兴奋']),
        EmojiData('😆', '哈哈', ['哈哈', '大笑', '开心']),
        EmojiData('😅', '苦笑', ['苦笑', '尴尬', '汗']),
        EmojiData('🤣', '笑哭', ['笑哭', '大笑', '搞笑']),
        EmojiData('😂', '喜极而泣', ['喜极而泣', '笑哭', '开心']),
        EmojiData('🙂', '微笑', ['微笑', '开心', '友好']),
        EmojiData('🙃', '倒脸', ['倒脸', '调皮', '搞怪']),
        EmojiData('😉', '眨眼', ['眨眼', '调皮', '暗示']),
        EmojiData('😊', '害羞', ['害羞', '开心', '温柔']),
      ],
    ));
    
    // 爱心表情
    _categories.add(EmojiCategory(
      name: '爱心',
      icon: '❤️',
      emojis: [
        EmojiData('❤️', '红心', ['爱', '心', '喜欢']),
        EmojiData('🧡', '橙心', ['爱', '心', '温暖']),
        EmojiData('💛', '黄心', ['爱', '心', '友谊']),
        EmojiData('💚', '绿心', ['爱', '心', '自然']),
        EmojiData('💙', '蓝心', ['爱', '心', '信任']),
        EmojiData('💜', '紫心', ['爱', '心', '神秘']),
        EmojiData('🖤', '黑心', ['爱', '心', '酷']),
        EmojiData('🤍', '白心', ['爱', '心', '纯洁']),
        EmojiData('🤎', '棕心', ['爱', '心', '稳重']),
        EmojiData('💔', '心碎', ['心碎', '伤心', '分手']),
        EmojiData('❣️', '感叹心', ['爱', '心', '强调']),
        EmojiData('💕', '两颗心', ['爱', '心', '恋爱']),
      ],
    ));
    
    // 手势表情
    _categories.add(EmojiCategory(
      name: '手势',
      icon: '👍',
      emojis: [
        EmojiData('👍', '点赞', ['点赞', '好', '赞同']),
        EmojiData('👎', '点踩', ['点踩', '不好', '反对']),
        EmojiData('👌', 'OK', ['OK', '好的', '完美']),
        EmojiData('✌️', '胜利', ['胜利', '和平', '耶']),
        EmojiData('🤞', '祈祷', ['祈祷', '希望', '好运']),
        EmojiData('🤟', '爱你', ['爱你', '手语', '爱']),
        EmojiData('🤘', '摇滚', ['摇滚', '酷', '音乐']),
        EmojiData('🤙', '打电话', ['打电话', '联系', '电话']),
        EmojiData('👈', '左指', ['左', '指向', '那边']),
        EmojiData('👉', '右指', ['右', '指向', '这边']),
        EmojiData('👆', '上指', ['上', '指向', '上面']),
        EmojiData('👇', '下指', ['下', '指向', '下面']),
      ],
    ));
  }

  /// 加载自定义表情包
  Future<void> _loadCustomEmojis() async {
    // 模拟加载自定义表情包
    _customEmojis.clear();
    
    // 添加一些示例自定义表情
    _customEmojis.addAll([
      CustomEmoji(
        id: '1',
        name: '可爱猫咪',
        url: 'https://example.com/cat1.gif',
        tags: ['猫', '可爱', '动物'],
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      CustomEmoji(
        id: '2',
        name: '搞笑狗狗',
        url: 'https://example.com/dog1.gif',
        tags: ['狗', '搞笑', '动物'],
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ]);
  }

  /// 加载最近使用的表情
  Future<void> _loadRecentEmojis() async {
    // 模拟加载最近使用的表情
    _recentEmojis.clear();
    _recentEmojis.addAll(['😀', '❤️', '👍', '😂', '🤣', '😊']);
  }

  /// 保存自定义表情包
  Future<void> _saveCustomEmojis() async {
    // 在实际应用中，这里会保存到本地存储或服务器
  }

  /// 保存最近使用的表情
  Future<void> _saveRecentEmojis() async {
    // 在实际应用中，这里会保存到本地存储
  }

  void dispose() {
    _eventController.close();
  }
}

/// 表情包分类
class EmojiCategory {
  final String name;
  final String icon;
  final List<EmojiData> emojis;

  EmojiCategory({
    required this.name,
    required this.icon,
    required this.emojis,
  });
}

/// 表情数据
class EmojiData {
  final String emoji;
  final String name;
  final List<String> keywords;

  EmojiData(this.emoji, this.name, this.keywords);
}

/// 自定义表情包
class CustomEmoji {
  final String id;
  final String name;
  final String url;
  final List<String> tags;
  final DateTime createdAt;

  CustomEmoji({
    required this.id,
    required this.name,
    required this.url,
    required this.tags,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory CustomEmoji.fromJson(Map<String, dynamic> json) {
    return CustomEmoji(
      id: json['id'],
      name: json['name'],
      url: json['url'],
      tags: List<String>.from(json['tags']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// 表情包事件
class EmojiEvent {
  final EmojiEventType type;
  final String? emoji;
  final CustomEmoji? customEmoji;
  final String? error;

  EmojiEvent({
    required this.type,
    this.emoji,
    this.customEmoji,
    this.error,
  });
}

/// 表情包事件类型
enum EmojiEventType {
  recentUpdated,
  customEmojiAdded,
  customEmojiRemoved,
  error,
}
