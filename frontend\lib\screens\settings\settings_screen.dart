import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/slide_animation.dart';
import 'encryption_settings_screen.dart';
import 'notification_settings_screen.dart';
import 'theme_settings_screen.dart';
import 'privacy_settings_screen.dart';
import 'account_settings_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 用户信息卡片
          if (currentUser != null)
            SlideInAnimation(
              begin: const Offset(0.0, -1.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppConstants.primaryColor,
                        child: currentUser.avatar != null
                            ? ClipOval(
                                child: Image.network(
                                  currentUser.avatar!,
                                  width: 60,
                                  height: 60,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.person,
                                      color: Colors.white,
                                      size: 30,
                                    );
                                  },
                                ),
                              )
                            : const Icon(
                                Icons.person,
                                color: Colors.white,
                                size: 30,
                              ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              currentUser.nickname ?? currentUser.username,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              currentUser.email,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Icon(Icons.arrow_forward_ios, size: 16),
                    ],
                  ),
                ),
              ),
            ),
          const SizedBox(height: 16),

          // 通用设置
          SlideInAnimation(
            begin: const Offset(-1.0, 0.0),
            child: _buildSettingsSection(
              context,
              '通用',
              [
                _buildSettingsItem(
                  context,
                  icon: Icons.notifications,
                  title: '通知设置',
                  subtitle: '消息通知、提醒设置',
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const NotificationSettingsScreen(),
                      ),
                    );
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.palette,
                  title: '主题设置',
                  subtitle: '外观、主题、字体设置',
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ThemeSettingsScreen(),
                      ),
                    );
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.language,
                  title: '语言设置',
                  subtitle: '界面语言',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('语言设置功能开发中'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // 隐私与安全
          SlideInAnimation(
            begin: const Offset(1.0, 0.0),
            child: _buildSettingsSection(
              context,
              '隐私与安全',
              [
                _buildSettingsItem(
                  context,
                  icon: Icons.security,
                  title: '加密设置',
                  subtitle: '端到端加密、密钥管理',
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const EncryptionSettingsScreen(),
                      ),
                    );
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.privacy_tip,
                  title: '隐私设置',
                  subtitle: '在线状态、最后上线时间',
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PrivacySettingsScreen(),
                      ),
                    );
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.block,
                  title: '黑名单',
                  subtitle: '已屏蔽的用户',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('黑名单功能开发中'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // 账户管理
          SlideInAnimation(
            begin: const Offset(-1.0, 0.0),
            child: _buildSettingsSection(
              context,
              '账户',
              [
                _buildSettingsItem(
                  context,
                  icon: Icons.account_circle,
                  title: '账户设置',
                  subtitle: '密码、邮箱、手机号',
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AccountSettingsScreen(),
                      ),
                    );
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.storage,
                  title: '存储管理',
                  subtitle: '聊天记录、缓存清理',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('存储管理功能开发中'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.backup,
                  title: '数据备份',
                  subtitle: '聊天记录备份与恢复',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('数据备份功能开发中'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // 关于
          SlideInAnimation(
            begin: const Offset(1.0, 0.0),
            child: _buildSettingsSection(
              context,
              '关于',
              [
                _buildSettingsItem(
                  context,
                  icon: Icons.help,
                  title: '帮助与反馈',
                  subtitle: '使用帮助、问题反馈',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('帮助与反馈功能开发中'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.info,
                  title: '关于AqiChat',
                  subtitle: '版本信息、开发者信息',
                  onTap: () {
                    _showAboutDialog(context);
                  },
                ),
                _buildSettingsItem(
                  context,
                  icon: Icons.description,
                  title: '用户协议',
                  subtitle: '服务条款、隐私政策',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('用户协议功能开发中'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // 退出登录
          SlideInAnimation(
            begin: const Offset(0.0, 1.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _showLogoutDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  '退出登录',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(BuildContext context, String title, List<Widget> items) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  Widget _buildSettingsItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppConstants.primaryColor),
      title: Text(title),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于AqiChat'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('AqiChat v1.0.0'),
            SizedBox(height: 8),
            Text('一个现代化的即时通讯应用'),
            SizedBox(height: 16),
            Text('功能特性：'),
            Text('• 实时消息传输'),
            Text('• 端到端加密'),
            Text('• 多媒体消息支持'),
            Text('• 群聊功能'),
            Text('• 视频通话'),
            Text('• 屏幕共享'),
            SizedBox(height: 16),
            Text('开发者：AqiChat Team'),
            Text('© 2024 AqiChat. All rights reserved.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<AuthProvider>(context, listen: false).logout();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('退出'),
          ),
        ],
      ),
    );
  }
}
