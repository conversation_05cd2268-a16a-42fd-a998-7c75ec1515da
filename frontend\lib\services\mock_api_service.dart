import 'dart:async';
import 'dart:math';
import '../models/user.dart';
import '../models/message.dart';

// 模拟API服务，用于演示和开发
class MockApiService {
  static final MockApiService _instance = MockApiService._internal();
  factory MockApiService() => _instance;
  MockApiService._internal();

  // 模拟用户数据
  final List<User> _users = [
    User(
      id: 1,
      username: 'alice',
      email: '<EMAIL>',
      nickname: 'Alice',
      status: UserStatus.online,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    ),
    User(
      id: 2,
      username: 'bob',
      email: '<EMAIL>',
      nickname: '<PERSON>',
      status: UserStatus.away,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now(),
    ),
    User(
      id: 3,
      username: 'charlie',
      email: '<EMAIL>',
      nickname: '<PERSON>',
      status: UserStatus.offline,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now(),
    ),
  ];

  // 模拟消息数据
  final List<Message> _messages = [];
  
  // 模拟会话数据
  final List<Conversation> _conversations = [];

  User? _currentUser;
  String? _accessToken;

  // 模拟登录
  Future<LoginResponse> login(String username, String password) async {
    await Future.delayed(const Duration(seconds: 1)); // 模拟网络延迟

    // 简单的用户验证
    User? user;
    if (username == 'demo' && password == '123456') {
      user = User(
        id: 999,
        username: 'demo',
        email: '<EMAIL>',
        nickname: 'Demo User',
        status: UserStatus.online,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now(),
      );
    } else {
      user = _users.firstWhere(
        (u) => u.username == username,
        orElse: () => throw Exception('用户不存在'),
      );
    }

    _currentUser = user;
    _accessToken = 'mock_token_${user.id}_${DateTime.now().millisecondsSinceEpoch}';

    // 初始化模拟数据
    _initializeMockData();
    _initializeFriendRequests();

    return LoginResponse(
      user: user,
      accessToken: _accessToken!,
      refreshToken: 'mock_refresh_token',
    );
  }

  // 模拟注册
  Future<LoginResponse> register(RegisterRequest request) async {
    await Future.delayed(const Duration(seconds: 1));

    // 检查用户名是否已存在
    if (_users.any((u) => u.username == request.username)) {
      throw Exception('用户名已存在');
    }

    final user = User(
      id: _users.length + 1000,
      username: request.username,
      email: request.email,
      nickname: request.nickname ?? request.username,
      status: UserStatus.online,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _users.add(user);
    _currentUser = user;
    _accessToken = 'mock_token_${user.id}_${DateTime.now().millisecondsSinceEpoch}';

    _initializeMockData();

    return LoginResponse(
      user: user,
      accessToken: _accessToken!,
      refreshToken: 'mock_refresh_token',
    );
  }

  // 获取用户资料
  Future<User> getProfile() async {
    await Future.delayed(const Duration(milliseconds: 500));
    if (_currentUser == null) throw Exception('未登录');
    return _currentUser!;
  }

  // 获取会话列表
  Future<List<Conversation>> getConversations() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return List.from(_conversations);
  }

  // 获取消息列表
  Future<List<Message>> getMessages(int conversationId, int page, int limit) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    // 根据会话ID过滤消息
    final conversation = _conversations.firstWhere(
      (c) => c.id == conversationId,
      orElse: () => throw Exception('会话不存在'),
    );

    final messages = _messages.where((m) => 
      (m.senderId == _currentUser!.id && m.receiverId == conversation.friend.id) ||
      (m.senderId == conversation.friend.id && m.receiverId == _currentUser!.id)
    ).toList();

    messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    return messages;
  }

  // 发送消息
  Future<Message> sendMessage(SendMessageRequest request) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final receiver = _users.firstWhere(
      (u) => u.id == request.receiverId,
      orElse: () => throw Exception('接收者不存在'),
    );

    final message = Message(
      id: _messages.length + 1,
      senderId: _currentUser!.id,
      receiverId: request.receiverId,
      content: request.content,
      type: request.type,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sender: _currentUser!,
      receiver: receiver,
    );

    _messages.add(message);

    // 更新或创建会话
    _updateConversation(message);

    // 模拟自动回复（仅用于演示）
    if (request.receiverId != _currentUser!.id) {
      Timer(const Duration(seconds: 2), () {
        _sendAutoReply(message);
      });
    }

    return message;
  }

  // 搜索用户
  Future<List<User>> searchUsers(String query, int limit) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final results = _users.where((user) =>
      user.username.toLowerCase().contains(query.toLowerCase()) ||
      (user.nickname?.toLowerCase().contains(query.toLowerCase()) ?? false)
    ).take(limit).toList();

    return results;
  }

  // 初始化模拟数据
  void _initializeMockData() {
    if (_conversations.isEmpty && _currentUser != null) {
      // 创建一些示例会话
      for (int i = 0; i < _users.length && i < 3; i++) {
        final friend = _users[i];
        if (friend.id != _currentUser!.id) {
          final conversation = Conversation(
            id: _conversations.length + 1,
            friend: friend,
            unreadCount: Random().nextInt(3),
            updatedAt: DateTime.now().subtract(Duration(hours: i)),
          );
          _conversations.add(conversation);

          // 添加一些示例消息
          _addSampleMessages(friend);
        }
      }
    }
  }

  // 添加示例消息
  void _addSampleMessages(User friend) {
    final sampleMessages = [
      '你好！',
      '最近怎么样？',
      '有空聊聊吗？',
      '今天天气不错呢',
      '周末有什么计划吗？',
    ];

    for (int i = 0; i < 3; i++) {
      final isFromFriend = i % 2 == 0;
      final message = Message(
        id: _messages.length + 1,
        senderId: isFromFriend ? friend.id : _currentUser!.id,
        receiverId: isFromFriend ? _currentUser!.id : friend.id,
        content: sampleMessages[Random().nextInt(sampleMessages.length)],
        type: MessageType.text,
        createdAt: DateTime.now().subtract(Duration(hours: 3 - i)),
        updatedAt: DateTime.now().subtract(Duration(hours: 3 - i)),
        sender: isFromFriend ? friend : _currentUser!,
        receiver: isFromFriend ? _currentUser! : friend,
      );
      _messages.add(message);
    }
  }

  // 更新会话
  void _updateConversation(Message message) {
    final existingIndex = _conversations.indexWhere((c) =>
      c.friend.id == message.receiverId || c.friend.id == message.senderId);

    if (existingIndex != -1) {
      final conversation = _conversations[existingIndex];
      final updatedConversation = Conversation(
        id: conversation.id,
        friend: conversation.friend,
        lastMessage: message,
        unreadCount: conversation.unreadCount + (message.senderId != _currentUser!.id ? 1 : 0),
        updatedAt: DateTime.now(),
      );
      _conversations[existingIndex] = updatedConversation;
    }
  }

  // 模拟自动回复
  void _sendAutoReply(Message originalMessage) {
    final autoReplies = [
      '收到！',
      '好的，明白了',
      '谢谢你的消息',
      '我稍后回复你',
      '👍',
      '哈哈，有趣',
      '确实如此',
    ];

    final reply = Message(
      id: _messages.length + 1,
      senderId: originalMessage.receiverId,
      receiverId: originalMessage.senderId,
      content: autoReplies[Random().nextInt(autoReplies.length)],
      type: MessageType.text,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sender: originalMessage.receiver,
      receiver: originalMessage.sender,
    );

    _messages.add(reply);
    _updateConversation(reply);
  }

  // 清除数据（用于登出）
  void clear() {
    _currentUser = null;
    _accessToken = null;
    _messages.clear();
    _conversations.clear();
  }

  // 检查是否已认证
  bool get isAuthenticated => _currentUser != null && _accessToken != null;

  // 模拟好友请求数据
  final List<FriendRequest> _friendRequests = [];

  // 发送好友请求
  Future<void> sendFriendRequest(int receiverId, String? message) async {
    await Future.delayed(const Duration(milliseconds: 300));

    if (_currentUser == null) {
      throw Exception('用户未登录');
    }

    final receiver = _users.firstWhere(
      (user) => user.id == receiverId,
      orElse: () => throw Exception('用户不存在'),
    );

    // 检查是否已经发送过请求
    final existingRequest = _friendRequests.any(
      (request) => request.senderId == _currentUser!.id && request.receiverId == receiverId,
    );

    if (existingRequest) {
      throw Exception('已经发送过好友请求');
    }

    final request = FriendRequest(
      id: _friendRequests.length + 1,
      senderId: _currentUser!.id,
      receiverId: receiverId,
      status: FriendRequestStatus.pending,
      message: message,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sender: _currentUser!,
      receiver: receiver,
    );

    _friendRequests.add(request);
  }

  // 获取好友请求列表
  Future<List<FriendRequest>> getFriendRequests() async {
    await Future.delayed(const Duration(milliseconds: 300));

    if (_currentUser == null) {
      throw Exception('用户未登录');
    }

    // 返回发给当前用户的好友请求
    return _friendRequests
        .where((request) => request.receiverId == _currentUser!.id)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // 接受好友请求
  Future<void> acceptFriendRequest(int requestId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final requestIndex = _friendRequests.indexWhere((request) => request.id == requestId);
    if (requestIndex == -1) {
      throw Exception('好友请求不存在');
    }

    final request = _friendRequests[requestIndex];
    if (request.receiverId != _currentUser?.id) {
      throw Exception('无权限操作此请求');
    }

    // 更新请求状态
    final updatedRequest = FriendRequest(
      id: request.id,
      senderId: request.senderId,
      receiverId: request.receiverId,
      status: FriendRequestStatus.accepted,
      message: request.message,
      createdAt: request.createdAt,
      updatedAt: DateTime.now(),
      sender: request.sender,
      receiver: request.receiver,
    );

    _friendRequests[requestIndex] = updatedRequest;

    // 这里可以添加创建好友关系的逻辑
  }

  // 拒绝好友请求
  Future<void> rejectFriendRequest(int requestId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final requestIndex = _friendRequests.indexWhere((request) => request.id == requestId);
    if (requestIndex == -1) {
      throw Exception('好友请求不存在');
    }

    final request = _friendRequests[requestIndex];
    if (request.receiverId != _currentUser?.id) {
      throw Exception('无权限操作此请求');
    }

    // 更新请求状态
    final updatedRequest = FriendRequest(
      id: request.id,
      senderId: request.senderId,
      receiverId: request.receiverId,
      status: FriendRequestStatus.rejected,
      message: request.message,
      createdAt: request.createdAt,
      updatedAt: DateTime.now(),
      sender: request.sender,
      receiver: request.receiver,
    );

    _friendRequests[requestIndex] = updatedRequest;
  }

  // 初始化一些模拟的好友请求数据
  void _initializeFriendRequests() {
    if (_currentUser == null || _friendRequests.isNotEmpty) return;

    // 添加一些模拟的好友请求
    final sampleRequests = [
      FriendRequest(
        id: 1,
        senderId: 2, // Bob
        receiverId: _currentUser!.id,
        status: FriendRequestStatus.pending,
        message: '你好，我想加你为好友！',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        sender: _users.firstWhere((u) => u.id == 2),
        receiver: _currentUser!,
      ),
      FriendRequest(
        id: 2,
        senderId: 3, // Charlie
        receiverId: _currentUser!.id,
        status: FriendRequestStatus.pending,
        message: '我们可以成为朋友吗？',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        sender: _users.firstWhere((u) => u.id == 3),
        receiver: _currentUser!,
      ),
    ];

    _friendRequests.addAll(sampleRequests);
  }
}
