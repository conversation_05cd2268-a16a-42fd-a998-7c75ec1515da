-- Merging decision tree log ---
manifest
ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml:1:1-3:12
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml:1:1-3:12
	package
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml:2:3-64
		INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml:1:11-69
uses-sdk
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml
INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\src\flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.28\android\src\main\AndroidManifest.xml
