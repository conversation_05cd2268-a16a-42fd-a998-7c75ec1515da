import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider extends ChangeNotifier {
  final ApiClient _apiClient = ApiClient();
  
  User? _currentUser;
  bool _isLoading = false;
  String? _error;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null && _apiClient.isAuthenticated;

  AuthProvider() {
    _loadStoredAuth();
  }

  Future<void> _loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token');
      final refreshToken = prefs.getString('refresh_token');
      final userJson = prefs.getString('user');

      if (accessToken != null && refreshToken != null && userJson != null) {
        _apiClient.setTokens(accessToken, refreshToken);
        // 这里可以解析用户信息，但为了简化，我们通过API获取
        await _getCurrentUser();
      }
    } catch (e) {
      print('Error loading stored auth: $e');
    }
  }

  Future<void> _getCurrentUser() async {
    try {
      final user = await _apiClient.api.getProfile();
      _currentUser = user;
      notifyListeners();
    } catch (e) {
      print('Error getting current user: $e');
      await logout();
    }
  }

  Future<bool> register({
    required String username,
    required String email,
    required String password,
    String? nickname,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = RegisterRequest(
        username: username,
        email: email,
        password: password,
        nickname: nickname,
      );

      final response = await _apiClient.api.register(request);
      
      _currentUser = response.user;
      _apiClient.setTokens(response.accessToken, response.refreshToken);
      
      await _storeAuth(response);
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> login({
    required String username,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = LoginRequest(
        username: username,
        password: password,
      );

      final response = await _apiClient.api.login(request);
      
      _currentUser = response.user;
      _apiClient.setTokens(response.accessToken, response.refreshToken);
      
      await _storeAuth(response);
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    _apiClient.clearTokens();
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    await prefs.remove('refresh_token');
    await prefs.remove('user');
    
    notifyListeners();
  }

  Future<bool> updateProfile({
    String? nickname,
    String? avatar,
    UserStatus? status,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      final request = <String, dynamic>{};
      if (nickname != null) request['nickname'] = nickname;
      if (avatar != null) request['avatar'] = avatar;
      if (status != null) request['status'] = status.name;

      final updatedUser = await _apiClient.api.updateProfile(request);
      _currentUser = updatedUser;
      
      // 更新存储的用户信息
      final prefs = await SharedPreferences.getInstance();
      // 这里需要实现用户信息的序列化存储
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Profile update failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> _storeAuth(LoginResponse response) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', response.accessToken);
    await prefs.setString('refresh_token', response.refreshToken);
    // 这里需要实现用户信息的序列化存储
    // await prefs.setString('user', jsonEncode(response.user.toJson()));
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  ApiClient get apiClient => _apiClient;
}
