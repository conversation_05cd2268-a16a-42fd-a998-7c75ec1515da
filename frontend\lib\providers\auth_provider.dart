import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import '../services/mock_api_service.dart';

class AuthProvider extends ChangeNotifier {
  final ApiClient _apiClient = ApiClient();
  final MockApiService _mockApi = MockApiService();

  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  final bool _useMockApi = true; // 开发模式下使用模拟API

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _useMockApi ? _mockApi.isAuthenticated : (_currentUser != null && _apiClient.isAuthenticated);

  AuthProvider() {
    _loadStoredAuth();
  }

  Future<void> _loadStoredAuth() async {
    // 在模拟模式下跳过本地存储
    if (_useMockApi) {
      return;
    }

    try {
      // 在真实环境中，这里会从SharedPreferences加载认证信息
      // final prefs = await SharedPreferences.getInstance();
      // final accessToken = prefs.getString('access_token');
      // final refreshToken = prefs.getString('refresh_token');
      // if (accessToken != null && refreshToken != null) {
      //   _apiClient.setTokens(accessToken, refreshToken);
      //   await _getCurrentUser();
      // }
    } catch (e) {
      print('Error loading stored auth: $e');
    }
  }

  Future<void> _getCurrentUser() async {
    try {
      final user = await _apiClient.api.getProfile();
      _currentUser = user;
      notifyListeners();
    } catch (e) {
      print('Error getting current user: $e');
      await logout();
    }
  }

  Future<bool> register({
    required String username,
    required String email,
    required String password,
    String? nickname,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      if (_useMockApi) {
        final request = RegisterRequest(
          username: username,
          email: email,
          password: password,
          nickname: nickname,
        );
        final response = await _mockApi.register(request);
        _currentUser = response.user;
      } else {
        final request = RegisterRequest(
          username: username,
          email: email,
          password: password,
          nickname: nickname,
        );
        final response = await _apiClient.api.register(request);
        _currentUser = response.user;
        _apiClient.setTokens(response.accessToken, response.refreshToken);
        await _storeAuth(response);
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> login({
    required String username,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      if (_useMockApi) {
        final response = await _mockApi.login(username, password);
        _currentUser = response.user;
      } else {
        final request = LoginRequest(
          username: username,
          password: password,
        );
        final response = await _apiClient.api.login(request);
        _currentUser = response.user;
        _apiClient.setTokens(response.accessToken, response.refreshToken);
        await _storeAuth(response);
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;

    if (_useMockApi) {
      _mockApi.clear();
    } else {
      _apiClient.clearTokens();
      // 清除本地存储（在真实环境中使用）
      // final prefs = await SharedPreferences.getInstance();
      // await prefs.remove('access_token');
      // await prefs.remove('refresh_token');
      // await prefs.remove('user');
    }

    notifyListeners();
  }

  Future<bool> updateProfile({
    String? nickname,
    String? avatar,
    UserStatus? status,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      if (_useMockApi) {
        // 在模拟模式下，直接更新本地用户信息
        _currentUser = _currentUser!.copyWith(
          nickname: nickname,
          avatar: avatar,
          status: status,
        );
      } else {
        final request = <String, dynamic>{};
        if (nickname != null) request['nickname'] = nickname;
        if (avatar != null) request['avatar'] = avatar;
        if (status != null) request['status'] = status.name;

        final updatedUser = await _apiClient.api.updateProfile(request);
        _currentUser = updatedUser;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Profile update failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> _storeAuth(LoginResponse response) async {
    // 在真实环境中存储认证信息
    // final prefs = await SharedPreferences.getInstance();
    // await prefs.setString('access_token', response.accessToken);
    // await prefs.setString('refresh_token', response.refreshToken);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  ApiClient get apiClient => _apiClient;
}
