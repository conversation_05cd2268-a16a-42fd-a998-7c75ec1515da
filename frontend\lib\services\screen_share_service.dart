import 'dart:async';
import 'dart:typed_data';
import '../models/user.dart';

/// 屏幕共享服务
/// 处理屏幕共享、远程协作等功能
class ScreenShareService {
  static final ScreenShareService _instance = ScreenShareService._internal();
  factory ScreenShareService() => _instance;
  ScreenShareService._internal();

  // 共享状态
  bool _isSharing = false;
  bool _isViewing = false;
  ScreenShareSession? _currentSession;
  
  // 事件流
  final StreamController<ScreenShareEvent> _eventController = 
      StreamController<ScreenShareEvent>.broadcast();
  
  Stream<ScreenShareEvent> get eventStream => _eventController.stream;

  /// 获取当前共享状态
  bool get isSharing => _isSharing;

  /// 获取当前观看状态
  bool get isViewing => _isViewing;

  /// 获取当前会话
  ScreenShareSession? get currentSession => _currentSession;

  /// 开始屏幕共享
  Future<bool> startScreenShare(User targetUser) async {
    if (_isSharing || _isViewing) {
      return false;
    }

    try {
      _isSharing = true;
      _currentSession = ScreenShareSession(
        id: _generateSessionId(),
        sharer: User(id: 1, username: 'current_user', email: '<EMAIL>'),
        viewer: targetUser,
        startTime: DateTime.now(),
        status: ScreenShareStatus.starting,
        quality: ScreenShareQuality.high,
        allowControl: false,
      );

      _eventController.add(ScreenShareEvent(
        type: ScreenShareEventType.shareStarted,
        session: _currentSession!,
      ));

      // 模拟启动过程
      await Future.delayed(const Duration(seconds: 2));

      _currentSession = _currentSession!.copyWith(
        status: ScreenShareStatus.active,
      );

      _eventController.add(ScreenShareEvent(
        type: ScreenShareEventType.shareActive,
        session: _currentSession!,
      ));

      return true;
    } catch (e) {
      _isSharing = false;
      _currentSession = null;
      
      _eventController.add(ScreenShareEvent(
        type: ScreenShareEventType.shareError,
        error: e.toString(),
      ));

      return false;
    }
  }

  /// 停止屏幕共享
  Future<void> stopScreenShare() async {
    if (!_isSharing || _currentSession == null) return;

    _isSharing = false;
    _currentSession = _currentSession!.copyWith(
      status: ScreenShareStatus.stopped,
      endTime: DateTime.now(),
    );

    _eventController.add(ScreenShareEvent(
      type: ScreenShareEventType.shareStopped,
      session: _currentSession!,
    ));

    _currentSession = null;
  }

  /// 开始观看屏幕共享
  Future<bool> startViewing(ScreenShareSession session) async {
    if (_isSharing || _isViewing) {
      return false;
    }

    try {
      _isViewing = true;
      _currentSession = session.copyWith(
        status: ScreenShareStatus.active,
      );

      _eventController.add(ScreenShareEvent(
        type: ScreenShareEventType.viewingStarted,
        session: _currentSession!,
      ));

      return true;
    } catch (e) {
      _isViewing = false;
      _currentSession = null;
      
      _eventController.add(ScreenShareEvent(
        type: ScreenShareEventType.viewingError,
        error: e.toString(),
      ));

      return false;
    }
  }

  /// 停止观看屏幕共享
  Future<void> stopViewing() async {
    if (!_isViewing || _currentSession == null) return;

    _isViewing = false;
    
    _eventController.add(ScreenShareEvent(
      type: ScreenShareEventType.viewingStopped,
      session: _currentSession!,
    ));

    _currentSession = null;
  }

  /// 切换画质
  Future<void> changeQuality(ScreenShareQuality quality) async {
    if (_currentSession == null) return;

    _currentSession = _currentSession!.copyWith(quality: quality);
    
    _eventController.add(ScreenShareEvent(
      type: ScreenShareEventType.qualityChanged,
      session: _currentSession!,
    ));
  }

  /// 切换远程控制权限
  Future<void> toggleRemoteControl() async {
    if (_currentSession == null || !_isSharing) return;

    _currentSession = _currentSession!.copyWith(
      allowControl: !_currentSession!.allowControl,
    );
    
    _eventController.add(ScreenShareEvent(
      type: ScreenShareEventType.controlToggled,
      session: _currentSession!,
    ));
  }

  /// 发送远程控制事件
  Future<void> sendControlEvent(ControlEvent event) async {
    if (_currentSession == null || !_currentSession!.allowControl || !_isViewing) {
      return;
    }

    _eventController.add(ScreenShareEvent(
      type: ScreenShareEventType.controlEvent,
      session: _currentSession!,
      controlEvent: event,
    ));
  }

  /// 模拟接收屏幕共享邀请
  void simulateShareInvitation(User sharer) {
    final session = ScreenShareSession(
      id: _generateSessionId(),
      sharer: sharer,
      viewer: User(id: 1, username: 'current_user', email: '<EMAIL>'),
      startTime: DateTime.now(),
      status: ScreenShareStatus.invitation,
      quality: ScreenShareQuality.medium,
      allowControl: false,
    );

    _eventController.add(ScreenShareEvent(
      type: ScreenShareEventType.shareInvitation,
      session: session,
    ));
  }

  /// 获取屏幕截图（模拟）
  Future<Uint8List?> captureScreen() async {
    if (!_isSharing) return null;

    try {
      // 模拟截图延迟
      await Future.delayed(const Duration(milliseconds: 100));
      
      // 返回模拟的图片数据
      return Uint8List.fromList([0xFF, 0xD8, 0xFF, 0xE0]); // JPEG header
    } catch (e) {
      return null;
    }
  }

  String _generateSessionId() {
    return 'screen_${DateTime.now().millisecondsSinceEpoch}';
  }

  void dispose() {
    _eventController.close();
  }
}

/// 屏幕共享状态
enum ScreenShareStatus {
  invitation,
  starting,
  active,
  paused,
  stopped,
  error,
}

/// 屏幕共享画质
enum ScreenShareQuality {
  low,
  medium,
  high,
  ultra,
}

/// 屏幕共享会话
class ScreenShareSession {
  final String id;
  final User sharer;
  final User viewer;
  final DateTime startTime;
  final DateTime? endTime;
  final ScreenShareStatus status;
  final ScreenShareQuality quality;
  final bool allowControl;

  ScreenShareSession({
    required this.id,
    required this.sharer,
    required this.viewer,
    required this.startTime,
    this.endTime,
    required this.status,
    required this.quality,
    required this.allowControl,
  });

  ScreenShareSession copyWith({
    String? id,
    User? sharer,
    User? viewer,
    DateTime? startTime,
    DateTime? endTime,
    ScreenShareStatus? status,
    ScreenShareQuality? quality,
    bool? allowControl,
  }) {
    return ScreenShareSession(
      id: id ?? this.id,
      sharer: sharer ?? this.sharer,
      viewer: viewer ?? this.viewer,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      quality: quality ?? this.quality,
      allowControl: allowControl ?? this.allowControl,
    );
  }

  /// 获取共享时长
  Duration get duration {
    final endTime = this.endTime ?? DateTime.now();
    return endTime.difference(startTime);
  }

  String get qualityText {
    switch (quality) {
      case ScreenShareQuality.low:
        return '流畅';
      case ScreenShareQuality.medium:
        return '标清';
      case ScreenShareQuality.high:
        return '高清';
      case ScreenShareQuality.ultra:
        return '超清';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sharer': sharer.toJson(),
      'viewer': viewer.toJson(),
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'status': status.index,
      'quality': quality.index,
      'allowControl': allowControl,
    };
  }
}

/// 屏幕共享事件
class ScreenShareEvent {
  final ScreenShareEventType type;
  final ScreenShareSession? session;
  final String? error;
  final ControlEvent? controlEvent;

  ScreenShareEvent({
    required this.type,
    this.session,
    this.error,
    this.controlEvent,
  });
}

/// 屏幕共享事件类型
enum ScreenShareEventType {
  shareInvitation,
  shareStarted,
  shareActive,
  shareStopped,
  shareError,
  viewingStarted,
  viewingStopped,
  viewingError,
  qualityChanged,
  controlToggled,
  controlEvent,
}

/// 远程控制事件
class ControlEvent {
  final ControlEventType type;
  final double? x;
  final double? y;
  final String? text;
  final int? keyCode;

  ControlEvent({
    required this.type,
    this.x,
    this.y,
    this.text,
    this.keyCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.index,
      'x': x,
      'y': y,
      'text': text,
      'keyCode': keyCode,
    };
  }
}

/// 远程控制事件类型
enum ControlEventType {
  mouseMove,
  mouseClick,
  mouseDoubleClick,
  mouseRightClick,
  keyPress,
  textInput,
  scroll,
}
