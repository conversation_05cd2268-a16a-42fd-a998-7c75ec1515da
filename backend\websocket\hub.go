package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"

	"aqichat-backend/models"
	"aqichat-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源，生产环境中应该更严格
	},
}

type Client struct {
	hub    *Hub
	conn   *websocket.Conn
	send   chan models.WSMessage
	userID uint
}

type Hub struct {
	clients    map[*Client]bool
	userClients map[uint]*Client // 用户ID到客户端的映射
	register   chan *Client
	unregister chan *Client
	broadcast  chan models.WSMessage
	mutex      sync.RWMutex
}

func NewHub() *Hub {
	return &Hub{
		clients:     make(map[*Client]bool),
		userClients: make(map[uint]*Client),
		register:    make(chan *Client),
		unregister:  make(chan *Client),
		broadcast:   make(chan models.WSMessage),
	}
}

func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.userClients[client.userID] = client
			h.mutex.Unlock()
			
			log.Printf("Client connected: user %d", client.userID)
			
			// 通知其他用户该用户上线
			h.broadcastUserStatus(client.userID, models.StatusOnline)

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				delete(h.userClients, client.userID)
				close(client.send)
			}
			h.mutex.Unlock()
			
			log.Printf("Client disconnected: user %d", client.userID)
			
			// 通知其他用户该用户下线
			h.broadcastUserStatus(client.userID, models.StatusOffline)

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
					delete(h.userClients, client.userID)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

func (h *Hub) SendToUser(userID uint, message models.WSMessage) {
	h.mutex.RLock()
	client, exists := h.userClients[userID]
	h.mutex.RUnlock()
	
	if exists {
		select {
		case client.send <- message:
		default:
			// 客户端发送缓冲区满，关闭连接
			h.mutex.Lock()
			delete(h.clients, client)
			delete(h.userClients, userID)
			close(client.send)
			h.mutex.Unlock()
		}
	}
}

func (h *Hub) broadcastUserStatus(userID uint, status models.UserStatus) {
	message := models.WSMessage{
		Type: models.WSMessageTypeUserOnline,
		Data: map[string]interface{}{
			"user_id": userID,
			"status":  status,
		},
	}
	
	if status == models.StatusOffline {
		message.Type = models.WSMessageTypeUserOffline
	}
	
	h.broadcast <- message
}

func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(512)
	
	for {
		_, messageBytes, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		var wsMessage models.WSMessage
		if err := json.Unmarshal(messageBytes, &wsMessage); err != nil {
			log.Printf("Error unmarshaling message: %v", err)
			continue
		}

		// 处理不同类型的消息
		switch wsMessage.Type {
		case models.WSMessageTypeTyping:
			// 转发打字状态
			if data, ok := wsMessage.Data.(map[string]interface{}); ok {
				if receiverID, ok := data["receiver_id"].(float64); ok {
					c.hub.SendToUser(uint(receiverID), wsMessage)
				}
			}
		case models.WSMessageTypeMessage:
			// 这里可以处理实时消息发送，但通常通过HTTP API处理
			log.Printf("Received message via WebSocket from user %d", c.userID)
		}
	}
}

func (c *Client) writePump() {
	defer c.conn.Close()

	for {
		select {
		case message, ok := <-c.send:
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			messageBytes, err := json.Marshal(message)
			if err != nil {
				log.Printf("Error marshaling message: %v", err)
				continue
			}

			if err := c.conn.WriteMessage(websocket.TextMessage, messageBytes); err != nil {
				log.Printf("Error writing message: %v", err)
				return
			}
		}
	}
}

type WebSocketHandler struct {
	hub *Hub
}

func NewWebSocketHandler(hub *Hub) *WebSocketHandler {
	return &WebSocketHandler{hub: hub}
}

func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// 从查询参数获取token
	token := c.Query("token")
	if token == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Token required"})
		return
	}

	// 验证token
	claims, err := utils.ValidateToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	// 创建客户端
	client := &Client{
		hub:    h.hub,
		conn:   conn,
		send:   make(chan models.WSMessage, 256),
		userID: claims.UserID,
	}

	// 注册客户端
	client.hub.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}
