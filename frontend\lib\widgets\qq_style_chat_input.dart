import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../models/emoji.dart';
import '../models/user.dart';
import 'emoji_picker.dart';
import 'camera_interface.dart';

enum VoiceMode {
  walkie, // 对讲模式
  record, // 录音模式
}

enum VoiceRecordState {
  idle,
  recording,
  recorded,
}

class QQStyleChatInput extends StatefulWidget {
  final Function(String) onSendMessage;
  final Function(String) onSendVoice;
  final Function(XFile) onSendImage;
  final Function(XFile) onSendVideo;
  final Function() onStartVoiceCall;
  final Function() onStartVideoCall;
  final Function() onSendLocation;
  final Function(User) onSendContact;

  const QQStyleChatInput({
    Key? key,
    required this.onSendMessage,
    required this.onSendVoice,
    required this.onSendImage,
    required this.onSendVideo,
    required this.onStartVoiceCall,
    required this.onStartVideoCall,
    required this.onSendLocation,
    required this.onSendContact,
  }) : super(key: key);

  @override
  State<QQStyleChatInput> createState() => _QQStyleChatInputState();
}

class _QQStyleChatInputState extends State<QQStyleChatInput>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();
  
  bool _showVoiceInterface = false;
  bool _showEmojiPicker = false;
  bool _showMoreMenu = false;
  
  VoiceMode _voiceMode = VoiceMode.walkie;
  VoiceRecordState _recordState = VoiceRecordState.idle;
  
  bool _isRecording = false;
  bool _showVoiceGesture = false;
  double _gestureStartY = 0.0;
  double _currentGestureY = 0.0;
  double _gestureStartX = 0.0;
  double _currentGestureX = 0.0;
  String _gestureHint = '';
  
  late AnimationController _recordingAnimationController;
  late Animation<double> _recordingAnimation;
  
  // 手势阈值
  static const double _verticalThreshold = 80.0;
  static const double _horizontalThreshold = 100.0;

  @override
  void initState() {
    super.initState();
    _recordingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _recordingAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _recordingAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    _recordingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[300]!, width: 0.5),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_showVoiceInterface)
            _buildVoiceInterface()
          else
            _buildNormalInterface(),
        ],
      ),
    );
  }

  Widget _buildNormalInterface() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 第一排：文字输入框 + 发送按钮
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              // 文字输入框
              Expanded(
                child: Container(
                  constraints: const BoxConstraints(
                    minHeight: 36,
                    maxHeight: 100,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: TextField(
                    controller: _textController,
                    maxLines: null,
                    decoration: const InputDecoration(
                      hintText: '请输入消息...',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    onChanged: (text) {
                      setState(() {});
                    },
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 发送按钮
              GestureDetector(
                onTap: _textController.text.trim().isNotEmpty ? _sendTextMessage : null,
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: _textController.text.trim().isNotEmpty 
                        ? AppConstants.primaryColor 
                        : Colors.grey[300],
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: const Icon(
                    Icons.send,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // 第二排：六个功能按钮
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFunctionButton(
                icon: Icons.keyboard_voice,
                label: '语音',
                onTap: () => setState(() => _showVoiceInterface = true),
              ),
              _buildFunctionButton(
                icon: Icons.photo_library,
                label: '相册',
                onTap: _pickImageFromGallery,
              ),
              _buildFunctionButton(
                icon: Icons.camera_alt,
                label: '拍照',
                onTap: _takePicture,
              ),
              _buildFunctionButton(
                icon: Icons.videocam,
                label: '录像',
                onTap: _recordVideo,
              ),
              _buildFunctionButton(
                icon: Icons.emoji_emotions,
                label: '表情',
                onTap: _toggleEmojiPicker,
              ),
              _buildFunctionButton(
                icon: Icons.add_circle_outline,
                label: '更多',
                onTap: _toggleMoreMenu,
              ),
            ],
          ),
        ),
        
        // 表情选择器
        if (_showEmojiPicker)
          SizedBox(
            height: 250,
            child: EmojiPicker(
              onEmojiSelected: (emoji) {
                _textController.text += emoji;
              },
              onStickerSelected: (sticker) {
                widget.onSendMessage('[贴纸:${sticker.name}]');
              },
            ),
          ),
        
        // 更多功能菜单
        if (_showMoreMenu)
          _buildMoreMenuPanel(),
      ],
    );
  }

  Widget _buildFunctionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Icon(
              icon,
              color: Colors.grey[700],
              size: 22,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceInterface() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部关闭按钮
          Row(
            children: [
              GestureDetector(
                onTap: () => setState(() => _showVoiceInterface = false),
                child: const Icon(Icons.close, color: Colors.grey),
              ),
              const Expanded(
                child: Center(
                  child: Text(
                    '语音输入',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 24), // 占位，保持居中
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 大按钮区域
          _buildVoiceBigButton(),
          
          const SizedBox(height: 20),

          // 录音完成后的操作按钮
          if (_voiceMode == VoiceMode.record && _recordState == VoiceRecordState.recorded)
            _buildRecordActionButtons()
          else
            // 底部模式切换按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildModeButton(
                  label: '对讲',
                  isSelected: _voiceMode == VoiceMode.walkie,
                  onTap: () => setState(() => _voiceMode = VoiceMode.walkie),
                ),
                _buildModeButton(
                  label: '录音',
                  isSelected: _voiceMode == VoiceMode.record,
                  onTap: () => setState(() => _voiceMode = VoiceMode.record),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildVoiceBigButton() {
    if (_voiceMode == VoiceMode.walkie) {
      return _buildWalkieButton();
    } else {
      return _buildRecordButton();
    }
  }

  Widget _buildWalkieButton() {
    return Stack(
      children: [
        GestureDetector(
          onLongPressStart: _startWalkieRecording,
          onLongPressEnd: _stopWalkieRecording,
          onLongPressCancel: _cancelWalkieRecording,
          onLongPressMoveUpdate: _updateWalkieGesture,
          child: AnimatedBuilder(
            animation: _recordingAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isRecording ? _recordingAnimation.value : 1.0,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: _isRecording ? Colors.red[100] : Colors.grey[100],
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _isRecording ? Colors.red : Colors.grey[300]!,
                      width: 3,
                    ),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.mic,
                          size: 60,
                          color: _isRecording ? Colors.red : Colors.grey[600],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _isRecording ? '正在录音...' : '按住说话',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: _isRecording ? Colors.red : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // 手势提示覆盖层
        if (_showVoiceGesture)
          _buildVoiceGestureOverlay(),
      ],
    );
  }

  Widget _buildRecordButton() {
    return GestureDetector(
      onTap: _handleRecordButtonTap,
      child: AnimatedBuilder(
        animation: _recordingAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isRecording ? _recordingAnimation.value : 1.0,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: _getRecordButtonColor(),
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getRecordButtonBorderColor(),
                  width: 3,
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _getRecordButtonIcon(),
                      size: 60,
                      color: _getRecordButtonIconColor(),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getRecordButtonText(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _getRecordButtonIconColor(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecordActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 取消按钮
        GestureDetector(
          onTap: _cancelRecording,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: const Text(
              '取消',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ),
        ),

        // 发送按钮
        GestureDetector(
          onTap: _sendRecording,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: AppConstants.primaryColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              '发送',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModeButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppConstants.primaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppConstants.primaryColor : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isSelected ? Colors.white : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  // 录音按钮相关方法
  void _handleRecordButtonTap() {
    switch (_recordState) {
      case VoiceRecordState.idle:
        _startRecording();
        break;
      case VoiceRecordState.recording:
        _stopRecording();
        break;
      case VoiceRecordState.recorded:
        // 已录制状态下点击无效
        break;
    }
  }

  Color _getRecordButtonColor() {
    switch (_recordState) {
      case VoiceRecordState.idle:
        return Colors.grey[100]!;
      case VoiceRecordState.recording:
        return Colors.red[100]!;
      case VoiceRecordState.recorded:
        return Colors.green[100]!;
    }
  }

  Color _getRecordButtonBorderColor() {
    switch (_recordState) {
      case VoiceRecordState.idle:
        return Colors.grey[300]!;
      case VoiceRecordState.recording:
        return Colors.red;
      case VoiceRecordState.recorded:
        return Colors.green;
    }
  }

  IconData _getRecordButtonIcon() {
    switch (_recordState) {
      case VoiceRecordState.idle:
        return Icons.mic;
      case VoiceRecordState.recording:
        return Icons.stop;
      case VoiceRecordState.recorded:
        return Icons.check;
    }
  }

  Color _getRecordButtonIconColor() {
    switch (_recordState) {
      case VoiceRecordState.idle:
        return Colors.grey[600]!;
      case VoiceRecordState.recording:
        return Colors.red;
      case VoiceRecordState.recorded:
        return Colors.green;
    }
  }

  String _getRecordButtonText() {
    switch (_recordState) {
      case VoiceRecordState.idle:
        return '点击录音';
      case VoiceRecordState.recording:
        return '点击停止';
      case VoiceRecordState.recorded:
        return '录音完成';
    }
  }

  // 对讲模式手势处理
  void _startWalkieRecording(LongPressStartDetails details) {
    setState(() {
      _isRecording = true;
      _showVoiceGesture = false;
      _gestureStartY = details.globalPosition.dy;
      _currentGestureY = details.globalPosition.dy;
      _gestureStartX = details.globalPosition.dx;
      _currentGestureX = details.globalPosition.dx;
      _gestureHint = '';
    });
    _recordingAnimationController.repeat(reverse: true);
  }

  void _updateWalkieGesture(LongPressMoveUpdateDetails details) {
    if (!_isRecording) return;

    setState(() {
      _currentGestureY = details.globalPosition.dy;
      _currentGestureX = details.globalPosition.dx;

      final deltaY = _gestureStartY - _currentGestureY;
      final deltaX = _currentGestureX - _gestureStartX;

      if (deltaY > _verticalThreshold) {
        _showVoiceGesture = true;

        if (deltaX < -_horizontalThreshold) {
          _gestureHint = '松开取消录音';
        } else if (deltaX > _horizontalThreshold) {
          _gestureHint = '松开转换文字';
        } else {
          _gestureHint = '松开发送语音';
        }
      } else {
        _showVoiceGesture = false;
        _gestureHint = '';
      }
    });
  }

  void _stopWalkieRecording(LongPressEndDetails details) {
    final deltaY = _gestureStartY - _currentGestureY;
    final deltaX = _currentGestureX - _gestureStartX;

    setState(() {
      _isRecording = false;
      _showVoiceGesture = false;
    });
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();

    if (deltaY > _verticalThreshold) {
      if (deltaX < -_horizontalThreshold) {
        // 左滑取消
        _showMessage('录音已取消');
      } else if (deltaX > _horizontalThreshold) {
        // 右滑转文字
        _convertVoiceToText();
      } else {
        // 上滑发送
        _sendVoiceMessage();
      }
    } else {
      // 正常松开发送
      _sendVoiceMessage();
    }
  }

  void _cancelWalkieRecording() {
    setState(() {
      _isRecording = false;
      _showVoiceGesture = false;
    });
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();
    _showMessage('录音已取消');
  }

  // 录音模式处理
  void _startRecording() {
    setState(() {
      _recordState = VoiceRecordState.recording;
      _isRecording = true;
    });
    _recordingAnimationController.repeat(reverse: true);
  }

  void _stopRecording() {
    setState(() {
      _recordState = VoiceRecordState.recorded;
      _isRecording = false;
    });
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();
  }

  void _cancelRecording() {
    setState(() {
      _recordState = VoiceRecordState.idle;
      _isRecording = false;
    });
    _recordingAnimationController.stop();
    _recordingAnimationController.reset();
    _showMessage('录音已取消');
  }

  void _sendRecording() {
    widget.onSendVoice('voice_message_path');
    setState(() {
      _recordState = VoiceRecordState.idle;
    });
    _showMessage('语音消息已发送');
  }

  void _sendVoiceMessage() {
    widget.onSendVoice('voice_message_path');
    _showMessage('语音消息已发送');
  }

  void _convertVoiceToText() {
    const convertedText = '这是语音转换的文字内容';
    _textController.text = convertedText;
    setState(() {
      _showVoiceInterface = false;
    });
    _showMessage('语音已转换为文字');
  }

  void _sendTextMessage() {
    if (_textController.text.trim().isNotEmpty) {
      widget.onSendMessage(_textController.text.trim());
      _textController.clear();
      setState(() {});
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildVoiceGestureOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          shape: BoxShape.circle,
        ),
        child: Stack(
          children: [
            // 手势区域指示
            Positioned(
              top: -60,
              left: -100,
              right: -100,
              child: Container(
                height: 60,
                child: Row(
                  children: [
                    // 左侧取消区域
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: _gestureHint.contains('取消')
                              ? Colors.red.withValues(alpha: 0.3)
                              : Colors.transparent,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(30),
                            bottomLeft: Radius.circular(30),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.cancel,
                              color: _gestureHint.contains('取消')
                                  ? Colors.red
                                  : Colors.red.withValues(alpha: 0.5),
                              size: 20,
                            ),
                            Text(
                              '取消',
                              style: TextStyle(
                                color: _gestureHint.contains('取消')
                                    ? Colors.red
                                    : Colors.red.withValues(alpha: 0.5),
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 中间发送区域
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: _gestureHint.contains('发送')
                              ? Colors.green.withValues(alpha: 0.3)
                              : Colors.transparent,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.send,
                              color: _gestureHint.contains('发送')
                                  ? Colors.green
                                  : Colors.green.withValues(alpha: 0.5),
                              size: 20,
                            ),
                            Text(
                              '发送',
                              style: TextStyle(
                                color: _gestureHint.contains('发送')
                                    ? Colors.green
                                    : Colors.green.withValues(alpha: 0.5),
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 右侧转文字区域
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: _gestureHint.contains('转换')
                              ? Colors.blue.withValues(alpha: 0.3)
                              : Colors.transparent,
                          borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(30),
                            bottomRight: Radius.circular(30),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.text_fields,
                              color: _gestureHint.contains('转换')
                                  ? Colors.blue
                                  : Colors.blue.withValues(alpha: 0.5),
                              size: 20,
                            ),
                            Text(
                              '转文字',
                              style: TextStyle(
                                color: _gestureHint.contains('转换')
                                    ? Colors.blue
                                    : Colors.blue.withValues(alpha: 0.5),
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 中心提示
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _gestureHint,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '上滑选择操作',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleEmojiPicker() {
    setState(() {
      _showEmojiPicker = !_showEmojiPicker;
      _showMoreMenu = false;
    });
  }

  void _toggleMoreMenu() {
    setState(() {
      _showMoreMenu = !_showMoreMenu;
      _showEmojiPicker = false;
    });
  }

  Widget _buildMoreMenuPanel() {
    return Container(
      height: 200,
      color: Colors.white,
      child: GridView.count(
        crossAxisCount: 4,
        padding: const EdgeInsets.all(16),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        children: [
          _buildMoreMenuItem(
            icon: Icons.phone,
            label: '语音通话',
            color: Colors.green,
            onTap: widget.onStartVoiceCall,
          ),
          _buildMoreMenuItem(
            icon: Icons.video_call,
            label: '视频通话',
            color: Colors.blue,
            onTap: widget.onStartVideoCall,
          ),
          _buildMoreMenuItem(
            icon: Icons.location_on,
            label: '位置',
            color: Colors.red,
            onTap: widget.onSendLocation,
          ),
          _buildMoreMenuItem(
            icon: Icons.attach_file,
            label: '文件',
            color: Colors.orange,
            onTap: _pickFile,
          ),
          _buildMoreMenuItem(
            icon: Icons.star,
            label: '收藏',
            color: Colors.amber,
            onTap: _showFavorites,
          ),
          _buildMoreMenuItem(
            icon: Icons.screen_share,
            label: '屏幕分享',
            color: Colors.purple,
            onTap: _startScreenShare,
          ),
          _buildMoreMenuItem(
            icon: Icons.person,
            label: '名片',
            color: Colors.indigo,
            onTap: _showContactPicker,
          ),
        ],
      ),
    );
  }

  Widget _buildMoreMenuItem({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // 功能方法
  void _pickImageFromGallery() async {
    final XFile? image = await _imagePicker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      widget.onSendImage(image);
    }
  }

  void _takePicture() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CameraInterface(
          isVideoMode: false,
          onMediaCaptured: widget.onSendImage,
        ),
      ),
    );
  }

  void _recordVideo() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CameraInterface(
          isVideoMode: true,
          onMediaCaptured: widget.onSendVideo,
        ),
      ),
    );
  }

  void _pickFile() {
    _showMessage('文件选择功能即将推出');
  }

  void _showFavorites() {
    _showMessage('收藏功能即将推出');
  }

  void _startScreenShare() {
    _showMessage('屏幕分享功能即将推出');
  }

  void _showContactPicker() {
    _showMessage('名片分享功能即将推出');
  }
}
